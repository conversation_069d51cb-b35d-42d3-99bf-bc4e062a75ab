# This file is generated by running "yarn install" inside your project.
# Manual changes might be lost - proceed with caution!

__metadata:
  version: 6
  cacheKey: 8

"@alloc/quick-lru@npm:^5.2.0":
  version: 5.2.0
  resolution: "@alloc/quick-lru@npm:5.2.0"
  checksum: bdc35758b552bcf045733ac047fb7f9a07c4678b944c641adfbd41f798b4b91fffd0fdc0df2578d9b0afc7b4d636aa6e110ead5d6281a2adc1ab90efd7f057f8
  languageName: node
  linkType: hard

"@ampproject/remapping@npm:^2.2.0":
  version: 2.3.0
  resolution: "@ampproject/remapping@npm:2.3.0"
  dependencies:
    "@jridgewell/gen-mapping": ^0.3.5
    "@jridgewell/trace-mapping": ^0.3.24
  checksum: d3ad7b89d973df059c4e8e6d7c972cbeb1bb2f18f002a3bd04ae0707da214cb06cc06929b65aa2313b9347463df2914772298bae8b1d7973f246bb3f2ab3e8f0
  languageName: node
  linkType: hard

"@antfu/utils@npm:^0.7.10, @antfu/utils@npm:^0.7.6":
  version: 0.7.10
  resolution: "@antfu/utils@npm:0.7.10"
  checksum: b93dd9e2c7e96ae6dca8a07c1fc5e7165ea9c7a89e78ecb75959bc9a8e769d3f565aea1b5c43db7374dd1f405cc277b6d14d85f884886f9d424dd6144d9203f2
  languageName: node
  linkType: hard

"@babel/code-frame@npm:^7.24.7, @babel/code-frame@npm:^7.25.9, @babel/code-frame@npm:^7.26.0, @babel/code-frame@npm:^7.26.2":
  version: 7.26.2
  resolution: "@babel/code-frame@npm:7.26.2"
  dependencies:
    "@babel/helper-validator-identifier": ^7.25.9
    js-tokens: ^4.0.0
    picocolors: ^1.0.0
  checksum: db13f5c42d54b76c1480916485e6900748bbcb0014a8aca87f50a091f70ff4e0d0a6db63cade75eb41fcc3d2b6ba0a7f89e343def4f96f00269b41b8ab8dd7b8
  languageName: node
  linkType: hard

"@babel/compat-data@npm:^7.25.9":
  version: 7.26.3
  resolution: "@babel/compat-data@npm:7.26.3"
  checksum: 85c5a9fb365231688c7faeb977f1d659da1c039e17b416f8ef11733f7aebe11fe330dce20c1844cacf243766c1d643d011df1d13cac9eda36c46c6c475693d21
  languageName: node
  linkType: hard

"@babel/core@npm:^7.23.3":
  version: 7.26.0
  resolution: "@babel/core@npm:7.26.0"
  dependencies:
    "@ampproject/remapping": ^2.2.0
    "@babel/code-frame": ^7.26.0
    "@babel/generator": ^7.26.0
    "@babel/helper-compilation-targets": ^7.25.9
    "@babel/helper-module-transforms": ^7.26.0
    "@babel/helpers": ^7.26.0
    "@babel/parser": ^7.26.0
    "@babel/template": ^7.25.9
    "@babel/traverse": ^7.25.9
    "@babel/types": ^7.26.0
    convert-source-map: ^2.0.0
    debug: ^4.1.0
    gensync: ^1.0.0-beta.2
    json5: ^2.2.3
    semver: ^6.3.1
  checksum: b296084cfd818bed8079526af93b5dfa0ba70282532d2132caf71d4060ab190ba26d3184832a45accd82c3c54016985a4109ab9118674347a7e5e9bc464894e6
  languageName: node
  linkType: hard

"@babel/generator@npm:^7.26.0, @babel/generator@npm:^7.26.3":
  version: 7.26.3
  resolution: "@babel/generator@npm:7.26.3"
  dependencies:
    "@babel/parser": ^7.26.3
    "@babel/types": ^7.26.3
    "@jridgewell/gen-mapping": ^0.3.5
    "@jridgewell/trace-mapping": ^0.3.25
    jsesc: ^3.0.2
  checksum: fb09fa55c66f272badf71c20a3a2cee0fa1a447fed32d1b84f16a668a42aff3e5f5ddc6ed5d832dda1e952187c002ca1a5cdd827022efe591b6ac44cada884ea
  languageName: node
  linkType: hard

"@babel/helper-annotate-as-pure@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/helper-annotate-as-pure@npm:7.25.9"
  dependencies:
    "@babel/types": ^7.25.9
  checksum: 41edda10df1ae106a9b4fe617bf7c6df77db992992afd46192534f5cff29f9e49a303231733782dd65c5f9409714a529f215325569f14282046e9d3b7a1ffb6c
  languageName: node
  linkType: hard

"@babel/helper-compilation-targets@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/helper-compilation-targets@npm:7.25.9"
  dependencies:
    "@babel/compat-data": ^7.25.9
    "@babel/helper-validator-option": ^7.25.9
    browserslist: ^4.24.0
    lru-cache: ^5.1.1
    semver: ^6.3.1
  checksum: 3af536e2db358b38f968abdf7d512d425d1018fef2f485d6f131a57a7bcaed32c606b4e148bb230e1508fa42b5b2ac281855a68eb78270f54698c48a83201b9b
  languageName: node
  linkType: hard

"@babel/helper-create-class-features-plugin@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/helper-create-class-features-plugin@npm:7.25.9"
  dependencies:
    "@babel/helper-annotate-as-pure": ^7.25.9
    "@babel/helper-member-expression-to-functions": ^7.25.9
    "@babel/helper-optimise-call-expression": ^7.25.9
    "@babel/helper-replace-supers": ^7.25.9
    "@babel/helper-skip-transparent-expression-wrappers": ^7.25.9
    "@babel/traverse": ^7.25.9
    semver: ^6.3.1
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 91dd5f203ed04568c70b052e2f26dfaac7c146447196c00b8ecbb6d3d2f3b517abadb985d3321a19d143adaed6fe17f7f79f8f50e0c20e9d8ad83e1027b42424
  languageName: node
  linkType: hard

"@babel/helper-member-expression-to-functions@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/helper-member-expression-to-functions@npm:7.25.9"
  dependencies:
    "@babel/traverse": ^7.25.9
    "@babel/types": ^7.25.9
  checksum: 8e2f1979b6d596ac2a8cbf17f2cf709180fefc274ac3331408b48203fe19134ed87800774ef18838d0275c3965130bae22980d90caed756b7493631d4b2cf961
  languageName: node
  linkType: hard

"@babel/helper-module-imports@npm:^7.24.7, @babel/helper-module-imports@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/helper-module-imports@npm:7.25.9"
  dependencies:
    "@babel/traverse": ^7.25.9
    "@babel/types": ^7.25.9
  checksum: 1b411ce4ca825422ef7065dffae7d8acef52023e51ad096351e3e2c05837e9bf9fca2af9ca7f28dc26d596a588863d0fedd40711a88e350b736c619a80e704e6
  languageName: node
  linkType: hard

"@babel/helper-module-transforms@npm:^7.26.0":
  version: 7.26.0
  resolution: "@babel/helper-module-transforms@npm:7.26.0"
  dependencies:
    "@babel/helper-module-imports": ^7.25.9
    "@babel/helper-validator-identifier": ^7.25.9
    "@babel/traverse": ^7.25.9
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 942eee3adf2b387443c247a2c190c17c4fd45ba92a23087abab4c804f40541790d51ad5277e4b5b1ed8d5ba5b62de73857446b7742f835c18ebd350384e63917
  languageName: node
  linkType: hard

"@babel/helper-optimise-call-expression@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/helper-optimise-call-expression@npm:7.25.9"
  dependencies:
    "@babel/types": ^7.25.9
  checksum: f09d0ad60c0715b9a60c31841b3246b47d67650c512ce85bbe24a3124f1a4d66377df793af393273bc6e1015b0a9c799626c48e53747581c1582b99167cc65dc
  languageName: node
  linkType: hard

"@babel/helper-plugin-utils@npm:^7.24.8, @babel/helper-plugin-utils@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/helper-plugin-utils@npm:7.25.9"
  checksum: e19ec8acf0b696756e6d84531f532c5fe508dce57aa68c75572a77798bd04587a844a9a6c8ea7d62d673e21fdc174d091c9097fb29aea1c1b49f9c6eaa80f022
  languageName: node
  linkType: hard

"@babel/helper-replace-supers@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/helper-replace-supers@npm:7.25.9"
  dependencies:
    "@babel/helper-member-expression-to-functions": ^7.25.9
    "@babel/helper-optimise-call-expression": ^7.25.9
    "@babel/traverse": ^7.25.9
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 84f40e12520b7023e52d289bf9d569a06284879fe23bbbacad86bec5d978b2669769f11b073fcfeb1567d8c547168323005fda88607a4681ecaeb4a5cdd48bb9
  languageName: node
  linkType: hard

"@babel/helper-skip-transparent-expression-wrappers@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/helper-skip-transparent-expression-wrappers@npm:7.25.9"
  dependencies:
    "@babel/traverse": ^7.25.9
    "@babel/types": ^7.25.9
  checksum: fdbb5248932198bc26daa6abf0d2ac42cab9c2dbb75b7e9f40d425c8f28f09620b886d40e7f9e4e08ffc7aaa2cefe6fc2c44be7c20e81f7526634702fb615bdc
  languageName: node
  linkType: hard

"@babel/helper-string-parser@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/helper-string-parser@npm:7.25.9"
  checksum: 6435ee0849e101681c1849868278b5aee82686ba2c1e27280e5e8aca6233af6810d39f8e4e693d2f2a44a3728a6ccfd66f72d71826a94105b86b731697cdfa99
  languageName: node
  linkType: hard

"@babel/helper-validator-identifier@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/helper-validator-identifier@npm:7.25.9"
  checksum: 5b85918cb1a92a7f3f508ea02699e8d2422fe17ea8e82acd445006c0ef7520fbf48e3dbcdaf7b0a1d571fc3a2715a29719e5226636cb6042e15fe6ed2a590944
  languageName: node
  linkType: hard

"@babel/helper-validator-option@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/helper-validator-option@npm:7.25.9"
  checksum: 9491b2755948ebbdd68f87da907283698e663b5af2d2b1b02a2765761974b1120d5d8d49e9175b167f16f72748ffceec8c9cf62acfbee73f4904507b246e2b3d
  languageName: node
  linkType: hard

"@babel/helpers@npm:^7.26.0":
  version: 7.26.0
  resolution: "@babel/helpers@npm:7.26.0"
  dependencies:
    "@babel/template": ^7.25.9
    "@babel/types": ^7.26.0
  checksum: d77fe8d45033d6007eadfa440355c1355eed57902d5a302f450827ad3d530343430a21210584d32eef2f216ae463d4591184c6fc60cf205bbf3a884561469200
  languageName: node
  linkType: hard

"@babel/parser@npm:^7.25.3, @babel/parser@npm:^7.25.6, @babel/parser@npm:^7.25.9, @babel/parser@npm:^7.26.0, @babel/parser@npm:^7.26.3":
  version: 7.26.3
  resolution: "@babel/parser@npm:7.26.3"
  dependencies:
    "@babel/types": ^7.26.3
  bin:
    parser: ./bin/babel-parser.js
  checksum: e2bff2e9fa6540ee18fecc058bc74837eda2ddcecbe13454667314a93fc0ba26c1fb862c812d84f6d5f225c3bd8d191c3a42d4296e287a882c4e1f82ff2815ff
  languageName: node
  linkType: hard

"@babel/plugin-syntax-jsx@npm:^7.24.7":
  version: 7.25.9
  resolution: "@babel/plugin-syntax-jsx@npm:7.25.9"
  dependencies:
    "@babel/helper-plugin-utils": ^7.25.9
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: bb609d1ffb50b58f0c1bac8810d0e46a4f6c922aa171c458f3a19d66ee545d36e782d3bffbbc1fed0dc65a558bdce1caf5279316583c0fff5a2c1658982a8563
  languageName: node
  linkType: hard

"@babel/plugin-syntax-typescript@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/plugin-syntax-typescript@npm:7.25.9"
  dependencies:
    "@babel/helper-plugin-utils": ^7.25.9
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 0e9821e8ba7d660c36c919654e4144a70546942ae184e85b8102f2322451eae102cbfadbcadd52ce077a2b44b400ee52394c616feab7b5b9f791b910e933fd33
  languageName: node
  linkType: hard

"@babel/plugin-transform-typescript@npm:^7.23.3":
  version: 7.26.3
  resolution: "@babel/plugin-transform-typescript@npm:7.26.3"
  dependencies:
    "@babel/helper-annotate-as-pure": ^7.25.9
    "@babel/helper-create-class-features-plugin": ^7.25.9
    "@babel/helper-plugin-utils": ^7.25.9
    "@babel/helper-skip-transparent-expression-wrappers": ^7.25.9
    "@babel/plugin-syntax-typescript": ^7.25.9
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 38ab210e80d4fc4eaa27e88705a961d53f5eae1dcd0ef8794affe3002fec557404e8bb29ca22d18e691a75690e3bcadbfeb8207a830f15cf83231ab5fd1ea08b
  languageName: node
  linkType: hard

"@babel/runtime@npm:^7.16.3, @babel/runtime@npm:^7.22.6":
  version: 7.26.0
  resolution: "@babel/runtime@npm:7.26.0"
  dependencies:
    regenerator-runtime: ^0.14.0
  checksum: c8e2c0504ab271b3467a261a8f119bf2603eb857a0d71e37791f4e3fae00f681365073cc79f141ddaa90c6077c60ba56448004ad5429d07ac73532be9f7cf28a
  languageName: node
  linkType: hard

"@babel/template@npm:^7.25.0, @babel/template@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/template@npm:7.25.9"
  dependencies:
    "@babel/code-frame": ^7.25.9
    "@babel/parser": ^7.25.9
    "@babel/types": ^7.25.9
  checksum: 103641fea19c7f4e82dc913aa6b6ac157112a96d7c724d513288f538b84bae04fb87b1f1e495ac1736367b1bc30e10f058b30208fb25f66038e1f1eb4e426472
  languageName: node
  linkType: hard

"@babel/traverse@npm:^7.25.6, @babel/traverse@npm:^7.25.9":
  version: 7.26.4
  resolution: "@babel/traverse@npm:7.26.4"
  dependencies:
    "@babel/code-frame": ^7.26.2
    "@babel/generator": ^7.26.3
    "@babel/parser": ^7.26.3
    "@babel/template": ^7.25.9
    "@babel/types": ^7.26.3
    debug: ^4.3.1
    globals: ^11.1.0
  checksum: dcdf51b27ab640291f968e4477933465c2910bfdcbcff8f5315d1f29b8ff861864f363e84a71fb489f5e9708e8b36b7540608ce019aa5e57ef7a4ba537e62700
  languageName: node
  linkType: hard

"@babel/types@npm:^7.25.6, @babel/types@npm:^7.25.9, @babel/types@npm:^7.26.0, @babel/types@npm:^7.26.3":
  version: 7.26.3
  resolution: "@babel/types@npm:7.26.3"
  dependencies:
    "@babel/helper-string-parser": ^7.25.9
    "@babel/helper-validator-identifier": ^7.25.9
  checksum: 195f428080dcaadbcecc9445df7f91063beeaa91b49ccd78f38a5af6b75a6a58391d0c6614edb1ea322e57889a1684a0aab8e667951f820196901dd341f931e9
  languageName: node
  linkType: hard

"@colors/colors@npm:1.6.0, @colors/colors@npm:^1.6.0":
  version: 1.6.0
  resolution: "@colors/colors@npm:1.6.0"
  checksum: aa209963e0c3218e80a4a20553ba8c0fbb6fa13140540b4e5f97923790be06801fc90172c1114fc8b7e888b3d012b67298cde6b9e81521361becfaee400c662f
  languageName: node
  linkType: hard

"@cspotcode/source-map-support@npm:^0.8.0":
  version: 0.8.1
  resolution: "@cspotcode/source-map-support@npm:0.8.1"
  dependencies:
    "@jridgewell/trace-mapping": 0.3.9
  checksum: 5718f267085ed8edb3e7ef210137241775e607ee18b77d95aa5bd7514f47f5019aa2d82d96b3bf342ef7aa890a346fa1044532ff7cc3009e7d24fce3ce6200fa
  languageName: node
  linkType: hard

"@dabh/diagnostics@npm:^2.0.2":
  version: 2.0.3
  resolution: "@dabh/diagnostics@npm:2.0.3"
  dependencies:
    colorspace: 1.1.x
    enabled: 2.0.x
    kuler: ^2.0.0
  checksum: 4879600c55c8315a0fb85fbb19057bad1adc08f0a080a8cb4e2b63f723c379bfc4283b68123a2b078d367b327dd8df12fcb27464efe791addc0a48b9df6d79a1
  languageName: node
  linkType: hard

"@dotenvx/dotenvx@npm:^0.24.0":
  version: 0.24.0
  resolution: "@dotenvx/dotenvx@npm:0.24.0"
  dependencies:
    "@inquirer/prompts": ^3.3.0
    chalk: ^4.1.2
    clipboardy: ^2.3.0
    commander: ^11.1.0
    conf: ^10.2.0
    dotenv: ^16.4.5
    dotenv-expand: ^11.0.6
    execa: ^5.1.1
    glob: ^10.3.10
    ignore: ^5.3.0
    object-treeify: 1.1.33
    open: ^8.4.2
    ora: ^5.4.1
    undici: ^5.28.3
    update-notifier: ^5.1.0
    which: ^4.0.0
    winston: ^3.11.0
    xxhashjs: ^0.2.2
  bin:
    dotenvx: src/cli/dotenvx.js
    git-dotenvx: src/cli/dotenvx.js
  checksum: fd6502bdc77872ecc4e5c10a3c6b1e5fb3d947e4345b103fe9e3de6ac40cf10e0938e850803f59485dcaa1906b97e5a9a16f2fc3da32aa7b3cf35c74c573bd52
  languageName: node
  linkType: hard

"@esbuild/aix-ppc64@npm:0.21.5":
  version: 0.21.5
  resolution: "@esbuild/aix-ppc64@npm:0.21.5"
  conditions: os=aix & cpu=ppc64
  languageName: node
  linkType: hard

"@esbuild/android-arm64@npm:0.21.5":
  version: 0.21.5
  resolution: "@esbuild/android-arm64@npm:0.21.5"
  conditions: os=android & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/android-arm@npm:0.21.5":
  version: 0.21.5
  resolution: "@esbuild/android-arm@npm:0.21.5"
  conditions: os=android & cpu=arm
  languageName: node
  linkType: hard

"@esbuild/android-x64@npm:0.21.5":
  version: 0.21.5
  resolution: "@esbuild/android-x64@npm:0.21.5"
  conditions: os=android & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/darwin-arm64@npm:0.21.5":
  version: 0.21.5
  resolution: "@esbuild/darwin-arm64@npm:0.21.5"
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/darwin-x64@npm:0.21.5":
  version: 0.21.5
  resolution: "@esbuild/darwin-x64@npm:0.21.5"
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/freebsd-arm64@npm:0.21.5":
  version: 0.21.5
  resolution: "@esbuild/freebsd-arm64@npm:0.21.5"
  conditions: os=freebsd & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/freebsd-x64@npm:0.21.5":
  version: 0.21.5
  resolution: "@esbuild/freebsd-x64@npm:0.21.5"
  conditions: os=freebsd & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/linux-arm64@npm:0.21.5":
  version: 0.21.5
  resolution: "@esbuild/linux-arm64@npm:0.21.5"
  conditions: os=linux & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/linux-arm@npm:0.21.5":
  version: 0.21.5
  resolution: "@esbuild/linux-arm@npm:0.21.5"
  conditions: os=linux & cpu=arm
  languageName: node
  linkType: hard

"@esbuild/linux-ia32@npm:0.21.5":
  version: 0.21.5
  resolution: "@esbuild/linux-ia32@npm:0.21.5"
  conditions: os=linux & cpu=ia32
  languageName: node
  linkType: hard

"@esbuild/linux-loong64@npm:0.21.5":
  version: 0.21.5
  resolution: "@esbuild/linux-loong64@npm:0.21.5"
  conditions: os=linux & cpu=loong64
  languageName: node
  linkType: hard

"@esbuild/linux-mips64el@npm:0.21.5":
  version: 0.21.5
  resolution: "@esbuild/linux-mips64el@npm:0.21.5"
  conditions: os=linux & cpu=mips64el
  languageName: node
  linkType: hard

"@esbuild/linux-ppc64@npm:0.21.5":
  version: 0.21.5
  resolution: "@esbuild/linux-ppc64@npm:0.21.5"
  conditions: os=linux & cpu=ppc64
  languageName: node
  linkType: hard

"@esbuild/linux-riscv64@npm:0.21.5":
  version: 0.21.5
  resolution: "@esbuild/linux-riscv64@npm:0.21.5"
  conditions: os=linux & cpu=riscv64
  languageName: node
  linkType: hard

"@esbuild/linux-s390x@npm:0.21.5":
  version: 0.21.5
  resolution: "@esbuild/linux-s390x@npm:0.21.5"
  conditions: os=linux & cpu=s390x
  languageName: node
  linkType: hard

"@esbuild/linux-x64@npm:0.21.5":
  version: 0.21.5
  resolution: "@esbuild/linux-x64@npm:0.21.5"
  conditions: os=linux & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/netbsd-x64@npm:0.21.5":
  version: 0.21.5
  resolution: "@esbuild/netbsd-x64@npm:0.21.5"
  conditions: os=netbsd & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/openbsd-x64@npm:0.21.5":
  version: 0.21.5
  resolution: "@esbuild/openbsd-x64@npm:0.21.5"
  conditions: os=openbsd & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/sunos-x64@npm:0.21.5":
  version: 0.21.5
  resolution: "@esbuild/sunos-x64@npm:0.21.5"
  conditions: os=sunos & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/win32-arm64@npm:0.21.5":
  version: 0.21.5
  resolution: "@esbuild/win32-arm64@npm:0.21.5"
  conditions: os=win32 & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/win32-ia32@npm:0.21.5":
  version: 0.21.5
  resolution: "@esbuild/win32-ia32@npm:0.21.5"
  conditions: os=win32 & cpu=ia32
  languageName: node
  linkType: hard

"@esbuild/win32-x64@npm:0.21.5":
  version: 0.21.5
  resolution: "@esbuild/win32-x64@npm:0.21.5"
  conditions: os=win32 & cpu=x64
  languageName: node
  linkType: hard

"@eslint-community/eslint-utils@npm:^4.1.2, @eslint-community/eslint-utils@npm:^4.2.0, @eslint-community/eslint-utils@npm:^4.4.0":
  version: 4.4.1
  resolution: "@eslint-community/eslint-utils@npm:4.4.1"
  dependencies:
    eslint-visitor-keys: ^3.4.3
  peerDependencies:
    eslint: ^6.0.0 || ^7.0.0 || >=8.0.0
  checksum: a7ffc838eb6a9ef594cda348458ccf38f34439ac77dc090fa1c120024bcd4eb911dfd74d5ef44d42063e7949fa7c5123ce714a015c4abb917d4124be1bd32bfe
  languageName: node
  linkType: hard

"@eslint-community/regexpp@npm:^4.10.0, @eslint-community/regexpp@npm:^4.11.0, @eslint-community/regexpp@npm:^4.6.1":
  version: 4.12.1
  resolution: "@eslint-community/regexpp@npm:4.12.1"
  checksum: 0d628680e204bc316d545b4993d3658427ca404ae646ce541fcc65306b8c712c340e5e573e30fb9f85f4855c0c5f6dca9868931f2fcced06417fbe1a0c6cd2d6
  languageName: node
  linkType: hard

"@eslint/eslintrc@npm:^2.1.4":
  version: 2.1.4
  resolution: "@eslint/eslintrc@npm:2.1.4"
  dependencies:
    ajv: ^6.12.4
    debug: ^4.3.2
    espree: ^9.6.0
    globals: ^13.19.0
    ignore: ^5.2.0
    import-fresh: ^3.2.1
    js-yaml: ^4.1.0
    minimatch: ^3.1.2
    strip-json-comments: ^3.1.1
  checksum: 10957c7592b20ca0089262d8c2a8accbad14b4f6507e35416c32ee6b4dbf9cad67dfb77096bbd405405e9ada2b107f3797fe94362e1c55e0b09d6e90dd149127
  languageName: node
  linkType: hard

"@eslint/js@npm:8.57.1":
  version: 8.57.1
  resolution: "@eslint/js@npm:8.57.1"
  checksum: 2afb77454c06e8316793d2e8e79a0154854d35e6782a1217da274ca60b5044d2c69d6091155234ed0551a1e408f86f09dd4ece02752c59568fa403e60611e880
  languageName: node
  linkType: hard

"@fastify/busboy@npm:^2.0.0":
  version: 2.1.1
  resolution: "@fastify/busboy@npm:2.1.1"
  checksum: 42c32ef75e906c9a4809c1e1930a5ca6d4ddc8d138e1a8c8ba5ea07f997db32210617d23b2e4a85fe376316a41a1a0439fc6ff2dedf5126d96f45a9d80754fb2
  languageName: node
  linkType: hard

"@google/generative-ai@npm:^0.2.1":
  version: 0.2.1
  resolution: "@google/generative-ai@npm:0.2.1"
  checksum: 1263d2eada5bc4961e2dd4fe1ccb33f12b57846f22107e275f9554dcaaf021b0988a9280ac58c8a49a830b1cd6f4e724ae5a0545d6e553ff2dea76e1006d8989
  languageName: node
  linkType: hard

"@hapi/bourne@npm:^3.0.0":
  version: 3.0.0
  resolution: "@hapi/bourne@npm:3.0.0"
  checksum: 7174cab6c33191918fcdb1953fe3169a1106e6ac79a67ef5fd08b351f0813f8f608170f2239786cbe5519e03cdfe5ab748ea1635caa06dcd5802410295514ef8
  languageName: node
  linkType: hard

"@humanwhocodes/config-array@npm:^0.13.0":
  version: 0.13.0
  resolution: "@humanwhocodes/config-array@npm:0.13.0"
  dependencies:
    "@humanwhocodes/object-schema": ^2.0.3
    debug: ^4.3.1
    minimatch: ^3.0.5
  checksum: eae69ff9134025dd2924f0b430eb324981494be26f0fddd267a33c28711c4db643242cf9fddf7dadb9d16c96b54b2d2c073e60a56477df86e0173149313bd5d6
  languageName: node
  linkType: hard

"@humanwhocodes/module-importer@npm:^1.0.1":
  version: 1.0.1
  resolution: "@humanwhocodes/module-importer@npm:1.0.1"
  checksum: 0fd22007db8034a2cdf2c764b140d37d9020bbfce8a49d3ec5c05290e77d4b0263b1b972b752df8c89e5eaa94073408f2b7d977aed131faf6cf396ebb5d7fb61
  languageName: node
  linkType: hard

"@humanwhocodes/object-schema@npm:^2.0.3":
  version: 2.0.3
  resolution: "@humanwhocodes/object-schema@npm:2.0.3"
  checksum: d3b78f6c5831888c6ecc899df0d03bcc25d46f3ad26a11d7ea52944dc36a35ef543fad965322174238d677a43d5c694434f6607532cff7077062513ad7022631
  languageName: node
  linkType: hard

"@inquirer/checkbox@npm:^1.5.2":
  version: 1.5.2
  resolution: "@inquirer/checkbox@npm:1.5.2"
  dependencies:
    "@inquirer/core": ^6.0.0
    "@inquirer/type": ^1.1.6
    ansi-escapes: ^4.3.2
    chalk: ^4.1.2
    figures: ^3.2.0
  checksum: aeb1efe6cd424d32c8c971b97fd4f95f84a44f8e8538c9e1d6c552b05a7efb74cafdb6c5173d6a9f8a085d17e247524f620118408e74d64179ec7d219dba99f7
  languageName: node
  linkType: hard

"@inquirer/confirm@npm:^2.0.17":
  version: 2.0.17
  resolution: "@inquirer/confirm@npm:2.0.17"
  dependencies:
    "@inquirer/core": ^6.0.0
    "@inquirer/type": ^1.1.6
    chalk: ^4.1.2
  checksum: 58085b31618308f2082b019c65c87a9698f05ba2336cc29d95f5d89680aa3ab0ff5cf6ec9828158b3850a5dee329a3050736b4c5b77af78870136605909e5a95
  languageName: node
  linkType: hard

"@inquirer/core@npm:^6.0.0":
  version: 6.0.0
  resolution: "@inquirer/core@npm:6.0.0"
  dependencies:
    "@inquirer/type": ^1.1.6
    "@types/mute-stream": ^0.0.4
    "@types/node": ^20.10.7
    "@types/wrap-ansi": ^3.0.0
    ansi-escapes: ^4.3.2
    chalk: ^4.1.2
    cli-spinners: ^2.9.2
    cli-width: ^4.1.0
    figures: ^3.2.0
    mute-stream: ^1.0.0
    run-async: ^3.0.0
    signal-exit: ^4.1.0
    strip-ansi: ^6.0.1
    wrap-ansi: ^6.2.0
  checksum: 77a7185db6015ebcfb0429923be7da59f30b487204bb8692515c1059a79caad790da9915b0dea9eb9f6a9b1596e05edec98b5d57851f2180e84aba5f94077a2c
  languageName: node
  linkType: hard

"@inquirer/editor@npm:^1.2.15":
  version: 1.2.15
  resolution: "@inquirer/editor@npm:1.2.15"
  dependencies:
    "@inquirer/core": ^6.0.0
    "@inquirer/type": ^1.1.6
    chalk: ^4.1.2
    external-editor: ^3.1.0
  checksum: 84743b62c014d5da4d9e9d5b2ccf3bc99544aa4dd9e9e7059e6f88c76e6ddfba1b490b6a441ce3f3d16360b34a5b4d273120ba078d159a2aba75e7e34e0ed0f1
  languageName: node
  linkType: hard

"@inquirer/expand@npm:^1.1.16":
  version: 1.1.16
  resolution: "@inquirer/expand@npm:1.1.16"
  dependencies:
    "@inquirer/core": ^6.0.0
    "@inquirer/type": ^1.1.6
    chalk: ^4.1.2
    figures: ^3.2.0
  checksum: 5158f8eb807bd1d55e7e7ca9288051f5947970b32b1a33cd9c94ea228c03cdb18508be5ecd0237061276721f5c9ca96741b3e2c288123c526cdd5049cdf566db
  languageName: node
  linkType: hard

"@inquirer/input@npm:^1.2.16":
  version: 1.2.16
  resolution: "@inquirer/input@npm:1.2.16"
  dependencies:
    "@inquirer/core": ^6.0.0
    "@inquirer/type": ^1.1.6
    chalk: ^4.1.2
  checksum: 35b4c414c12964989eb941e97fd2e27945869a2e8889a8131fa908687b0f09955f34fe4e50a075c2a1fc5034ea82ce96fa0a1da764d24f0127db0da68550378a
  languageName: node
  linkType: hard

"@inquirer/password@npm:^1.1.16":
  version: 1.1.16
  resolution: "@inquirer/password@npm:1.1.16"
  dependencies:
    "@inquirer/core": ^6.0.0
    "@inquirer/type": ^1.1.6
    ansi-escapes: ^4.3.2
    chalk: ^4.1.2
  checksum: d5a1b2ae68b462aa364543fe0cb8cfbaaf979a5997f6a1c4de02d9aec67b7326c09456205ebec4dd09f38c7c5f16c6655e5e9b1e6d01436e6270ad6e94f5d6a1
  languageName: node
  linkType: hard

"@inquirer/prompts@npm:^3.3.0":
  version: 3.3.2
  resolution: "@inquirer/prompts@npm:3.3.2"
  dependencies:
    "@inquirer/checkbox": ^1.5.2
    "@inquirer/confirm": ^2.0.17
    "@inquirer/core": ^6.0.0
    "@inquirer/editor": ^1.2.15
    "@inquirer/expand": ^1.1.16
    "@inquirer/input": ^1.2.16
    "@inquirer/password": ^1.1.16
    "@inquirer/rawlist": ^1.2.16
    "@inquirer/select": ^1.3.3
  checksum: 41bb9ca24b0d0d45b7a5413617028dede818d3f5d9c8f6a3a2d3eadbed122979c0602ff16c9c0ab70375b0632092e3c5a45da6296108d4365c37e85171e724cd
  languageName: node
  linkType: hard

"@inquirer/rawlist@npm:^1.2.16":
  version: 1.2.16
  resolution: "@inquirer/rawlist@npm:1.2.16"
  dependencies:
    "@inquirer/core": ^6.0.0
    "@inquirer/type": ^1.1.6
    chalk: ^4.1.2
  checksum: a4acefb0f54e4d4c3f7c44d35971cb0b8cbf2acd6dbe490576cd24369f3304ff4a36255cd4cc851c2de7c037cf70f71c129bc6c8c5c80dce495998e6168904fd
  languageName: node
  linkType: hard

"@inquirer/select@npm:^1.3.3":
  version: 1.3.3
  resolution: "@inquirer/select@npm:1.3.3"
  dependencies:
    "@inquirer/core": ^6.0.0
    "@inquirer/type": ^1.1.6
    ansi-escapes: ^4.3.2
    chalk: ^4.1.2
    figures: ^3.2.0
  checksum: 0f33c51ab69c156b96092bfb107d08dd0f4227274917b9406aa23877e3ba94fd6738800fb973c44c051aaebdba72d07dc328df4b76e6e1285f68aa01a7ed0ed8
  languageName: node
  linkType: hard

"@inquirer/type@npm:^1.1.6":
  version: 1.5.5
  resolution: "@inquirer/type@npm:1.5.5"
  dependencies:
    mute-stream: ^1.0.0
  checksum: 6cada82bb14519f3c71f455b08dc03c1064046fe0469aa5fce44c7ebf88a3a3d67a0cf852b0a7339476fec3b02874167f46d2c5b0964218d00273ab27ff861c5
  languageName: node
  linkType: hard

"@intlify/core-base@npm:9.14.2":
  version: 9.14.2
  resolution: "@intlify/core-base@npm:9.14.2"
  dependencies:
    "@intlify/message-compiler": 9.14.2
    "@intlify/shared": 9.14.2
  checksum: ec2f6414f60eac33950aa46df3f7f8a7c30c6a6690cf25e1cf597ac229c566649ba83a1bd3b74d8ba9486268bbbec383fc98fb1a348be568c05b65e1ecba00e6
  languageName: node
  linkType: hard

"@intlify/message-compiler@npm:9.14.2":
  version: 9.14.2
  resolution: "@intlify/message-compiler@npm:9.14.2"
  dependencies:
    "@intlify/shared": 9.14.2
    source-map-js: ^1.0.2
  checksum: 00dfbe83f6d4e7cc538717f785c23f2afa07d0778927a9e1ec5e156e29580c03740ed43111d292a96e53b2db44785a2713794541f970d1d7a2ba3b377bd81e76
  languageName: node
  linkType: hard

"@intlify/shared@npm:9.14.2":
  version: 9.14.2
  resolution: "@intlify/shared@npm:9.14.2"
  checksum: 8befe0c605ed2b55fe3058d83c63a2d4831d6f9b786129ae04206009d9bd63111f0db3c645ebcd7addb9aa15296380be3f0f9daa37b5bae4b832c73fd41e2b39
  languageName: node
  linkType: hard

"@isaacs/cliui@npm:^8.0.2":
  version: 8.0.2
  resolution: "@isaacs/cliui@npm:8.0.2"
  dependencies:
    string-width: ^5.1.2
    string-width-cjs: "npm:string-width@^4.2.0"
    strip-ansi: ^7.0.1
    strip-ansi-cjs: "npm:strip-ansi@^6.0.1"
    wrap-ansi: ^8.1.0
    wrap-ansi-cjs: "npm:wrap-ansi@^7.0.0"
  checksum: 4a473b9b32a7d4d3cfb7a614226e555091ff0c5a29a1734c28c72a182c2f6699b26fc6b5c2131dfd841e86b185aea714c72201d7c98c2fba5f17709333a67aeb
  languageName: node
  linkType: hard

"@isaacs/fs-minipass@npm:^4.0.0":
  version: 4.0.1
  resolution: "@isaacs/fs-minipass@npm:4.0.1"
  dependencies:
    minipass: ^7.0.4
  checksum: 5d36d289960e886484362d9eb6a51d1ea28baed5f5d0140bbe62b99bac52eaf06cc01c2bc0d3575977962f84f6b2c4387b043ee632216643d4787b0999465bf2
  languageName: node
  linkType: hard

"@jridgewell/gen-mapping@npm:^0.3.2, @jridgewell/gen-mapping@npm:^0.3.5":
  version: 0.3.8
  resolution: "@jridgewell/gen-mapping@npm:0.3.8"
  dependencies:
    "@jridgewell/set-array": ^1.2.1
    "@jridgewell/sourcemap-codec": ^1.4.10
    "@jridgewell/trace-mapping": ^0.3.24
  checksum: c0687b5227461717aa537fe71a42e356bcd1c43293b3353796a148bf3b0d6f59109def46c22f05b60e29a46f19b2e4676d027959a7c53a6c92b9d5b0d87d0420
  languageName: node
  linkType: hard

"@jridgewell/resolve-uri@npm:^3.0.3, @jridgewell/resolve-uri@npm:^3.1.0":
  version: 3.1.2
  resolution: "@jridgewell/resolve-uri@npm:3.1.2"
  checksum: 83b85f72c59d1c080b4cbec0fef84528963a1b5db34e4370fa4bd1e3ff64a0d80e0cee7369d11d73c704e0286fb2865b530acac7a871088fbe92b5edf1000870
  languageName: node
  linkType: hard

"@jridgewell/set-array@npm:^1.2.1":
  version: 1.2.1
  resolution: "@jridgewell/set-array@npm:1.2.1"
  checksum: 832e513a85a588f8ed4f27d1279420d8547743cc37fcad5a5a76fc74bb895b013dfe614d0eed9cb860048e6546b798f8f2652020b4b2ba0561b05caa8c654b10
  languageName: node
  linkType: hard

"@jridgewell/sourcemap-codec@npm:^1.4.10, @jridgewell/sourcemap-codec@npm:^1.4.14, @jridgewell/sourcemap-codec@npm:^1.5.0":
  version: 1.5.0
  resolution: "@jridgewell/sourcemap-codec@npm:1.5.0"
  checksum: 05df4f2538b3b0f998ea4c1cd34574d0feba216fa5d4ccaef0187d12abf82eafe6021cec8b49f9bb4d90f2ba4582ccc581e72986a5fcf4176ae0cfeb04cf52ec
  languageName: node
  linkType: hard

"@jridgewell/trace-mapping@npm:0.3.9":
  version: 0.3.9
  resolution: "@jridgewell/trace-mapping@npm:0.3.9"
  dependencies:
    "@jridgewell/resolve-uri": ^3.0.3
    "@jridgewell/sourcemap-codec": ^1.4.10
  checksum: d89597752fd88d3f3480845691a05a44bd21faac18e2185b6f436c3b0fd0c5a859fbbd9aaa92050c4052caf325ad3e10e2e1d1b64327517471b7d51babc0ddef
  languageName: node
  linkType: hard

"@jridgewell/trace-mapping@npm:^0.3.24, @jridgewell/trace-mapping@npm:^0.3.25":
  version: 0.3.25
  resolution: "@jridgewell/trace-mapping@npm:0.3.25"
  dependencies:
    "@jridgewell/resolve-uri": ^3.1.0
    "@jridgewell/sourcemap-codec": ^1.4.14
  checksum: 9d3c40d225e139987b50c48988f8717a54a8c994d8a948ee42e1412e08988761d0754d7d10b803061cc3aebf35f92a5dbbab493bd0e1a9ef9e89a2130e83ba34
  languageName: node
  linkType: hard

"@koa/bodyparser@npm:^5.1.0":
  version: 5.1.1
  resolution: "@koa/bodyparser@npm:5.1.1"
  dependencies:
    co-body: ^6.1.0
    lodash.merge: ^4.6.2
    type-is: ^1.6.18
  peerDependencies:
    koa: ^2.14.1
  checksum: 68915ebd78eee339a34d0559e6b6909a7eb4ec03f7b5651de9337e63d7440ee9a510fb316ee3c2013ffc21a44583a552a95965c806fd4a373a44d3289de6668c
  languageName: node
  linkType: hard

"@koa/cors@npm:^5.0.0":
  version: 5.0.0
  resolution: "@koa/cors@npm:5.0.0"
  dependencies:
    vary: ^1.1.2
  checksum: 050701fb57dede2fefe0217459782bab7c9488fd07ff1f87fff680005cab43e03b7509e6015ea68082aadb1b31fe3eea7858ebdc93a2cf6f26d36d071190d50c
  languageName: node
  linkType: hard

"@koa/router@npm:^12.0.1":
  version: 12.0.2
  resolution: "@koa/router@npm:12.0.2"
  dependencies:
    debug: ^4.3.4
    http-errors: ^2.0.0
    koa-compose: ^4.1.0
    methods: ^1.1.2
    path-to-regexp: ^6.3.0
  checksum: a68e95de7e36dfe482289def2687cbf13c52c12c5aa5089266bbae2959583fb522c4d142a6efab4dc6669cb922e75d01be6985a1979b6c897778d1471df36b5d
  languageName: node
  linkType: hard

"@lmstudio/lms-isomorphic@npm:^0.3.2":
  version: 0.3.2
  resolution: "@lmstudio/lms-isomorphic@npm:0.3.2"
  dependencies:
    bufferutil: ^4.0.1
    utf-8-validate: ^6.0.3
    ws: ^8.16.0
  checksum: e4b8c508f4c88706d2f67e6b3b6914f032c34d816902f613feb4df2ff66ee670abf3583ba06647b8fbb9b38e1f4863fbf78435f81f198ae779d3b5052d40040d
  languageName: node
  linkType: hard

"@lmstudio/sdk@npm:^0.1.0":
  version: 0.1.0
  resolution: "@lmstudio/sdk@npm:0.1.0"
  dependencies:
    "@lmstudio/lms-isomorphic": ^0.3.2
    chalk: ^4.1.2
  checksum: 9bb4e87ebb78006fc0d4784f1c69fa5fde92a26b4f16225c6791bf00f0f8c4ea68c9c4002ba181e80f6f16f4a6d0006e46551e2c516876258b935cef1f9f7eca
  languageName: node
  linkType: hard

"@nodelib/fs.scandir@npm:2.1.5":
  version: 2.1.5
  resolution: "@nodelib/fs.scandir@npm:2.1.5"
  dependencies:
    "@nodelib/fs.stat": 2.0.5
    run-parallel: ^1.1.9
  checksum: a970d595bd23c66c880e0ef1817791432dbb7acbb8d44b7e7d0e7a22f4521260d4a83f7f9fd61d44fda4610105577f8f58a60718105fb38352baed612fd79e59
  languageName: node
  linkType: hard

"@nodelib/fs.stat@npm:2.0.5, @nodelib/fs.stat@npm:^2.0.2":
  version: 2.0.5
  resolution: "@nodelib/fs.stat@npm:2.0.5"
  checksum: 012480b5ca9d97bff9261571dbbec7bbc6033f69cc92908bc1ecfad0792361a5a1994bc48674b9ef76419d056a03efadfce5a6cf6dbc0a36559571a7a483f6f0
  languageName: node
  linkType: hard

"@nodelib/fs.walk@npm:^1.2.3, @nodelib/fs.walk@npm:^1.2.8":
  version: 1.2.8
  resolution: "@nodelib/fs.walk@npm:1.2.8"
  dependencies:
    "@nodelib/fs.scandir": 2.1.5
    fastq: ^1.6.0
  checksum: 190c643f156d8f8f277bf2a6078af1ffde1fd43f498f187c2db24d35b4b4b5785c02c7dc52e356497b9a1b65b13edc996de08de0b961c32844364da02986dc53
  languageName: node
  linkType: hard

"@npmcli/agent@npm:^3.0.0":
  version: 3.0.0
  resolution: "@npmcli/agent@npm:3.0.0"
  dependencies:
    agent-base: ^7.1.0
    http-proxy-agent: ^7.0.0
    https-proxy-agent: ^7.0.1
    lru-cache: ^10.0.1
    socks-proxy-agent: ^8.0.3
  checksum: e8fc25d536250ed3e669813b36e8c6d805628b472353c57afd8c4fde0fcfcf3dda4ffe22f7af8c9070812ec2e7a03fb41d7151547cef3508efe661a5a3add20f
  languageName: node
  linkType: hard

"@npmcli/fs@npm:^4.0.0":
  version: 4.0.0
  resolution: "@npmcli/fs@npm:4.0.0"
  dependencies:
    semver: ^7.3.5
  checksum: 68951c589e9a4328698a35fd82fe71909a257d6f2ede0434d236fa55634f0fbcad9bb8755553ce5849bd25ee6f019f4d435921ac715c853582c4a7f5983c8d4a
  languageName: node
  linkType: hard

"@pkgjs/parseargs@npm:^0.11.0":
  version: 0.11.0
  resolution: "@pkgjs/parseargs@npm:0.11.0"
  checksum: 6ad6a00fc4f2f2cfc6bff76fb1d88b8ee20bc0601e18ebb01b6d4be583733a860239a521a7fbca73b612e66705078809483549d2b18f370eb346c5155c8e4a0f
  languageName: node
  linkType: hard

"@popperjs/core@npm:^2.11.8":
  version: 2.11.8
  resolution: "@popperjs/core@npm:2.11.8"
  checksum: e5c69fdebf52a4012f6a1f14817ca8e9599cb1be73dd1387e1785e2ed5e5f0862ff817f420a87c7fc532add1f88a12e25aeb010ffcbdc98eace3d55ce2139cf0
  languageName: node
  linkType: hard

"@remixicon/vue@npm:^4.1.0":
  version: 4.5.0
  resolution: "@remixicon/vue@npm:4.5.0"
  peerDependencies:
    vue: ">= 3"
  checksum: a96d2435df8286affddd9c72cc1e579d1d00d3d29b22e8c6ab301fc864ec1eec7dfa8883f5ead1e643c8e42dc95b0c9abbacb1ede3adc47a7e453a417ce82777
  languageName: node
  linkType: hard

"@rollup/pluginutils@npm:^5.0.4, @rollup/pluginutils@npm:^5.1.0, @rollup/pluginutils@npm:^5.1.3":
  version: 5.1.3
  resolution: "@rollup/pluginutils@npm:5.1.3"
  dependencies:
    "@types/estree": ^1.0.0
    estree-walker: ^2.0.2
    picomatch: ^4.0.2
  peerDependencies:
    rollup: ^1.20.0||^2.0.0||^3.0.0||^4.0.0
  peerDependenciesMeta:
    rollup:
      optional: true
  checksum: a6e9bac8ae94da39679dae390b53b43fe7a218f8fa2bfecf86e59be4da4ba02ac004f166daf55f03506e49108399394f13edeb62cce090f8cfc967b29f4738bf
  languageName: node
  linkType: hard

"@rollup/rollup-android-arm-eabi@npm:4.28.1":
  version: 4.28.1
  resolution: "@rollup/rollup-android-arm-eabi@npm:4.28.1"
  conditions: os=android & cpu=arm
  languageName: node
  linkType: hard

"@rollup/rollup-android-arm64@npm:4.28.1":
  version: 4.28.1
  resolution: "@rollup/rollup-android-arm64@npm:4.28.1"
  conditions: os=android & cpu=arm64
  languageName: node
  linkType: hard

"@rollup/rollup-darwin-arm64@npm:4.28.1":
  version: 4.28.1
  resolution: "@rollup/rollup-darwin-arm64@npm:4.28.1"
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"@rollup/rollup-darwin-x64@npm:4.28.1":
  version: 4.28.1
  resolution: "@rollup/rollup-darwin-x64@npm:4.28.1"
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"@rollup/rollup-freebsd-arm64@npm:4.28.1":
  version: 4.28.1
  resolution: "@rollup/rollup-freebsd-arm64@npm:4.28.1"
  conditions: os=freebsd & cpu=arm64
  languageName: node
  linkType: hard

"@rollup/rollup-freebsd-x64@npm:4.28.1":
  version: 4.28.1
  resolution: "@rollup/rollup-freebsd-x64@npm:4.28.1"
  conditions: os=freebsd & cpu=x64
  languageName: node
  linkType: hard

"@rollup/rollup-linux-arm-gnueabihf@npm:4.28.1":
  version: 4.28.1
  resolution: "@rollup/rollup-linux-arm-gnueabihf@npm:4.28.1"
  conditions: os=linux & cpu=arm & libc=glibc
  languageName: node
  linkType: hard

"@rollup/rollup-linux-arm-musleabihf@npm:4.28.1":
  version: 4.28.1
  resolution: "@rollup/rollup-linux-arm-musleabihf@npm:4.28.1"
  conditions: os=linux & cpu=arm & libc=musl
  languageName: node
  linkType: hard

"@rollup/rollup-linux-arm64-gnu@npm:4.28.1":
  version: 4.28.1
  resolution: "@rollup/rollup-linux-arm64-gnu@npm:4.28.1"
  conditions: os=linux & cpu=arm64 & libc=glibc
  languageName: node
  linkType: hard

"@rollup/rollup-linux-arm64-musl@npm:4.28.1":
  version: 4.28.1
  resolution: "@rollup/rollup-linux-arm64-musl@npm:4.28.1"
  conditions: os=linux & cpu=arm64 & libc=musl
  languageName: node
  linkType: hard

"@rollup/rollup-linux-loongarch64-gnu@npm:4.28.1":
  version: 4.28.1
  resolution: "@rollup/rollup-linux-loongarch64-gnu@npm:4.28.1"
  conditions: os=linux & cpu=loong64 & libc=glibc
  languageName: node
  linkType: hard

"@rollup/rollup-linux-powerpc64le-gnu@npm:4.28.1":
  version: 4.28.1
  resolution: "@rollup/rollup-linux-powerpc64le-gnu@npm:4.28.1"
  conditions: os=linux & cpu=ppc64 & libc=glibc
  languageName: node
  linkType: hard

"@rollup/rollup-linux-riscv64-gnu@npm:4.28.1":
  version: 4.28.1
  resolution: "@rollup/rollup-linux-riscv64-gnu@npm:4.28.1"
  conditions: os=linux & cpu=riscv64 & libc=glibc
  languageName: node
  linkType: hard

"@rollup/rollup-linux-s390x-gnu@npm:4.28.1":
  version: 4.28.1
  resolution: "@rollup/rollup-linux-s390x-gnu@npm:4.28.1"
  conditions: os=linux & cpu=s390x & libc=glibc
  languageName: node
  linkType: hard

"@rollup/rollup-linux-x64-gnu@npm:4.28.1":
  version: 4.28.1
  resolution: "@rollup/rollup-linux-x64-gnu@npm:4.28.1"
  conditions: os=linux & cpu=x64 & libc=glibc
  languageName: node
  linkType: hard

"@rollup/rollup-linux-x64-musl@npm:4.28.1":
  version: 4.28.1
  resolution: "@rollup/rollup-linux-x64-musl@npm:4.28.1"
  conditions: os=linux & cpu=x64 & libc=musl
  languageName: node
  linkType: hard

"@rollup/rollup-win32-arm64-msvc@npm:4.28.1":
  version: 4.28.1
  resolution: "@rollup/rollup-win32-arm64-msvc@npm:4.28.1"
  conditions: os=win32 & cpu=arm64
  languageName: node
  linkType: hard

"@rollup/rollup-win32-ia32-msvc@npm:4.28.1":
  version: 4.28.1
  resolution: "@rollup/rollup-win32-ia32-msvc@npm:4.28.1"
  conditions: os=win32 & cpu=ia32
  languageName: node
  linkType: hard

"@rollup/rollup-win32-x64-msvc@npm:4.28.1":
  version: 4.28.1
  resolution: "@rollup/rollup-win32-x64-msvc@npm:4.28.1"
  conditions: os=win32 & cpu=x64
  languageName: node
  linkType: hard

"@rtsao/scc@npm:^1.1.0":
  version: 1.1.0
  resolution: "@rtsao/scc@npm:1.1.0"
  checksum: 17d04adf404e04c1e61391ed97bca5117d4c2767a76ae3e879390d6dec7b317fcae68afbf9e98badee075d0b64fa60f287729c4942021b4d19cd01db77385c01
  languageName: node
  linkType: hard

"@sindresorhus/is@npm:^0.14.0":
  version: 0.14.0
  resolution: "@sindresorhus/is@npm:0.14.0"
  checksum: 971e0441dd44ba3909b467219a5e242da0fc584048db5324cfb8048148fa8dcc9d44d71e3948972c4f6121d24e5da402ef191420d1266a95f713bb6d6e59c98a
  languageName: node
  linkType: hard

"@szmarczak/http-timer@npm:^1.1.2":
  version: 1.1.2
  resolution: "@szmarczak/http-timer@npm:1.1.2"
  dependencies:
    defer-to-connect: ^1.0.1
  checksum: 4d9158061c5f397c57b4988cde33a163244e4f02df16364f103971957a32886beb104d6180902cbe8b38cb940e234d9f98a4e486200deca621923f62f50a06fe
  languageName: node
  linkType: hard

"@tsconfig/node10@npm:^1.0.7":
  version: 1.0.11
  resolution: "@tsconfig/node10@npm:1.0.11"
  checksum: 51fe47d55fe1b80ec35e6e5ed30a13665fd3a531945350aa74a14a1e82875fb60b350c2f2a5e72a64831b1b6bc02acb6760c30b3738b54954ec2dea82db7a267
  languageName: node
  linkType: hard

"@tsconfig/node12@npm:^1.0.7":
  version: 1.0.11
  resolution: "@tsconfig/node12@npm:1.0.11"
  checksum: 5ce29a41b13e7897a58b8e2df11269c5395999e588b9a467386f99d1d26f6c77d1af2719e407621412520ea30517d718d5192a32403b8dfcc163bf33e40a338a
  languageName: node
  linkType: hard

"@tsconfig/node14@npm:^1.0.0":
  version: 1.0.3
  resolution: "@tsconfig/node14@npm:1.0.3"
  checksum: 19275fe80c4c8d0ad0abed6a96dbf00642e88b220b090418609c4376e1cef81bf16237bf170ad1b341452feddb8115d8dd2e5acdfdea1b27422071163dc9ba9d
  languageName: node
  linkType: hard

"@tsconfig/node16@npm:^1.0.2":
  version: 1.0.4
  resolution: "@tsconfig/node16@npm:1.0.4"
  checksum: 202319785901f942a6e1e476b872d421baec20cf09f4b266a1854060efbf78cde16a4d256e8bc949d31e6cd9a90f1e8ef8fb06af96a65e98338a2b6b0de0a0ff
  languageName: node
  linkType: hard

"@types/accepts@npm:*":
  version: 1.3.7
  resolution: "@types/accepts@npm:1.3.7"
  dependencies:
    "@types/node": "*"
  checksum: 7678cf74976e16093aff6e6f9755826faf069ac1e30179276158ce46ea246348ff22ca6bdd46cef08428881337d9ceefbf00bab08a7731646eb9fc9449d6a1e7
  languageName: node
  linkType: hard

"@types/body-parser@npm:*":
  version: 1.19.5
  resolution: "@types/body-parser@npm:1.19.5"
  dependencies:
    "@types/connect": "*"
    "@types/node": "*"
  checksum: 1e251118c4b2f61029cc43b0dc028495f2d1957fe8ee49a707fb940f86a9bd2f9754230805598278fe99958b49e9b7e66eec8ef6a50ab5c1f6b93e1ba2aaba82
  languageName: node
  linkType: hard

"@types/connect@npm:*":
  version: 3.4.38
  resolution: "@types/connect@npm:3.4.38"
  dependencies:
    "@types/node": "*"
  checksum: 7eb1bc5342a9604facd57598a6c62621e244822442976c443efb84ff745246b10d06e8b309b6e80130026a396f19bf6793b7cecd7380169f369dac3bfc46fb99
  languageName: node
  linkType: hard

"@types/content-disposition@npm:*":
  version: 0.5.8
  resolution: "@types/content-disposition@npm:0.5.8"
  checksum: eeea868fb510ae7a32aa2d7de680fba79d59001f3e758a334621e10bc0a6496d3a42bb79243a5e53b9c63cb524522853ccc144fe1ab160c4247d37cdb81146c4
  languageName: node
  linkType: hard

"@types/cookies@npm:*":
  version: 0.9.0
  resolution: "@types/cookies@npm:0.9.0"
  dependencies:
    "@types/connect": "*"
    "@types/express": "*"
    "@types/keygrip": "*"
    "@types/node": "*"
  checksum: ce59bfdf3a5d750400ac32aa93157ec7be997dc632660cf0bbfd76df23d71a70bb5f0820558cd26b9a5576f86b6664a2fd23ae211b51202a5b2f9a15995d7331
  languageName: node
  linkType: hard

"@types/estree@npm:1.0.6, @types/estree@npm:^1.0.0":
  version: 1.0.6
  resolution: "@types/estree@npm:1.0.6"
  checksum: 8825d6e729e16445d9a1dd2fb1db2edc5ed400799064cd4d028150701031af012ba30d6d03fe9df40f4d7a437d0de6d2b256020152b7b09bde9f2e420afdffd9
  languageName: node
  linkType: hard

"@types/express-serve-static-core@npm:^5.0.0":
  version: 5.0.2
  resolution: "@types/express-serve-static-core@npm:5.0.2"
  dependencies:
    "@types/node": "*"
    "@types/qs": "*"
    "@types/range-parser": "*"
    "@types/send": "*"
  checksum: 841229b63801b334729e56ec04b5023e084e2962d61dddc95b3508e2fc821e8550bd69c074b6cb0a1c57147eb324c4dc543103d0827e34077f7eb6d230d08a8f
  languageName: node
  linkType: hard

"@types/express@npm:*":
  version: 5.0.0
  resolution: "@types/express@npm:5.0.0"
  dependencies:
    "@types/body-parser": "*"
    "@types/express-serve-static-core": ^5.0.0
    "@types/qs": "*"
    "@types/serve-static": "*"
  checksum: ef68d8e2b7593c930093b1e79bf4df15413773508c9acd6a1a933ed7017f2a4892a8d128b2222d7eab9a3fa43181067a378c2600d9258bd7ae917f170e962df4
  languageName: node
  linkType: hard

"@types/http-assert@npm:*":
  version: 1.5.6
  resolution: "@types/http-assert@npm:1.5.6"
  checksum: dfe1010164ba633859d90a50c4c53e69a38a16972061ef614acc1b0bdb7e53a1c923a11b4169a4a7eedc20b2303962d761727a212ae099717327cf4f38293817
  languageName: node
  linkType: hard

"@types/http-errors@npm:*":
  version: 2.0.4
  resolution: "@types/http-errors@npm:2.0.4"
  checksum: 1f3d7c3b32c7524811a45690881736b3ef741bf9849ae03d32ad1ab7062608454b150a4e7f1351f83d26a418b2d65af9bdc06198f1c079d75578282884c4e8e3
  languageName: node
  linkType: hard

"@types/json5@npm:^0.0.29":
  version: 0.0.29
  resolution: "@types/json5@npm:0.0.29"
  checksum: e60b153664572116dfea673c5bda7778dbff150498f44f998e34b5886d8afc47f16799280e4b6e241c0472aef1bc36add771c569c68fc5125fc2ae519a3eb9ac
  languageName: node
  linkType: hard

"@types/keygrip@npm:*":
  version: 1.0.6
  resolution: "@types/keygrip@npm:1.0.6"
  checksum: d157f60bf920492347791d2b26d530d5069ce05796549fbacd4c24d66ffbebbcb0ab67b21e7a1b80a593b9fd4b67dc4843dec04c12bbc2e0fddfb8577a826c41
  languageName: node
  linkType: hard

"@types/keyv@npm:^3.1.1":
  version: 3.1.4
  resolution: "@types/keyv@npm:3.1.4"
  dependencies:
    "@types/node": "*"
  checksum: e009a2bfb50e90ca9b7c6e8f648f8464067271fd99116f881073fa6fa76dc8d0133181dd65e6614d5fb1220d671d67b0124aef7d97dc02d7e342ab143a47779d
  languageName: node
  linkType: hard

"@types/koa-compose@npm:*":
  version: 3.2.8
  resolution: "@types/koa-compose@npm:3.2.8"
  dependencies:
    "@types/koa": "*"
  checksum: 95c32bdee738ac7c10439bbf6342ca3b9f0aafd7e8118739eac7fb0fa703a23cfe4c88f63e13a69a16fbde702e0bcdc62b272aa734325fc8efa7e5625479752e
  languageName: node
  linkType: hard

"@types/koa-send@npm:*":
  version: 4.1.6
  resolution: "@types/koa-send@npm:4.1.6"
  dependencies:
    "@types/koa": "*"
  checksum: d46d207f1d826ccd74bf3a02180d0475be8456eb3a2244244d19cb3f1737251e163d73958fdcd12111e03c7c0545cc89e7888a6ef2ba370ebf2b2e804efaaaf1
  languageName: node
  linkType: hard

"@types/koa-static@npm:^4.0.4":
  version: 4.0.4
  resolution: "@types/koa-static@npm:4.0.4"
  dependencies:
    "@types/koa": "*"
    "@types/koa-send": "*"
  checksum: 99087a9b6f4214679932008fbed2d4332fca06cd01f2d333439bd1cf0844c313584c8eb6b805360d1c3d6c6c8a475468a5f4f73ecad551c8cc369e290ad41331
  languageName: node
  linkType: hard

"@types/koa@npm:*, @types/koa@npm:^2.15.0":
  version: 2.15.0
  resolution: "@types/koa@npm:2.15.0"
  dependencies:
    "@types/accepts": "*"
    "@types/content-disposition": "*"
    "@types/cookies": "*"
    "@types/http-assert": "*"
    "@types/http-errors": "*"
    "@types/keygrip": "*"
    "@types/koa-compose": "*"
    "@types/node": "*"
  checksum: f429b92f36f96c8f5ceb5333f982400d0db20e177b7d89a7a576ac6f63aff8c964f7ab313e2e281a07bbb93931c66327fb42614cd4984b2ef33dfe7cbd76d741
  languageName: node
  linkType: hard

"@types/koa__cors@npm:^5.0.0":
  version: 5.0.0
  resolution: "@types/koa__cors@npm:5.0.0"
  dependencies:
    "@types/koa": "*"
  checksum: ad8e6a482f1bb0e357e0051faec328a75e2978a24065a953032d5dba58ac08edf5ca66b03059551f0faf9e085b15ee7892e6ab03c9500af4be8bd258965479c9
  languageName: node
  linkType: hard

"@types/koa__router@npm:^12.0.4":
  version: 12.0.4
  resolution: "@types/koa__router@npm:12.0.4"
  dependencies:
    "@types/koa": "*"
  checksum: 0740e3c8dc3af2d163231ba585d24c3bcc697469a3bd4f042988ca3f61ad9ebc5caa20961a4abf87e92996c97ac3f9fdf9f24925cf834a9a9d21cec10aa11e73
  languageName: node
  linkType: hard

"@types/lodash@npm:4.14.182":
  version: 4.14.182
  resolution: "@types/lodash@npm:4.14.182"
  checksum: 7dd137aa9dbabd632408bd37009d984655164fa1ecc3f2b6eb94afe35bf0a5852cbab6183148d883e9c73a958b7fec9a9bcf7c8e45d41195add6a18c34958209
  languageName: node
  linkType: hard

"@types/mime@npm:^1":
  version: 1.3.5
  resolution: "@types/mime@npm:1.3.5"
  checksum: e29a5f9c4776f5229d84e525b7cd7dd960b51c30a0fb9a028c0821790b82fca9f672dab56561e2acd9e8eed51d431bde52eafdfef30f643586c4162f1aecfc78
  languageName: node
  linkType: hard

"@types/mute-stream@npm:^0.0.4":
  version: 0.0.4
  resolution: "@types/mute-stream@npm:0.0.4"
  dependencies:
    "@types/node": "*"
  checksum: af8d83ad7b68ea05d9357985daf81b6c9b73af4feacb2f5c2693c7fd3e13e5135ef1bd083ce8d5bdc8e97acd28563b61bb32dec4e4508a8067fcd31b8a098632
  languageName: node
  linkType: hard

"@types/node-fetch@npm:^2.6.4":
  version: 2.6.12
  resolution: "@types/node-fetch@npm:2.6.12"
  dependencies:
    "@types/node": "*"
    form-data: ^4.0.0
  checksum: 9647e68f9a125a090220c38d77b3c8e669c488658ae7506f1b4f9568214beba087624b1705bba1dc76649a65281ce3fd5b400e15266cbef8088027fb88777557
  languageName: node
  linkType: hard

"@types/node@npm:*":
  version: 22.10.2
  resolution: "@types/node@npm:22.10.2"
  dependencies:
    undici-types: ~6.20.0
  checksum: b22401e6e7d1484e437d802c72f5560e18100b1257b9ad0574d6fe05bebe4dbcb620ea68627d1f1406775070d29ace8b6b51f57e7b1c7b8bafafe6da7f29c843
  languageName: node
  linkType: hard

"@types/node@npm:^18.11.18":
  version: 18.19.68
  resolution: "@types/node@npm:18.19.68"
  dependencies:
    undici-types: ~5.26.4
  checksum: 84e1cd61b719405aa3b9cc42fbdd8821696684150be04cbd35ebed3b92363a83e904cd89dec5b50dd6a8ff0ea9f26c60436109900b485dbd5b93a81fd6374ccb
  languageName: node
  linkType: hard

"@types/node@npm:^20.10.7":
  version: 20.17.10
  resolution: "@types/node@npm:20.17.10"
  dependencies:
    undici-types: ~6.19.2
  checksum: 44cfa7cd9a4ebb8f74efa4b89cf963ca0e522121a7d24d8121d40872bbcfd607eaccdc203c4fe92c8b587125be9ca7b071fe4f9b356f263434b8a8512dbebef0
  languageName: node
  linkType: hard

"@types/qs@npm:*":
  version: 6.9.17
  resolution: "@types/qs@npm:6.9.17"
  checksum: fc3beda0be70e820ddabaa361e8dfec5e09b482b8f6cf1515615479a027dd06cd5ba0ffbd612b654c2605523f45f484c8905a475623d6cd0c4cadcf5d0c517f5
  languageName: node
  linkType: hard

"@types/range-parser@npm:*":
  version: 1.2.7
  resolution: "@types/range-parser@npm:1.2.7"
  checksum: 95640233b689dfbd85b8c6ee268812a732cf36d5affead89e806fe30da9a430767af8ef2cd661024fd97e19d61f3dec75af2df5e80ec3bea000019ab7028629a
  languageName: node
  linkType: hard

"@types/responselike@npm:^1.0.0":
  version: 1.0.3
  resolution: "@types/responselike@npm:1.0.3"
  dependencies:
    "@types/node": "*"
  checksum: 6ac4b35723429b11b117e813c7acc42c3af8b5554caaf1fc750404c1ae59f9b7376bc69b9e9e194a5a97357a597c2228b7173d317320f0360d617b6425212f58
  languageName: node
  linkType: hard

"@types/send@npm:*":
  version: 0.17.4
  resolution: "@types/send@npm:0.17.4"
  dependencies:
    "@types/mime": ^1
    "@types/node": "*"
  checksum: cf4db48251bbb03cd6452b4de6e8e09e2d75390a92fd798eca4a803df06444adc94ed050246c94c7ed46fb97be1f63607f0e1f13c3ce83d71788b3e08640e5e0
  languageName: node
  linkType: hard

"@types/serve-static@npm:*":
  version: 1.15.7
  resolution: "@types/serve-static@npm:1.15.7"
  dependencies:
    "@types/http-errors": "*"
    "@types/node": "*"
    "@types/send": "*"
  checksum: bbbf00dbd84719da2250a462270dc68964006e8d62f41fe3741abd94504ba3688f420a49afb2b7478921a1544d3793183ffa097c5724167da777f4e0c7f1a7d6
  languageName: node
  linkType: hard

"@types/sortablejs@npm:^1.15.1":
  version: 1.15.8
  resolution: "@types/sortablejs@npm:1.15.8"
  checksum: 7866c134a6c81cfc438af52b7910a3efdd89e49c5d196f91a90decea2644c40ab24bbae929d32abb70093ef00a5a6211fe6eb956b618478bc0c81a1dcc59e887
  languageName: node
  linkType: hard

"@types/tinycolor2@npm:^1.4.3":
  version: 1.4.6
  resolution: "@types/tinycolor2@npm:1.4.6"
  checksum: 50179851b32dcf78e0b4b691350a61043d8a00c3a8176d8155a257b34bb937136ff5d7950c814dca3c832673c7b6dc835f89dd154e85ccec3a4d4eac3993ed84
  languageName: node
  linkType: hard

"@types/triple-beam@npm:^1.3.2":
  version: 1.3.5
  resolution: "@types/triple-beam@npm:1.3.5"
  checksum: 519b6a1b30d4571965c9706ad5400a200b94e4050feca3e7856e3ea7ac00ec9903e32e9a10e2762d0f7e472d5d03e5f4b29c16c0bd8c1f77c8876c683b2231f1
  languageName: node
  linkType: hard

"@types/validator@npm:^13.7.17":
  version: 13.12.2
  resolution: "@types/validator@npm:13.12.2"
  checksum: 4e989f76e155a93a94f53c2362d5695f0a95fb6f36e05f215b1af893e1dc70a7db2d8422c9a0c14dadb4fd3c32a7698c86bce3b81ff99116c8c7f21888875a2f
  languageName: node
  linkType: hard

"@types/wrap-ansi@npm:^3.0.0":
  version: 3.0.0
  resolution: "@types/wrap-ansi@npm:3.0.0"
  checksum: 492f0610093b5802f45ca292777679bb9b381f1f32ae939956dd9e00bf81dba7cc99979687620a2817d9a7d8b59928207698166c47a0861c6a2e5c30d4aaf1e9
  languageName: node
  linkType: hard

"@typescript-eslint/eslint-plugin@npm:7.18.0, @typescript-eslint/eslint-plugin@npm:^7.0.2":
  version: 7.18.0
  resolution: "@typescript-eslint/eslint-plugin@npm:7.18.0"
  dependencies:
    "@eslint-community/regexpp": ^4.10.0
    "@typescript-eslint/scope-manager": 7.18.0
    "@typescript-eslint/type-utils": 7.18.0
    "@typescript-eslint/utils": 7.18.0
    "@typescript-eslint/visitor-keys": 7.18.0
    graphemer: ^1.4.0
    ignore: ^5.3.1
    natural-compare: ^1.4.0
    ts-api-utils: ^1.3.0
  peerDependencies:
    "@typescript-eslint/parser": ^7.0.0
    eslint: ^8.56.0
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: dfcf150628ca2d4ccdfc20b46b0eae075c2f16ef5e70d9d2f0d746acf4c69a09f962b93befee01a529f14bbeb3e817b5aba287d7dd0edc23396bc5ed1f448c3d
  languageName: node
  linkType: hard

"@typescript-eslint/parser@npm:7.18.0, @typescript-eslint/parser@npm:^7.0.2":
  version: 7.18.0
  resolution: "@typescript-eslint/parser@npm:7.18.0"
  dependencies:
    "@typescript-eslint/scope-manager": 7.18.0
    "@typescript-eslint/types": 7.18.0
    "@typescript-eslint/typescript-estree": 7.18.0
    "@typescript-eslint/visitor-keys": 7.18.0
    debug: ^4.3.4
  peerDependencies:
    eslint: ^8.56.0
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 132b56ac3b2d90b588d61d005a70f6af322860974225b60201cbf45abf7304d67b7d8a6f0ade1c188ac4e339884e78d6dcd450417f1481998f9ddd155bab0801
  languageName: node
  linkType: hard

"@typescript-eslint/scope-manager@npm:7.18.0":
  version: 7.18.0
  resolution: "@typescript-eslint/scope-manager@npm:7.18.0"
  dependencies:
    "@typescript-eslint/types": 7.18.0
    "@typescript-eslint/visitor-keys": 7.18.0
  checksum: b982c6ac13d8c86bb3b949c6b4e465f3f60557c2ccf4cc229799827d462df56b9e4d3eaed7711d79b875422fc3d71ec1ebcb5195db72134d07c619e3c5506b57
  languageName: node
  linkType: hard

"@typescript-eslint/type-utils@npm:7.18.0":
  version: 7.18.0
  resolution: "@typescript-eslint/type-utils@npm:7.18.0"
  dependencies:
    "@typescript-eslint/typescript-estree": 7.18.0
    "@typescript-eslint/utils": 7.18.0
    debug: ^4.3.4
    ts-api-utils: ^1.3.0
  peerDependencies:
    eslint: ^8.56.0
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 68fd5df5146c1a08cde20d59b4b919acab06a1b06194fe4f7ba1b928674880249890785fbbc97394142f2ef5cff5a7fba9b8a940449e7d5605306505348e38bc
  languageName: node
  linkType: hard

"@typescript-eslint/types@npm:7.18.0":
  version: 7.18.0
  resolution: "@typescript-eslint/types@npm:7.18.0"
  checksum: 7df2750cd146a0acd2d843208d69f153b458e024bbe12aab9e441ad2c56f47de3ddfeb329c4d1ea0079e2577fea4b8c1c1ce15315a8d49044586b04fedfe7a4d
  languageName: node
  linkType: hard

"@typescript-eslint/typescript-estree@npm:7.18.0":
  version: 7.18.0
  resolution: "@typescript-eslint/typescript-estree@npm:7.18.0"
  dependencies:
    "@typescript-eslint/types": 7.18.0
    "@typescript-eslint/visitor-keys": 7.18.0
    debug: ^4.3.4
    globby: ^11.1.0
    is-glob: ^4.0.3
    minimatch: ^9.0.4
    semver: ^7.6.0
    ts-api-utils: ^1.3.0
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: c82d22ec9654973944f779eb4eb94c52f4a6eafaccce2f0231ff7757313f3a0d0256c3252f6dfe6d43f57171d09656478acb49a629a9d0c193fb959bc3f36116
  languageName: node
  linkType: hard

"@typescript-eslint/utils@npm:7.18.0":
  version: 7.18.0
  resolution: "@typescript-eslint/utils@npm:7.18.0"
  dependencies:
    "@eslint-community/eslint-utils": ^4.4.0
    "@typescript-eslint/scope-manager": 7.18.0
    "@typescript-eslint/types": 7.18.0
    "@typescript-eslint/typescript-estree": 7.18.0
  peerDependencies:
    eslint: ^8.56.0
  checksum: 751dbc816dab8454b7dc6b26a56671dbec08e3f4ef94c2661ce1c0fc48fa2d05a64e03efe24cba2c22d03ba943cd3c5c7a5e1b7b03bbb446728aec1c640bd767
  languageName: node
  linkType: hard

"@typescript-eslint/visitor-keys@npm:7.18.0":
  version: 7.18.0
  resolution: "@typescript-eslint/visitor-keys@npm:7.18.0"
  dependencies:
    "@typescript-eslint/types": 7.18.0
    eslint-visitor-keys: ^3.4.3
  checksum: 6e806a7cdb424c5498ea187a5a11d0fef7e4602a631be413e7d521e5aec1ab46ba00c76cfb18020adaa0a8c9802354a163bfa0deb74baa7d555526c7517bb158
  languageName: node
  linkType: hard

"@ungap/structured-clone@npm:^1.2.0":
  version: 1.2.1
  resolution: "@ungap/structured-clone@npm:1.2.1"
  checksum: 1e3b9fef293118861f0b2159b3695fc7f3793c0707095888ebb3ac7183f78c390e68f04cd4b4cf9ac979ae0da454505e08b3aae887cdd639609a3fe529e19e59
  languageName: node
  linkType: hard

"@vitejs/plugin-vue-jsx@npm:^3.1.0":
  version: 3.1.0
  resolution: "@vitejs/plugin-vue-jsx@npm:3.1.0"
  dependencies:
    "@babel/core": ^7.23.3
    "@babel/plugin-transform-typescript": ^7.23.3
    "@vue/babel-plugin-jsx": ^1.1.5
  peerDependencies:
    vite: ^4.0.0 || ^5.0.0
    vue: ^3.0.0
  checksum: 46312a3013346f268fd75905855ac3bd2f75d779840bbf7abbb2f17dec9c9f9cc3b4a6d2257b3277eb4ad2541db6dd264579b60e1fca631bbe62778896a9ea54
  languageName: node
  linkType: hard

"@vitejs/plugin-vue@npm:^5.0.4":
  version: 5.2.1
  resolution: "@vitejs/plugin-vue@npm:5.2.1"
  peerDependencies:
    vite: ^5.0.0 || ^6.0.0
    vue: ^3.2.25
  checksum: 4f3add595b9951d1e5b458765fa862647252a3c36a0fdc2b0704ef1d6ab4a447a63c5f646e1a9218a5b24a5d2c00d1cd00a70f2948d6cdfbf9ca6f286bd7f61b
  languageName: node
  linkType: hard

"@volar/language-core@npm:2.4.10, @volar/language-core@npm:~2.4.8":
  version: 2.4.10
  resolution: "@volar/language-core@npm:2.4.10"
  dependencies:
    "@volar/source-map": 2.4.10
  checksum: 463f2fb87d2cad1efbbe6c2cb06060c1285ef6fc35ee41fe1927a584d830629dd9c21d780349526350691b7230711ffcf088ad9836005f0a7e0a0ceb94dfd640
  languageName: node
  linkType: hard

"@volar/source-map@npm:2.4.10":
  version: 2.4.10
  resolution: "@volar/source-map@npm:2.4.10"
  checksum: f52cde8467a3bfd63d00b76383d39d12e6c510da28d45f561ad54f0f3ffd7cb362e0469449d26f310cffec960d99ebe91d356cd5ca72bb15e337c25f787d7da9
  languageName: node
  linkType: hard

"@volar/typescript@npm:~2.4.8":
  version: 2.4.10
  resolution: "@volar/typescript@npm:2.4.10"
  dependencies:
    "@volar/language-core": 2.4.10
    path-browserify: ^1.0.1
    vscode-uri: ^3.0.8
  checksum: d55982932c53aab4ad1f6399a5d0a80ae266ae8ad1243280ef1f21da49aabf1cea34c3390db18f789caaaa30fe2fafd4f3f3356d0e4266dd15225241aac61065
  languageName: node
  linkType: hard

"@vue/babel-helper-vue-transform-on@npm:1.2.5":
  version: 1.2.5
  resolution: "@vue/babel-helper-vue-transform-on@npm:1.2.5"
  checksum: e6372e22538e09446848e9deed8b64ef72de9a7081d5233b8bf0a1cc5db205683cbbdd0f2c6fd00cc303bac553042af5938e8b1609a0301bf9521b6cf9370894
  languageName: node
  linkType: hard

"@vue/babel-plugin-jsx@npm:^1.1.5":
  version: 1.2.5
  resolution: "@vue/babel-plugin-jsx@npm:1.2.5"
  dependencies:
    "@babel/helper-module-imports": ^7.24.7
    "@babel/helper-plugin-utils": ^7.24.8
    "@babel/plugin-syntax-jsx": ^7.24.7
    "@babel/template": ^7.25.0
    "@babel/traverse": ^7.25.6
    "@babel/types": ^7.25.6
    "@vue/babel-helper-vue-transform-on": 1.2.5
    "@vue/babel-plugin-resolve-type": 1.2.5
    html-tags: ^3.3.1
    svg-tags: ^1.0.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  peerDependenciesMeta:
    "@babel/core":
      optional: true
  checksum: 5feba1bbe3d684dc4eec583077fa80012c2fa7abf23ed3c897b04c69e98aa016f3739ea89391a3cde22cdfae018fc2636467a5d9385834cdbe20f12352e10df4
  languageName: node
  linkType: hard

"@vue/babel-plugin-resolve-type@npm:1.2.5":
  version: 1.2.5
  resolution: "@vue/babel-plugin-resolve-type@npm:1.2.5"
  dependencies:
    "@babel/code-frame": ^7.24.7
    "@babel/helper-module-imports": ^7.24.7
    "@babel/helper-plugin-utils": ^7.24.8
    "@babel/parser": ^7.25.6
    "@vue/compiler-sfc": ^3.5.3
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 42311700c7afa1a94903ef44286fc178428876e95432b79d916b32545d07f3890c71b256ab9b5d1abe620e0b6c9b35f66a28f061f7d35294e647859c7aa7bacd
  languageName: node
  linkType: hard

"@vue/compiler-core@npm:3.5.13":
  version: 3.5.13
  resolution: "@vue/compiler-core@npm:3.5.13"
  dependencies:
    "@babel/parser": ^7.25.3
    "@vue/shared": 3.5.13
    entities: ^4.5.0
    estree-walker: ^2.0.2
    source-map-js: ^1.2.0
  checksum: 9c67d4bcf2bcd758e45778f1d75efcf681154be1c13c5cb1c0b78c77373277a7f6bd69a3b816c17fa157316b989421d420a8d5af4915e89049a27dc7a6d97bcb
  languageName: node
  linkType: hard

"@vue/compiler-dom@npm:3.5.13, @vue/compiler-dom@npm:^3.5.0":
  version: 3.5.13
  resolution: "@vue/compiler-dom@npm:3.5.13"
  dependencies:
    "@vue/compiler-core": 3.5.13
    "@vue/shared": 3.5.13
  checksum: 8711fd205613829d685c5969b4ef313ff2ebba54f69b59274f0398424c0ea02ddacf51d450dd653ddbd33c9891bd42955ef8e677c58640535723673adfcf54b8
  languageName: node
  linkType: hard

"@vue/compiler-sfc@npm:3.5.13, @vue/compiler-sfc@npm:^3.5.3":
  version: 3.5.13
  resolution: "@vue/compiler-sfc@npm:3.5.13"
  dependencies:
    "@babel/parser": ^7.25.3
    "@vue/compiler-core": 3.5.13
    "@vue/compiler-dom": 3.5.13
    "@vue/compiler-ssr": 3.5.13
    "@vue/shared": 3.5.13
    estree-walker: ^2.0.2
    magic-string: ^0.30.11
    postcss: ^8.4.48
    source-map-js: ^1.2.0
  checksum: c1c03c9c19c839cf4721748dec50e2004b2f3ebe7eef2a30f3f473f4dfe386d5a04573e46d5c5c606d8411f124d28383580ae14dfc8e489e39b2a5121ce5933d
  languageName: node
  linkType: hard

"@vue/compiler-ssr@npm:3.5.13":
  version: 3.5.13
  resolution: "@vue/compiler-ssr@npm:3.5.13"
  dependencies:
    "@vue/compiler-dom": 3.5.13
    "@vue/shared": 3.5.13
  checksum: 066d6288a7ba2519ea7d9f97fc04bd140221d7a63e80e404020bfe78d502a31bb0a76381c7fb7beec841f98bd0948f4cfbea58ac53fca052965e6a4ea88af1e7
  languageName: node
  linkType: hard

"@vue/compiler-vue2@npm:^2.7.16":
  version: 2.7.16
  resolution: "@vue/compiler-vue2@npm:2.7.16"
  dependencies:
    de-indent: ^1.0.2
    he: ^1.2.0
  checksum: d6bba6c637838cf515907a7e530ae21d93d6d285c221a991a6b9afd292cc38cd822d043f333883cfe79228c6d150423fdb298df07c202a43a2b49862ecd75bde
  languageName: node
  linkType: hard

"@vue/devtools-api@npm:^6.5.0, @vue/devtools-api@npm:^6.6.3, @vue/devtools-api@npm:^6.6.4":
  version: 6.6.4
  resolution: "@vue/devtools-api@npm:6.6.4"
  checksum: beccd79c7c7794ea511e35581fbbe5411d3c5d63b2418bc3771efc66959966df4cbfc391ff5954028e2728eb0f0e37b41722e1c39fde61295f2814a143d2ef0e
  languageName: node
  linkType: hard

"@vue/language-core@npm:2.1.10":
  version: 2.1.10
  resolution: "@vue/language-core@npm:2.1.10"
  dependencies:
    "@volar/language-core": ~2.4.8
    "@vue/compiler-dom": ^3.5.0
    "@vue/compiler-vue2": ^2.7.16
    "@vue/shared": ^3.5.0
    alien-signals: ^0.2.0
    minimatch: ^9.0.3
    muggle-string: ^0.4.1
    path-browserify: ^1.0.1
  peerDependencies:
    typescript: "*"
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 9b208b54fd8409f740cefb63cbc27f6d67d281836bc1ed2a7c8c9e0f86c7ed0cf6ed27d36cb0e0503f74b3893e7e0de543a7db1de651d70cc5a6cdbb1a196ac1
  languageName: node
  linkType: hard

"@vue/reactivity@npm:3.5.13":
  version: 3.5.13
  resolution: "@vue/reactivity@npm:3.5.13"
  dependencies:
    "@vue/shared": 3.5.13
  checksum: 5c241cf7c62929dfd7a5a68ccdeca921ab245d16a7350a15928536955f8fb7edd8ca5e782b63ce9b6c0271e2aebd6d34cad1578aeb416646bb01cb63274d18e5
  languageName: node
  linkType: hard

"@vue/runtime-core@npm:3.5.13":
  version: 3.5.13
  resolution: "@vue/runtime-core@npm:3.5.13"
  dependencies:
    "@vue/reactivity": 3.5.13
    "@vue/shared": 3.5.13
  checksum: b9c732c95b83d4b8a22b3759b20f9715797926332ff74cc63d588791ef5efaaa6cdb504ab81cd65f1b1b65101800a24c92da2a7bd180a2f1c840ac62eb97fd83
  languageName: node
  linkType: hard

"@vue/runtime-dom@npm:3.5.13":
  version: 3.5.13
  resolution: "@vue/runtime-dom@npm:3.5.13"
  dependencies:
    "@vue/reactivity": 3.5.13
    "@vue/runtime-core": 3.5.13
    "@vue/shared": 3.5.13
    csstype: ^3.1.3
  checksum: c1f18baaa5e0fff2167f1835672ce2d21140ee4c2a8dc5f60686fdc35c361f482823caa90897819768fa4033d22e687b243914073a5471f2450de0adfdea50b6
  languageName: node
  linkType: hard

"@vue/server-renderer@npm:3.5.13":
  version: 3.5.13
  resolution: "@vue/server-renderer@npm:3.5.13"
  dependencies:
    "@vue/compiler-ssr": 3.5.13
    "@vue/shared": 3.5.13
  peerDependencies:
    vue: 3.5.13
  checksum: 58960d73344aeeee574977a85f5c14b28f29223f928b8eb4408bc159ac57a192e39a28f29f825ca9341486931edca7edc018cbda92feac81c8daf11c46339759
  languageName: node
  linkType: hard

"@vue/shared@npm:3.5.13, @vue/shared@npm:^3.5.0":
  version: 3.5.13
  resolution: "@vue/shared@npm:3.5.13"
  checksum: b562499b3f1506fe41d37ecb27af6a35d6585457b6ebc52bd2acae37feea30225280968b36b1121c4ae1056c34d140aa525d9020ae558a4e557445290a31c6a9
  languageName: node
  linkType: hard

"abbrev@npm:^2.0.0":
  version: 2.0.0
  resolution: "abbrev@npm:2.0.0"
  checksum: 0e994ad2aa6575f94670d8a2149afe94465de9cedaaaac364e7fb43a40c3691c980ff74899f682f4ca58fa96b4cbd7421a015d3a6defe43a442117d7821a2f36
  languageName: node
  linkType: hard

"abort-controller@npm:^3.0.0":
  version: 3.0.0
  resolution: "abort-controller@npm:3.0.0"
  dependencies:
    event-target-shim: ^5.0.0
  checksum: 170bdba9b47b7e65906a28c8ce4f38a7a369d78e2271706f020849c1bfe0ee2067d4261df8bbb66eb84f79208fd5b710df759d64191db58cfba7ce8ef9c54b75
  languageName: node
  linkType: hard

"accepts@npm:^1.3.5":
  version: 1.3.8
  resolution: "accepts@npm:1.3.8"
  dependencies:
    mime-types: ~2.1.34
    negotiator: 0.6.3
  checksum: 50c43d32e7b50285ebe84b613ee4a3aa426715a7d131b65b786e2ead0fd76b6b60091b9916d3478a75f11f162628a2139991b6c03ab3f1d9ab7c86075dc8eab4
  languageName: node
  linkType: hard

"acorn-jsx@npm:^5.3.2":
  version: 5.3.2
  resolution: "acorn-jsx@npm:5.3.2"
  peerDependencies:
    acorn: ^6.0.0 || ^7.0.0 || ^8.0.0
  checksum: c3d3b2a89c9a056b205b69530a37b972b404ee46ec8e5b341666f9513d3163e2a4f214a71f4dfc7370f5a9c07472d2fd1c11c91c3f03d093e37637d95da98950
  languageName: node
  linkType: hard

"acorn-walk@npm:^8.1.1":
  version: 8.3.4
  resolution: "acorn-walk@npm:8.3.4"
  dependencies:
    acorn: ^8.11.0
  checksum: 4ff03f42323e7cf90f1683e08606b0f460e1e6ac263d2730e3df91c7665b6f64e696db6ea27ee4bed18c2599569be61f28a8399fa170c611161a348c402ca19c
  languageName: node
  linkType: hard

"acorn@npm:^8.11.0, acorn@npm:^8.14.0, acorn@npm:^8.4.1, acorn@npm:^8.9.0":
  version: 8.14.0
  resolution: "acorn@npm:8.14.0"
  bin:
    acorn: bin/acorn
  checksum: 8755074ba55fff94e84e81c72f1013c2d9c78e973c31231c8ae505a5f966859baf654bddd75046bffd73ce816b149298977fff5077a3033dedba0ae2aad152d4
  languageName: node
  linkType: hard

"agent-base@npm:6":
  version: 6.0.2
  resolution: "agent-base@npm:6.0.2"
  dependencies:
    debug: 4
  checksum: f52b6872cc96fd5f622071b71ef200e01c7c4c454ee68bc9accca90c98cfb39f2810e3e9aa330435835eedc8c23f4f8a15267f67c6e245d2b33757575bdac49d
  languageName: node
  linkType: hard

"agent-base@npm:^7.1.0, agent-base@npm:^7.1.2":
  version: 7.1.3
  resolution: "agent-base@npm:7.1.3"
  checksum: 87bb7ee54f5ecf0ccbfcba0b07473885c43ecd76cb29a8db17d6137a19d9f9cd443a2a7c5fd8a3f24d58ad8145f9eb49116344a66b107e1aeab82cf2383f4753
  languageName: node
  linkType: hard

"agentkeepalive@npm:^4.2.1":
  version: 4.5.0
  resolution: "agentkeepalive@npm:4.5.0"
  dependencies:
    humanize-ms: ^1.2.1
  checksum: 13278cd5b125e51eddd5079f04d6fe0914ac1b8b91c1f3db2c1822f99ac1a7457869068997784342fe455d59daaff22e14fb7b8c3da4e741896e7e31faf92481
  languageName: node
  linkType: hard

"ajv-formats@npm:^2.1.1":
  version: 2.1.1
  resolution: "ajv-formats@npm:2.1.1"
  dependencies:
    ajv: ^8.0.0
  peerDependencies:
    ajv: ^8.0.0
  peerDependenciesMeta:
    ajv:
      optional: true
  checksum: 4a287d937f1ebaad4683249a4c40c0fa3beed30d9ddc0adba04859026a622da0d317851316ea64b3680dc60f5c3c708105ddd5d5db8fe595d9d0207fd19f90b7
  languageName: node
  linkType: hard

"ajv@npm:^6.12.4":
  version: 6.12.6
  resolution: "ajv@npm:6.12.6"
  dependencies:
    fast-deep-equal: ^3.1.1
    fast-json-stable-stringify: ^2.0.0
    json-schema-traverse: ^0.4.1
    uri-js: ^4.2.2
  checksum: 874972efe5c4202ab0a68379481fbd3d1b5d0a7bd6d3cc21d40d3536ebff3352a2a1fabb632d4fd2cc7fe4cbdcd5ed6782084c9bbf7f32a1536d18f9da5007d4
  languageName: node
  linkType: hard

"ajv@npm:^8.0.0, ajv@npm:^8.6.3":
  version: 8.17.1
  resolution: "ajv@npm:8.17.1"
  dependencies:
    fast-deep-equal: ^3.1.3
    fast-uri: ^3.0.1
    json-schema-traverse: ^1.0.0
    require-from-string: ^2.0.2
  checksum: 1797bf242cfffbaf3b870d13565bd1716b73f214bb7ada9a497063aada210200da36e3ed40237285f3255acc4feeae91b1fb183625331bad27da95973f7253d9
  languageName: node
  linkType: hard

"alien-signals@npm:^0.2.0":
  version: 0.2.2
  resolution: "alien-signals@npm:0.2.2"
  checksum: 6d7c0a315380b4de1b98f9448ae6b8071491f24f88e3bed133099b1bd68c5d8d0dd70a9a4565b466907c0feb4fdc378b7ac7265fc711a75041561ff5fbfb2586
  languageName: node
  linkType: hard

"ansi-align@npm:^3.0.0":
  version: 3.0.1
  resolution: "ansi-align@npm:3.0.1"
  dependencies:
    string-width: ^4.1.0
  checksum: 6abfa08f2141d231c257162b15292467081fa49a208593e055c866aa0455b57f3a86b5a678c190c618faa79b4c59e254493099cb700dd9cf2293c6be2c8f5d8d
  languageName: node
  linkType: hard

"ansi-escapes@npm:^4.3.2":
  version: 4.3.2
  resolution: "ansi-escapes@npm:4.3.2"
  dependencies:
    type-fest: ^0.21.3
  checksum: 93111c42189c0a6bed9cdb4d7f2829548e943827ee8479c74d6e0b22ee127b2a21d3f8b5ca57723b8ef78ce011fbfc2784350eb2bde3ccfccf2f575fa8489815
  languageName: node
  linkType: hard

"ansi-regex@npm:^5.0.1":
  version: 5.0.1
  resolution: "ansi-regex@npm:5.0.1"
  checksum: 2aa4bb54caf2d622f1afdad09441695af2a83aa3fe8b8afa581d205e57ed4261c183c4d3877cee25794443fde5876417d859c108078ab788d6af7e4fe52eb66b
  languageName: node
  linkType: hard

"ansi-regex@npm:^6.0.1":
  version: 6.1.0
  resolution: "ansi-regex@npm:6.1.0"
  checksum: 495834a53b0856c02acd40446f7130cb0f8284f4a39afdab20d5dc42b2e198b1196119fe887beed8f9055c4ff2055e3b2f6d4641d0be018cdfb64fedf6fc1aac
  languageName: node
  linkType: hard

"ansi-styles@npm:^4.0.0, ansi-styles@npm:^4.1.0":
  version: 4.3.0
  resolution: "ansi-styles@npm:4.3.0"
  dependencies:
    color-convert: ^2.0.1
  checksum: 513b44c3b2105dd14cc42a19271e80f386466c4be574bccf60b627432f9198571ebf4ab1e4c3ba17347658f4ee1711c163d574248c0c1cdc2d5917a0ad582ec4
  languageName: node
  linkType: hard

"ansi-styles@npm:^6.1.0":
  version: 6.2.1
  resolution: "ansi-styles@npm:6.2.1"
  checksum: ef940f2f0ced1a6347398da88a91da7930c33ecac3c77b72c5905f8b8fe402c52e6fde304ff5347f616e27a742da3f1dc76de98f6866c69251ad0b07a66776d9
  languageName: node
  linkType: hard

"any-promise@npm:^1.0.0":
  version: 1.3.0
  resolution: "any-promise@npm:1.3.0"
  checksum: 0ee8a9bdbe882c90464d75d1f55cf027f5458650c4bd1f0467e65aec38ccccda07ca5844969ee77ed46d04e7dded3eaceb027e8d32f385688523fe305fa7e1de
  languageName: node
  linkType: hard

"anymatch@npm:~3.1.2":
  version: 3.1.3
  resolution: "anymatch@npm:3.1.3"
  dependencies:
    normalize-path: ^3.0.0
    picomatch: ^2.0.4
  checksum: 3e044fd6d1d26545f235a9fe4d7a534e2029d8e59fa7fd9f2a6eb21230f6b5380ea1eaf55136e60cbf8e613544b3b766e7a6fa2102e2a3a117505466e3025dc2
  languageName: node
  linkType: hard

"arch@npm:^2.1.1":
  version: 2.2.0
  resolution: "arch@npm:2.2.0"
  checksum: e21b7635029fe8e9cdd5a026f9a6c659103e63fff423834323cdf836a1bb240a72d0c39ca8c470f84643385cf581bd8eda2cad8bf493e27e54bd9783abe9101f
  languageName: node
  linkType: hard

"arg@npm:^4.1.0":
  version: 4.1.3
  resolution: "arg@npm:4.1.3"
  checksum: 544af8dd3f60546d3e4aff084d451b96961d2267d668670199692f8d054f0415d86fc5497d0e641e91546f0aa920e7c29e5250e99fc89f5552a34b5d93b77f43
  languageName: node
  linkType: hard

"arg@npm:^5.0.2":
  version: 5.0.2
  resolution: "arg@npm:5.0.2"
  checksum: 6c69ada1a9943d332d9e5382393e897c500908d91d5cb735a01120d5f71daf1b339b7b8980cbeaba8fd1afc68e658a739746179e4315a26e8a28951ff9930078
  languageName: node
  linkType: hard

"argparse@npm:^2.0.1":
  version: 2.0.1
  resolution: "argparse@npm:2.0.1"
  checksum: 83644b56493e89a254bae05702abf3a1101b4fa4d0ca31df1c9985275a5a5bd47b3c27b7fa0b71098d41114d8ca000e6ed90cad764b306f8a503665e4d517ced
  languageName: node
  linkType: hard

"array-buffer-byte-length@npm:^1.0.1":
  version: 1.0.1
  resolution: "array-buffer-byte-length@npm:1.0.1"
  dependencies:
    call-bind: ^1.0.5
    is-array-buffer: ^3.0.4
  checksum: 53524e08f40867f6a9f35318fafe467c32e45e9c682ba67b11943e167344d2febc0f6977a17e699b05699e805c3e8f073d876f8bbf1b559ed494ad2cd0fae09e
  languageName: node
  linkType: hard

"array-includes@npm:^3.1.8":
  version: 3.1.8
  resolution: "array-includes@npm:3.1.8"
  dependencies:
    call-bind: ^1.0.7
    define-properties: ^1.2.1
    es-abstract: ^1.23.2
    es-object-atoms: ^1.0.0
    get-intrinsic: ^1.2.4
    is-string: ^1.0.7
  checksum: eb39ba5530f64e4d8acab39297c11c1c5be2a4ea188ab2b34aba5fb7224d918f77717a9d57a3e2900caaa8440e59431bdaf5c974d5212ef65d97f132e38e2d91
  languageName: node
  linkType: hard

"array-union@npm:^2.1.0":
  version: 2.1.0
  resolution: "array-union@npm:2.1.0"
  checksum: 5bee12395cba82da674931df6d0fea23c4aa4660cb3b338ced9f828782a65caa232573e6bf3968f23e0c5eb301764a382cef2f128b170a9dc59de0e36c39f98d
  languageName: node
  linkType: hard

"array.prototype.findlastindex@npm:^1.2.5":
  version: 1.2.5
  resolution: "array.prototype.findlastindex@npm:1.2.5"
  dependencies:
    call-bind: ^1.0.7
    define-properties: ^1.2.1
    es-abstract: ^1.23.2
    es-errors: ^1.3.0
    es-object-atoms: ^1.0.0
    es-shim-unscopables: ^1.0.2
  checksum: 2c81cff2a75deb95bf1ed89b6f5f2bfbfb882211e3b7cc59c3d6b87df774cd9d6b36949a8ae39ac476e092c1d4a4905f5ee11a86a456abb10f35f8211ae4e710
  languageName: node
  linkType: hard

"array.prototype.flat@npm:^1.3.2":
  version: 1.3.2
  resolution: "array.prototype.flat@npm:1.3.2"
  dependencies:
    call-bind: ^1.0.2
    define-properties: ^1.2.0
    es-abstract: ^1.22.1
    es-shim-unscopables: ^1.0.0
  checksum: 5d6b4bf102065fb3f43764bfff6feb3295d372ce89591e6005df3d0ce388527a9f03c909af6f2a973969a4d178ab232ffc9236654149173e0e187ec3a1a6b87b
  languageName: node
  linkType: hard

"array.prototype.flatmap@npm:^1.3.2":
  version: 1.3.2
  resolution: "array.prototype.flatmap@npm:1.3.2"
  dependencies:
    call-bind: ^1.0.2
    define-properties: ^1.2.0
    es-abstract: ^1.22.1
    es-shim-unscopables: ^1.0.0
  checksum: ce09fe21dc0bcd4f30271f8144083aa8c13d4639074d6c8dc82054b847c7fc9a0c97f857491f4da19d4003e507172a78f4bcd12903098adac8b9cd374f734be3
  languageName: node
  linkType: hard

"arraybuffer.prototype.slice@npm:^1.0.3":
  version: 1.0.3
  resolution: "arraybuffer.prototype.slice@npm:1.0.3"
  dependencies:
    array-buffer-byte-length: ^1.0.1
    call-bind: ^1.0.5
    define-properties: ^1.2.1
    es-abstract: ^1.22.3
    es-errors: ^1.2.1
    get-intrinsic: ^1.2.3
    is-array-buffer: ^3.0.4
    is-shared-array-buffer: ^1.0.2
  checksum: 352259cba534dcdd969c92ab002efd2ba5025b2e3b9bead3973150edbdf0696c629d7f4b3f061c5931511e8207bdc2306da614703c820b45dabce39e3daf7e3e
  languageName: node
  linkType: hard

"async@npm:^3.2.3":
  version: 3.2.6
  resolution: "async@npm:3.2.6"
  checksum: ee6eb8cd8a0ab1b58bd2a3ed6c415e93e773573a91d31df9d5ef559baafa9dab37d3b096fa7993e84585cac3697b2af6ddb9086f45d3ac8cae821bb2aab65682
  languageName: node
  linkType: hard

"asynckit@npm:^0.4.0":
  version: 0.4.0
  resolution: "asynckit@npm:0.4.0"
  checksum: 7b78c451df768adba04e2d02e63e2d0bf3b07adcd6e42b4cf665cb7ce899bedd344c69a1dcbce355b5f972d597b25aaa1c1742b52cffd9caccb22f348114f6be
  languageName: node
  linkType: hard

"atomically@npm:^1.7.0":
  version: 1.7.0
  resolution: "atomically@npm:1.7.0"
  checksum: 991153b17334597f93b58e831bea9851e57ed9cd41d8f33991be063f170b5cc8ec7ff8605f3eb95c1d389c2ad651039e9eb8f2b795e24833c2ceb944f347373a
  languageName: node
  linkType: hard

"autoprefixer@npm:^10.4.17":
  version: 10.4.20
  resolution: "autoprefixer@npm:10.4.20"
  dependencies:
    browserslist: ^4.23.3
    caniuse-lite: ^1.0.30001646
    fraction.js: ^4.3.7
    normalize-range: ^0.1.2
    picocolors: ^1.0.1
    postcss-value-parser: ^4.2.0
  peerDependencies:
    postcss: ^8.1.0
  bin:
    autoprefixer: bin/autoprefixer
  checksum: 187cec2ec356631932b212f76dc64f4419c117fdb2fb9eeeb40867d38ba5ca5ba734e6ceefc9e3af4eec8258e60accdf5cbf2b7708798598fde35cdc3de562d6
  languageName: node
  linkType: hard

"available-typed-arrays@npm:^1.0.7":
  version: 1.0.7
  resolution: "available-typed-arrays@npm:1.0.7"
  dependencies:
    possible-typed-array-names: ^1.0.0
  checksum: 1aa3ffbfe6578276996de660848b6e95669d9a95ad149e3dd0c0cda77db6ee1dbd9d1dd723b65b6d277b882dd0c4b91a654ae9d3cf9e1254b7e93e4908d78fd3
  languageName: node
  linkType: hard

"balanced-match@npm:^1.0.0":
  version: 1.0.2
  resolution: "balanced-match@npm:1.0.2"
  checksum: 9706c088a283058a8a99e0bf91b0a2f75497f185980d9ffa8b304de1d9e58ebda7c72c07ebf01dadedaac5b2907b2c6f566f660d62bd336c3468e960403b9d65
  languageName: node
  linkType: hard

"base64-js@npm:^1.3.1":
  version: 1.5.1
  resolution: "base64-js@npm:1.5.1"
  checksum: 669632eb3745404c2f822a18fc3a0122d2f9a7a13f7fb8b5823ee19d1d2ff9ee5b52c53367176ea4ad093c332fd5ab4bd0ebae5a8e27917a4105a4cfc86b1005
  languageName: node
  linkType: hard

"bignumber.js@npm:^9.0.0":
  version: 9.1.2
  resolution: "bignumber.js@npm:9.1.2"
  checksum: 582c03af77ec9cb0ebd682a373ee6c66475db94a4325f92299621d544aa4bd45cb45fd60001610e94aef8ae98a0905fa538241d9638d4422d57abbeeac6fadaf
  languageName: node
  linkType: hard

"binary-extensions@npm:^2.0.0":
  version: 2.3.0
  resolution: "binary-extensions@npm:2.3.0"
  checksum: bcad01494e8a9283abf18c1b967af65ee79b0c6a9e6fcfafebfe91dbe6e0fc7272bafb73389e198b310516ae04f7ad17d79aacf6cb4c0d5d5202a7e2e52c7d98
  languageName: node
  linkType: hard

"bl@npm:^4.1.0":
  version: 4.1.0
  resolution: "bl@npm:4.1.0"
  dependencies:
    buffer: ^5.5.0
    inherits: ^2.0.4
    readable-stream: ^3.4.0
  checksum: 9e8521fa7e83aa9427c6f8ccdcba6e8167ef30cc9a22df26effcc5ab682ef91d2cbc23a239f945d099289e4bbcfae7a192e9c28c84c6202e710a0dfec3722662
  languageName: node
  linkType: hard

"boolbase@npm:^1.0.0":
  version: 1.0.0
  resolution: "boolbase@npm:1.0.0"
  checksum: 3e25c80ef626c3a3487c73dbfc70ac322ec830666c9ad915d11b701142fab25ec1e63eff2c450c74347acfd2de854ccde865cd79ef4db1683f7c7b046ea43bb0
  languageName: node
  linkType: hard

"boxen@npm:^5.0.0":
  version: 5.1.2
  resolution: "boxen@npm:5.1.2"
  dependencies:
    ansi-align: ^3.0.0
    camelcase: ^6.2.0
    chalk: ^4.1.0
    cli-boxes: ^2.2.1
    string-width: ^4.2.2
    type-fest: ^0.20.2
    widest-line: ^3.1.0
    wrap-ansi: ^7.0.0
  checksum: 82d03e42a72576ff235123f17b7c505372fe05c83f75f61e7d4fa4bcb393897ec95ce766fecb8f26b915f0f7a7227d66e5ec7cef43f5b2bd9d3aeed47ec55877
  languageName: node
  linkType: hard

"brace-expansion@npm:^1.1.7":
  version: 1.1.11
  resolution: "brace-expansion@npm:1.1.11"
  dependencies:
    balanced-match: ^1.0.0
    concat-map: 0.0.1
  checksum: faf34a7bb0c3fcf4b59c7808bc5d2a96a40988addf2e7e09dfbb67a2251800e0d14cd2bfc1aa79174f2f5095c54ff27f46fb1289fe2d77dac755b5eb3434cc07
  languageName: node
  linkType: hard

"brace-expansion@npm:^2.0.1":
  version: 2.0.1
  resolution: "brace-expansion@npm:2.0.1"
  dependencies:
    balanced-match: ^1.0.0
  checksum: a61e7cd2e8a8505e9f0036b3b6108ba5e926b4b55089eeb5550cd04a471fe216c96d4fe7e4c7f995c728c554ae20ddfc4244cad10aef255e72b62930afd233d1
  languageName: node
  linkType: hard

"braces@npm:^3.0.3, braces@npm:~3.0.2":
  version: 3.0.3
  resolution: "braces@npm:3.0.3"
  dependencies:
    fill-range: ^7.1.1
  checksum: b95aa0b3bd909f6cd1720ffcf031aeaf46154dd88b4da01f9a1d3f7ea866a79eba76a6d01cbc3c422b2ee5cdc39a4f02491058d5df0d7bf6e6a162a832df1f69
  languageName: node
  linkType: hard

"browserslist@npm:^4.23.3, browserslist@npm:^4.24.0":
  version: 4.24.3
  resolution: "browserslist@npm:4.24.3"
  dependencies:
    caniuse-lite: ^1.0.30001688
    electron-to-chromium: ^1.5.73
    node-releases: ^2.0.19
    update-browserslist-db: ^1.1.1
  bin:
    browserslist: cli.js
  checksum: 016efc9953350e3a7212edcfdd72210cb33b339c1a974a77c0715eb67d23d7e5cd0a073ce1c801ab09235d8c213425ca51b92d41bbb829b833872b45f885fe7c
  languageName: node
  linkType: hard

"buffer@npm:^5.5.0":
  version: 5.7.1
  resolution: "buffer@npm:5.7.1"
  dependencies:
    base64-js: ^1.3.1
    ieee754: ^1.1.13
  checksum: e2cf8429e1c4c7b8cbd30834ac09bd61da46ce35f5c22a78e6c2f04497d6d25541b16881e30a019c6fd3154150650ccee27a308eff3e26229d788bbdeb08ab84
  languageName: node
  linkType: hard

"bufferutil@npm:^4.0.1":
  version: 4.0.8
  resolution: "bufferutil@npm:4.0.8"
  dependencies:
    node-gyp: latest
    node-gyp-build: ^4.3.0
  checksum: 7e9a46f1867dca72fda350966eb468eca77f4d623407b0650913fadf73d5750d883147d6e5e21c56f9d3b0bdc35d5474e80a600b9f31ec781315b4d2469ef087
  languageName: node
  linkType: hard

"builtin-modules@npm:^3.3.0":
  version: 3.3.0
  resolution: "builtin-modules@npm:3.3.0"
  checksum: db021755d7ed8be048f25668fe2117620861ef6703ea2c65ed2779c9e3636d5c3b82325bd912244293959ff3ae303afa3471f6a15bf5060c103e4cc3a839749d
  languageName: node
  linkType: hard

"builtins@npm:^5.0.1":
  version: 5.1.0
  resolution: "builtins@npm:5.1.0"
  dependencies:
    semver: ^7.0.0
  checksum: 76327fa85b8e253b26e52f79988148013ea742691b4ab15f7228ebee47dd757832da308c9d4e4fc89763a1773e3f25a9836fff6315df85c7c6c72190436bf11d
  languageName: node
  linkType: hard

"bytes@npm:3.1.2":
  version: 3.1.2
  resolution: "bytes@npm:3.1.2"
  checksum: e4bcd3948d289c5127591fbedf10c0b639ccbf00243504e4e127374a15c3bc8eed0d28d4aaab08ff6f1cf2abc0cce6ba3085ed32f4f90e82a5683ce0014e1b6e
  languageName: node
  linkType: hard

"cacache@npm:^19.0.1":
  version: 19.0.1
  resolution: "cacache@npm:19.0.1"
  dependencies:
    "@npmcli/fs": ^4.0.0
    fs-minipass: ^3.0.0
    glob: ^10.2.2
    lru-cache: ^10.0.1
    minipass: ^7.0.3
    minipass-collect: ^2.0.1
    minipass-flush: ^1.0.5
    minipass-pipeline: ^1.2.4
    p-map: ^7.0.2
    ssri: ^12.0.0
    tar: ^7.4.3
    unique-filename: ^4.0.0
  checksum: e95684717de6881b4cdaa949fa7574e3171946421cd8291769dd3d2417dbf7abf4aa557d1f968cca83dcbc95bed2a281072b09abfc977c942413146ef7ed4525
  languageName: node
  linkType: hard

"cache-content-type@npm:^1.0.0":
  version: 1.0.1
  resolution: "cache-content-type@npm:1.0.1"
  dependencies:
    mime-types: ^2.1.18
    ylru: ^1.2.0
  checksum: 18db4d59452669ccbfd7146a1510a37eb28e9eccf18ca7a4eb603dff2edc5cccdca7498fc3042a2978f76f11151fba486eb9eb69d9afa3fb124957870aef4fd3
  languageName: node
  linkType: hard

"cache-manager@npm:^5.4.0":
  version: 5.7.6
  resolution: "cache-manager@npm:5.7.6"
  dependencies:
    eventemitter3: ^5.0.1
    lodash.clonedeep: ^4.5.0
    lru-cache: ^10.2.2
    promise-coalesce: ^1.1.2
  checksum: c61d30e2824f99f04af8e14a2e98c2274daa276cce82f64f0ac4c4bd59cf30e1ad9dec4028148206ad729196b242940476602e2f12496366d6f3f4b9da7c24c5
  languageName: node
  linkType: hard

"cacheable-request@npm:^6.0.0":
  version: 6.1.0
  resolution: "cacheable-request@npm:6.1.0"
  dependencies:
    clone-response: ^1.0.2
    get-stream: ^5.1.0
    http-cache-semantics: ^4.0.0
    keyv: ^3.0.0
    lowercase-keys: ^2.0.0
    normalize-url: ^4.1.0
    responselike: ^1.0.2
  checksum: b510b237b18d17e89942e9ee2d2a077cb38db03f12167fd100932dfa8fc963424bfae0bfa1598df4ae16c944a5484e43e03df8f32105b04395ee9495e9e4e9f1
  languageName: node
  linkType: hard

"call-bind-apply-helpers@npm:^1.0.0, call-bind-apply-helpers@npm:^1.0.1":
  version: 1.0.1
  resolution: "call-bind-apply-helpers@npm:1.0.1"
  dependencies:
    es-errors: ^1.3.0
    function-bind: ^1.1.2
  checksum: 3c55343261bb387c58a4762d15ad9d42053659a62681ec5eb50690c6b52a4a666302a01d557133ce6533e8bd04530ee3b209f23dd06c9577a1925556f8fcccdf
  languageName: node
  linkType: hard

"call-bind@npm:^1.0.2, call-bind@npm:^1.0.5, call-bind@npm:^1.0.6, call-bind@npm:^1.0.7, call-bind@npm:^1.0.8":
  version: 1.0.8
  resolution: "call-bind@npm:1.0.8"
  dependencies:
    call-bind-apply-helpers: ^1.0.0
    es-define-property: ^1.0.0
    get-intrinsic: ^1.2.4
    set-function-length: ^1.2.2
  checksum: aa2899bce917a5392fd73bd32e71799c37c0b7ab454e0ed13af7f6727549091182aade8bbb7b55f304a5bc436d543241c14090fb8a3137e9875e23f444f4f5a9
  languageName: node
  linkType: hard

"call-bound@npm:^1.0.2":
  version: 1.0.2
  resolution: "call-bound@npm:1.0.2"
  dependencies:
    call-bind: ^1.0.8
    get-intrinsic: ^1.2.5
  checksum: 6f0020b8848307446ed410d0dcf40470b3c16d96a26d8d8bba2ea5bc1582f9bdfc49945cf270698495d4c0f422ad56d243a336855cfb0260feabf72952238cf8
  languageName: node
  linkType: hard

"callsites@npm:^3.0.0":
  version: 3.1.0
  resolution: "callsites@npm:3.1.0"
  checksum: 072d17b6abb459c2ba96598918b55868af677154bec7e73d222ef95a8fdb9bbf7dae96a8421085cdad8cd190d86653b5b6dc55a4484f2e5b2e27d5e0c3fc15b3
  languageName: node
  linkType: hard

"camelcase-css@npm:^2.0.1":
  version: 2.0.1
  resolution: "camelcase-css@npm:2.0.1"
  checksum: 1cec2b3b3dcb5026688a470b00299a8db7d904c4802845c353dbd12d9d248d3346949a814d83bfd988d4d2e5b9904c07efe76fecd195a1d4f05b543e7c0b56b1
  languageName: node
  linkType: hard

"camelcase@npm:^6.2.0":
  version: 6.3.0
  resolution: "camelcase@npm:6.3.0"
  checksum: 8c96818a9076434998511251dcb2761a94817ea17dbdc37f47ac080bd088fc62c7369429a19e2178b993497132c8cbcf5cc1f44ba963e76782ba469c0474938d
  languageName: node
  linkType: hard

"caniuse-lite@npm:^1.0.30001646, caniuse-lite@npm:^1.0.30001688":
  version: 1.0.30001688
  resolution: "caniuse-lite@npm:1.0.30001688"
  checksum: b48109e337f924a969ad3505d81cde32624b598f3ff67047dbd69a9bed59672cea37b6095c3a876174511447be5e356d87acac6c859d941572e57c220978e241
  languageName: node
  linkType: hard

"chalk@npm:^4.0.0, chalk@npm:^4.1.0, chalk@npm:^4.1.2":
  version: 4.1.2
  resolution: "chalk@npm:4.1.2"
  dependencies:
    ansi-styles: ^4.1.0
    supports-color: ^7.1.0
  checksum: fe75c9d5c76a7a98d45495b91b2172fa3b7a09e0cc9370e5c8feb1c567b85c4288e2b3fded7cfdd7359ac28d6b3844feb8b82b8686842e93d23c827c417e83fc
  languageName: node
  linkType: hard

"chardet@npm:^0.7.0":
  version: 0.7.0
  resolution: "chardet@npm:0.7.0"
  checksum: 6fd5da1f5d18ff5712c1e0aed41da200d7c51c28f11b36ee3c7b483f3696dabc08927fc6b227735eb8f0e1215c9a8abd8154637f3eff8cada5959df7f58b024d
  languageName: node
  linkType: hard

"cheerio-select@npm:^2.1.0":
  version: 2.1.0
  resolution: "cheerio-select@npm:2.1.0"
  dependencies:
    boolbase: ^1.0.0
    css-select: ^5.1.0
    css-what: ^6.1.0
    domelementtype: ^2.3.0
    domhandler: ^5.0.3
    domutils: ^3.0.1
  checksum: 843d6d479922f28a6c5342c935aff1347491156814de63c585a6eb73baf7bb4185c1b4383a1195dca0f12e3946d737c7763bcef0b9544c515d905c5c44c5308b
  languageName: node
  linkType: hard

"cheerio@npm:^1.0.0":
  version: 1.0.0
  resolution: "cheerio@npm:1.0.0"
  dependencies:
    cheerio-select: ^2.1.0
    dom-serializer: ^2.0.0
    domhandler: ^5.0.3
    domutils: ^3.1.0
    encoding-sniffer: ^0.2.0
    htmlparser2: ^9.1.0
    parse5: ^7.1.2
    parse5-htmlparser2-tree-adapter: ^7.0.0
    parse5-parser-stream: ^7.1.2
    undici: ^6.19.5
    whatwg-mimetype: ^4.0.0
  checksum: ade4344811dcad5b5d78392506ef6bab1900c13a65222c869e745a38370d287f4b94838ac6d752883a84d937edb62b5bd0deaf70e6f38054acbfe3da4881574a
  languageName: node
  linkType: hard

"chokidar@npm:^3.5.2, chokidar@npm:^3.5.3, chokidar@npm:^3.6.0":
  version: 3.6.0
  resolution: "chokidar@npm:3.6.0"
  dependencies:
    anymatch: ~3.1.2
    braces: ~3.0.2
    fsevents: ~2.3.2
    glob-parent: ~5.1.2
    is-binary-path: ~2.1.0
    is-glob: ~4.0.1
    normalize-path: ~3.0.0
    readdirp: ~3.6.0
  dependenciesMeta:
    fsevents:
      optional: true
  checksum: d2f29f499705dcd4f6f3bbed79a9ce2388cf530460122eed3b9c48efeab7a4e28739c6551fd15bec9245c6b9eeca7a32baa64694d64d9b6faeb74ddb8c4a413d
  languageName: node
  linkType: hard

"chownr@npm:^3.0.0":
  version: 3.0.0
  resolution: "chownr@npm:3.0.0"
  checksum: fd73a4bab48b79e66903fe1cafbdc208956f41ea4f856df883d0c7277b7ab29fd33ee65f93b2ec9192fc0169238f2f8307b7735d27c155821d886b84aa97aa8d
  languageName: node
  linkType: hard

"ci-info@npm:^2.0.0":
  version: 2.0.0
  resolution: "ci-info@npm:2.0.0"
  checksum: 3b374666a85ea3ca43fa49aa3a048d21c9b475c96eb13c133505d2324e7ae5efd6a454f41efe46a152269e9b6a00c9edbe63ec7fa1921957165aae16625acd67
  languageName: node
  linkType: hard

"cli-boxes@npm:^2.2.1":
  version: 2.2.1
  resolution: "cli-boxes@npm:2.2.1"
  checksum: be79f8ec23a558b49e01311b39a1ea01243ecee30539c880cf14bf518a12e223ef40c57ead0cb44f509bffdffc5c129c746cd50d863ab879385370112af4f585
  languageName: node
  linkType: hard

"cli-cursor@npm:^3.1.0":
  version: 3.1.0
  resolution: "cli-cursor@npm:3.1.0"
  dependencies:
    restore-cursor: ^3.1.0
  checksum: 2692784c6cd2fd85cfdbd11f53aea73a463a6d64a77c3e098b2b4697a20443f430c220629e1ca3b195ea5ac4a97a74c2ee411f3807abf6df2b66211fec0c0a29
  languageName: node
  linkType: hard

"cli-spinners@npm:^2.5.0, cli-spinners@npm:^2.9.2":
  version: 2.9.2
  resolution: "cli-spinners@npm:2.9.2"
  checksum: 1bd588289b28432e4676cb5d40505cfe3e53f2e4e10fbe05c8a710a154d6fe0ce7836844b00d6858f740f2ffe67cdc36e0fce9c7b6a8430e80e6388d5aa4956c
  languageName: node
  linkType: hard

"cli-width@npm:^4.1.0":
  version: 4.1.0
  resolution: "cli-width@npm:4.1.0"
  checksum: 0a79cff2dbf89ef530bcd54c713703ba94461457b11e5634bd024c78796ed21401e32349c004995954e06f442d82609287e7aabf6a5f02c919a1cf3b9b6854ff
  languageName: node
  linkType: hard

"clipboardy@npm:^2.3.0":
  version: 2.3.0
  resolution: "clipboardy@npm:2.3.0"
  dependencies:
    arch: ^2.1.1
    execa: ^1.0.0
    is-wsl: ^2.1.1
  checksum: 2733790bc8bbb76a5be7706fa4632f655010774e579a9d3ebe31dc10cf44a2b82cf07b0b6f74162e63048ce32d912193c08c5b5311dce5c19fc641a3bda1292b
  languageName: node
  linkType: hard

"clone-response@npm:^1.0.2":
  version: 1.0.3
  resolution: "clone-response@npm:1.0.3"
  dependencies:
    mimic-response: ^1.0.0
  checksum: 4e671cac39b11c60aa8ba0a450657194a5d6504df51bca3fac5b3bd0145c4f8e8464898f87c8406b83232e3bc5cca555f51c1f9c8ac023969ebfbf7f6bdabb2e
  languageName: node
  linkType: hard

"clone@npm:^1.0.2":
  version: 1.0.4
  resolution: "clone@npm:1.0.4"
  checksum: d06418b7335897209e77bdd430d04f882189582e67bd1f75a04565f3f07f5b3f119a9d670c943b6697d0afb100f03b866b3b8a1f91d4d02d72c4ecf2bb64b5dd
  languageName: node
  linkType: hard

"co-body@npm:^6.1.0":
  version: 6.2.0
  resolution: "co-body@npm:6.2.0"
  dependencies:
    "@hapi/bourne": ^3.0.0
    inflation: ^2.0.0
    qs: ^6.5.2
    raw-body: ^2.3.3
    type-is: ^1.6.16
  checksum: c89336086bb746291b5efd8999403eadce34810f2f1936ab4d38d2cb4290b7fc6b966d1d4e993a2788b3e954b8df63195dbdcb431a06ef2b0ac086fce8ae5c4c
  languageName: node
  linkType: hard

"co@npm:^4.6.0":
  version: 4.6.0
  resolution: "co@npm:4.6.0"
  checksum: 5210d9223010eb95b29df06a91116f2cf7c8e0748a9013ed853b53f362ea0e822f1e5bb054fb3cefc645239a4cf966af1f6133a3b43f40d591f3b68ed6cf0510
  languageName: node
  linkType: hard

"color-convert@npm:^1.9.3":
  version: 1.9.3
  resolution: "color-convert@npm:1.9.3"
  dependencies:
    color-name: 1.1.3
  checksum: fd7a64a17cde98fb923b1dd05c5f2e6f7aefda1b60d67e8d449f9328b4e53b228a428fd38bfeaeb2db2ff6b6503a776a996150b80cdf224062af08a5c8a3a203
  languageName: node
  linkType: hard

"color-convert@npm:^2.0.1":
  version: 2.0.1
  resolution: "color-convert@npm:2.0.1"
  dependencies:
    color-name: ~1.1.4
  checksum: 79e6bdb9fd479a205c71d89574fccfb22bd9053bd98c6c4d870d65c132e5e904e6034978e55b43d69fcaa7433af2016ee203ce76eeba9cfa554b373e7f7db336
  languageName: node
  linkType: hard

"color-name@npm:1.1.3":
  version: 1.1.3
  resolution: "color-name@npm:1.1.3"
  checksum: 09c5d3e33d2105850153b14466501f2bfb30324a2f76568a408763a3b7433b0e50e5b4ab1947868e65cb101bb7cb75029553f2c333b6d4b8138a73fcc133d69d
  languageName: node
  linkType: hard

"color-name@npm:^1.0.0, color-name@npm:~1.1.4":
  version: 1.1.4
  resolution: "color-name@npm:1.1.4"
  checksum: b0445859521eb4021cd0fb0cc1a75cecf67fceecae89b63f62b201cca8d345baf8b952c966862a9d9a2632987d4f6581f0ec8d957dfacece86f0a7919316f610
  languageName: node
  linkType: hard

"color-string@npm:^1.6.0":
  version: 1.9.1
  resolution: "color-string@npm:1.9.1"
  dependencies:
    color-name: ^1.0.0
    simple-swizzle: ^0.2.2
  checksum: c13fe7cff7885f603f49105827d621ce87f4571d78ba28ef4a3f1a104304748f620615e6bf065ecd2145d0d9dad83a3553f52bb25ede7239d18e9f81622f1cc5
  languageName: node
  linkType: hard

"color@npm:^3.1.3":
  version: 3.2.1
  resolution: "color@npm:3.2.1"
  dependencies:
    color-convert: ^1.9.3
    color-string: ^1.6.0
  checksum: f81220e8b774d35865c2561be921f5652117638dcda7ca4029262046e37fc2444ac7bbfdd110cf1fd9c074a4ee5eda8f85944ffbdda26186b602dd9bb05f6400
  languageName: node
  linkType: hard

"colorspace@npm:1.1.x":
  version: 1.1.4
  resolution: "colorspace@npm:1.1.4"
  dependencies:
    color: ^3.1.3
    text-hex: 1.0.x
  checksum: bb3934ef3c417e961e6d03d7ca60ea6e175947029bfadfcdb65109b01881a1c0ecf9c2b0b59abcd0ee4a0d7c1eae93beed01b0e65848936472270a0b341ebce8
  languageName: node
  linkType: hard

"combined-stream@npm:^1.0.8":
  version: 1.0.8
  resolution: "combined-stream@npm:1.0.8"
  dependencies:
    delayed-stream: ~1.0.0
  checksum: 49fa4aeb4916567e33ea81d088f6584749fc90c7abec76fd516bf1c5aa5c79f3584b5ba3de6b86d26ddd64bae5329c4c7479343250cfe71c75bb366eae53bb7c
  languageName: node
  linkType: hard

"commander@npm:^11.1.0":
  version: 11.1.0
  resolution: "commander@npm:11.1.0"
  checksum: fd1a8557c6b5b622c89ecdfde703242ab7db3b628ea5d1755784c79b8e7cb0d74d65b4a262289b533359cd58e1bfc0bf50245dfbcd2954682a6f367c828b79ef
  languageName: node
  linkType: hard

"commander@npm:^4.0.0":
  version: 4.1.1
  resolution: "commander@npm:4.1.1"
  checksum: d7b9913ff92cae20cb577a4ac6fcc121bd6223319e54a40f51a14740a681ad5c574fd29a57da478a5f234a6fa6c52cbf0b7c641353e03c648b1ae85ba670b977
  languageName: node
  linkType: hard

"concat-map@npm:0.0.1":
  version: 0.0.1
  resolution: "concat-map@npm:0.0.1"
  checksum: 902a9f5d8967a3e2faf138d5cb784b9979bad2e6db5357c5b21c568df4ebe62bcb15108af1b2253744844eb964fc023fbd9afbbbb6ddd0bcc204c6fb5b7bf3af
  languageName: node
  linkType: hard

"conf@npm:^10.2.0":
  version: 10.2.0
  resolution: "conf@npm:10.2.0"
  dependencies:
    ajv: ^8.6.3
    ajv-formats: ^2.1.1
    atomically: ^1.7.0
    debounce-fn: ^4.0.0
    dot-prop: ^6.0.1
    env-paths: ^2.2.1
    json-schema-typed: ^7.0.3
    onetime: ^5.1.2
    pkg-up: ^3.1.0
    semver: ^7.3.5
  checksum: 27066f38a25411c1e72e81a5219e2c7ed675cd39d8aa2a2f1797bb2c9255725e92e335d639334177a23d488b22b1290bbe0708e9a005574e5d83d5432df72bd3
  languageName: node
  linkType: hard

"confbox@npm:^0.1.8":
  version: 0.1.8
  resolution: "confbox@npm:0.1.8"
  checksum: 5c7718ab22cf9e35a31c21ef124156076ae8c9dc65e6463d54961caf5a1d529284485a0fdf83fd23b27329f3b75b0c8c07d2e36c699f5151a2efe903343f976a
  languageName: node
  linkType: hard

"configstore@npm:^5.0.1":
  version: 5.0.1
  resolution: "configstore@npm:5.0.1"
  dependencies:
    dot-prop: ^5.2.0
    graceful-fs: ^4.1.2
    make-dir: ^3.0.0
    unique-string: ^2.0.0
    write-file-atomic: ^3.0.0
    xdg-basedir: ^4.0.0
  checksum: 60ef65d493b63f96e14b11ba7ec072fdbf3d40110a94fb7199d1c287761bdea5c5244e76b2596325f30c1b652213aa75de96ea20afd4a5f82065e61ea090988e
  languageName: node
  linkType: hard

"content-disposition@npm:~0.5.2":
  version: 0.5.4
  resolution: "content-disposition@npm:0.5.4"
  dependencies:
    safe-buffer: 5.2.1
  checksum: afb9d545e296a5171d7574fcad634b2fdf698875f4006a9dd04a3e1333880c5c0c98d47b560d01216fb6505a54a2ba6a843ee3a02ec86d7e911e8315255f56c3
  languageName: node
  linkType: hard

"content-type@npm:^1.0.4":
  version: 1.0.5
  resolution: "content-type@npm:1.0.5"
  checksum: 566271e0a251642254cde0f845f9dd4f9856e52d988f4eb0d0dcffbb7a1f8ec98de7a5215fc628f3bce30fe2fb6fd2bc064b562d721658c59b544e2d34ea2766
  languageName: node
  linkType: hard

"convert-source-map@npm:^2.0.0":
  version: 2.0.0
  resolution: "convert-source-map@npm:2.0.0"
  checksum: 63ae9933be5a2b8d4509daca5124e20c14d023c820258e484e32dc324d34c2754e71297c94a05784064ad27615037ef677e3f0c00469fb55f409d2bb21261035
  languageName: node
  linkType: hard

"cookies@npm:~0.9.0":
  version: 0.9.1
  resolution: "cookies@npm:0.9.1"
  dependencies:
    depd: ~2.0.0
    keygrip: ~1.1.0
  checksum: 213e4d14847b582fbd8a003203d3621a4b9fa792a315c37954e89332d38fac5bcc34ba92ef316ad6d5fe28f0187aaa115927fbbe2080744ad1707a93b4313247
  languageName: node
  linkType: hard

"create-require@npm:^1.1.0":
  version: 1.1.1
  resolution: "create-require@npm:1.1.1"
  checksum: a9a1503d4390d8b59ad86f4607de7870b39cad43d929813599a23714831e81c520bddf61bcdd1f8e30f05fd3a2b71ae8538e946eb2786dc65c2bbc520f692eff
  languageName: node
  linkType: hard

"cross-env@npm:^7.0.3":
  version: 7.0.3
  resolution: "cross-env@npm:7.0.3"
  dependencies:
    cross-spawn: ^7.0.1
  bin:
    cross-env: src/bin/cross-env.js
    cross-env-shell: src/bin/cross-env-shell.js
  checksum: 26f2f3ea2ab32617f57effb70d329c2070d2f5630adc800985d8b30b56e8bf7f5f439dd3a0358b79cee6f930afc23cf8e23515f17ccfb30092c6b62c6b630a79
  languageName: node
  linkType: hard

"cross-spawn@npm:^6.0.0":
  version: 6.0.6
  resolution: "cross-spawn@npm:6.0.6"
  dependencies:
    nice-try: ^1.0.4
    path-key: ^2.0.1
    semver: ^5.5.0
    shebang-command: ^1.2.0
    which: ^1.2.9
  checksum: a6e2e5b04a0e0f806c1df45f92cd079b65f95fbe5a7650ee1ab60318c33a6c156a8a2f8b6898f57764f7363ec599a0625e9855dfa78d52d2d73dbd32eb11c25e
  languageName: node
  linkType: hard

"cross-spawn@npm:^7.0.0, cross-spawn@npm:^7.0.1, cross-spawn@npm:^7.0.2, cross-spawn@npm:^7.0.3":
  version: 7.0.6
  resolution: "cross-spawn@npm:7.0.6"
  dependencies:
    path-key: ^3.1.0
    shebang-command: ^2.0.0
    which: ^2.0.1
  checksum: 8d306efacaf6f3f60e0224c287664093fa9185680b2d195852ba9a863f85d02dcc737094c6e512175f8ee0161f9b87c73c6826034c2422e39de7d6569cf4503b
  languageName: node
  linkType: hard

"crypto-random-string@npm:^2.0.0":
  version: 2.0.0
  resolution: "crypto-random-string@npm:2.0.0"
  checksum: 0283879f55e7c16fdceacc181f87a0a65c53bc16ffe1d58b9d19a6277adcd71900d02bb2c4843dd55e78c51e30e89b0fec618a7f170ebcc95b33182c28f05fd6
  languageName: node
  linkType: hard

"css-select@npm:^5.1.0":
  version: 5.1.0
  resolution: "css-select@npm:5.1.0"
  dependencies:
    boolbase: ^1.0.0
    css-what: ^6.1.0
    domhandler: ^5.0.2
    domutils: ^3.0.1
    nth-check: ^2.0.1
  checksum: 2772c049b188d3b8a8159907192e926e11824aea525b8282981f72ba3f349cf9ecd523fdf7734875ee2cb772246c22117fc062da105b6d59afe8dcd5c99c9bda
  languageName: node
  linkType: hard

"css-what@npm:^6.1.0":
  version: 6.1.0
  resolution: "css-what@npm:6.1.0"
  checksum: b975e547e1e90b79625918f84e67db5d33d896e6de846c9b584094e529f0c63e2ab85ee33b9daffd05bff3a146a1916bec664e18bb76dd5f66cbff9fc13b2bbe
  languageName: node
  linkType: hard

"cssesc@npm:^3.0.0":
  version: 3.0.0
  resolution: "cssesc@npm:3.0.0"
  bin:
    cssesc: bin/cssesc
  checksum: f8c4ababffbc5e2ddf2fa9957dda1ee4af6048e22aeda1869d0d00843223c1b13ad3f5d88b51caa46c994225eacb636b764eb807a8883e2fb6f99b4f4e8c48b2
  languageName: node
  linkType: hard

"csstype@npm:^3.1.3":
  version: 3.1.3
  resolution: "csstype@npm:3.1.3"
  checksum: 8db785cc92d259102725b3c694ec0c823f5619a84741b5c7991b8ad135dfaa66093038a1cc63e03361a6cd28d122be48f2106ae72334e067dd619a51f49eddf7
  languageName: node
  linkType: hard

"cuint@npm:^0.2.2":
  version: 0.2.2
  resolution: "cuint@npm:0.2.2"
  checksum: b8127a93a7f16ce120ffcb22108014327c9808b258ee20e7dbb4c6740d7cb0f0c12d18a054eb716b0f2470090666abaae8a082d3cd5ef0e94fa447dd155842c4
  languageName: node
  linkType: hard

"data-view-buffer@npm:^1.0.1":
  version: 1.0.1
  resolution: "data-view-buffer@npm:1.0.1"
  dependencies:
    call-bind: ^1.0.6
    es-errors: ^1.3.0
    is-data-view: ^1.0.1
  checksum: ce24348f3c6231223b216da92e7e6a57a12b4af81a23f27eff8feabdf06acfb16c00639c8b705ca4d167f761cfc756e27e5f065d0a1f840c10b907fdaf8b988c
  languageName: node
  linkType: hard

"data-view-byte-length@npm:^1.0.1":
  version: 1.0.1
  resolution: "data-view-byte-length@npm:1.0.1"
  dependencies:
    call-bind: ^1.0.7
    es-errors: ^1.3.0
    is-data-view: ^1.0.1
  checksum: dbb3200edcb7c1ef0d68979834f81d64fd8cab2f7691b3a4c6b97e67f22182f3ec2c8602efd7b76997b55af6ff8bce485829c1feda4fa2165a6b71fb7baa4269
  languageName: node
  linkType: hard

"data-view-byte-offset@npm:^1.0.0":
  version: 1.0.0
  resolution: "data-view-byte-offset@npm:1.0.0"
  dependencies:
    call-bind: ^1.0.6
    es-errors: ^1.3.0
    is-data-view: ^1.0.1
  checksum: 7f0bf8720b7414ca719eedf1846aeec392f2054d7af707c5dc9a753cc77eb8625f067fa901e0b5127e831f9da9056138d894b9c2be79c27a21f6db5824f009c2
  languageName: node
  linkType: hard

"dayjs@npm:1.11.10":
  version: 1.11.10
  resolution: "dayjs@npm:1.11.10"
  checksum: a6b5a3813b8884f5cd557e2e6b7fa569f4c5d0c97aca9558e38534af4f2d60daafd3ff8c2000fed3435cfcec9e805bcebd99f90130c6d1c5ef524084ced588c4
  languageName: node
  linkType: hard

"de-indent@npm:^1.0.2":
  version: 1.0.2
  resolution: "de-indent@npm:1.0.2"
  checksum: 8deacc0f4a397a4414a0fc4d0034d2b7782e7cb4eaf34943ea47754e08eccf309a0e71fa6f56cc48de429ede999a42d6b4bca761bf91683be0095422dbf24611
  languageName: node
  linkType: hard

"debounce-fn@npm:^4.0.0":
  version: 4.0.0
  resolution: "debounce-fn@npm:4.0.0"
  dependencies:
    mimic-fn: ^3.0.0
  checksum: 7bf8d142b46a88453bbd6eda083f303049b4c8554af5114bdadfc2da56031030664360e81211ae08b708775e6904db7e6d72a421c4ff473344f4521c2c5e4a22
  languageName: node
  linkType: hard

"debug@npm:4, debug@npm:^4, debug@npm:^4.1.0, debug@npm:^4.1.1, debug@npm:^4.3.1, debug@npm:^4.3.2, debug@npm:^4.3.4":
  version: 4.4.0
  resolution: "debug@npm:4.4.0"
  dependencies:
    ms: ^2.1.3
  peerDependenciesMeta:
    supports-color:
      optional: true
  checksum: fb42df878dd0e22816fc56e1fdca9da73caa85212fbe40c868b1295a6878f9101ae684f4eeef516c13acfc700f5ea07f1136954f43d4cd2d477a811144136479
  languageName: node
  linkType: hard

"debug@npm:^3.1.0, debug@npm:^3.2.7":
  version: 3.2.7
  resolution: "debug@npm:3.2.7"
  dependencies:
    ms: ^2.1.1
  checksum: b3d8c5940799914d30314b7c3304a43305fd0715581a919dacb8b3176d024a782062368405b47491516d2091d6462d4d11f2f4974a405048094f8bfebfa3071c
  languageName: node
  linkType: hard

"decompress-response@npm:^3.3.0":
  version: 3.3.0
  resolution: "decompress-response@npm:3.3.0"
  dependencies:
    mimic-response: ^1.0.0
  checksum: 952552ac3bd7de2fc18015086b09468645c9638d98a551305e485230ada278c039c91116e946d07894b39ee53c0f0d5b6473f25a224029344354513b412d7380
  languageName: node
  linkType: hard

"deep-equal@npm:~1.0.1":
  version: 1.0.1
  resolution: "deep-equal@npm:1.0.1"
  checksum: 5af8cbfcebf190491878a498caccc7dc9592f8ebd1685b976eacc3825619d222b5e929923163b92c4f414494e2b884f7ebf00c022e8198e8292deb70dd9785f4
  languageName: node
  linkType: hard

"deep-extend@npm:^0.6.0":
  version: 0.6.0
  resolution: "deep-extend@npm:0.6.0"
  checksum: 7be7e5a8d468d6b10e6a67c3de828f55001b6eb515d014f7aeb9066ce36bd5717161eb47d6a0f7bed8a9083935b465bc163ee2581c8b128d29bf61092fdf57a7
  languageName: node
  linkType: hard

"deep-is@npm:^0.1.3":
  version: 0.1.4
  resolution: "deep-is@npm:0.1.4"
  checksum: edb65dd0d7d1b9c40b2f50219aef30e116cedd6fc79290e740972c132c09106d2e80aa0bc8826673dd5a00222d4179c84b36a790eef63a4c4bca75a37ef90804
  languageName: node
  linkType: hard

"defaults@npm:^1.0.3":
  version: 1.0.4
  resolution: "defaults@npm:1.0.4"
  dependencies:
    clone: ^1.0.2
  checksum: 3a88b7a587fc076b84e60affad8b85245c01f60f38fc1d259e7ac1d89eb9ce6abb19e27215de46b98568dd5bc48471730b327637e6f20b0f1bc85cf00440c80a
  languageName: node
  linkType: hard

"defer-to-connect@npm:^1.0.1":
  version: 1.1.3
  resolution: "defer-to-connect@npm:1.1.3"
  checksum: 9491b301dcfa04956f989481ba7a43c2231044206269eb4ab64a52d6639ee15b1252262a789eb4239fb46ab63e44d4e408641bae8e0793d640aee55398cb3930
  languageName: node
  linkType: hard

"define-data-property@npm:^1.0.1, define-data-property@npm:^1.1.4":
  version: 1.1.4
  resolution: "define-data-property@npm:1.1.4"
  dependencies:
    es-define-property: ^1.0.0
    es-errors: ^1.3.0
    gopd: ^1.0.1
  checksum: 8068ee6cab694d409ac25936eb861eea704b7763f7f342adbdfe337fc27c78d7ae0eff2364b2917b58c508d723c7a074326d068eef2e45c4edcd85cf94d0313b
  languageName: node
  linkType: hard

"define-lazy-prop@npm:^2.0.0":
  version: 2.0.0
  resolution: "define-lazy-prop@npm:2.0.0"
  checksum: 0115fdb065e0490918ba271d7339c42453d209d4cb619dfe635870d906731eff3e1ade8028bb461ea27ce8264ec5e22c6980612d332895977e89c1bbc80fcee2
  languageName: node
  linkType: hard

"define-properties@npm:^1.2.0, define-properties@npm:^1.2.1":
  version: 1.2.1
  resolution: "define-properties@npm:1.2.1"
  dependencies:
    define-data-property: ^1.0.1
    has-property-descriptors: ^1.0.0
    object-keys: ^1.1.1
  checksum: b4ccd00597dd46cb2d4a379398f5b19fca84a16f3374e2249201992f36b30f6835949a9429669ee6b41b6e837205a163eadd745e472069e70dfc10f03e5fcc12
  languageName: node
  linkType: hard

"delayed-stream@npm:~1.0.0":
  version: 1.0.0
  resolution: "delayed-stream@npm:1.0.0"
  checksum: 46fe6e83e2cb1d85ba50bd52803c68be9bd953282fa7096f51fc29edd5d67ff84ff753c51966061e5ba7cb5e47ef6d36a91924eddb7f3f3483b1c560f77a0020
  languageName: node
  linkType: hard

"delegates@npm:^1.0.0":
  version: 1.0.0
  resolution: "delegates@npm:1.0.0"
  checksum: a51744d9b53c164ba9c0492471a1a2ffa0b6727451bdc89e31627fdf4adda9d51277cfcbfb20f0a6f08ccb3c436f341df3e92631a3440226d93a8971724771fd
  languageName: node
  linkType: hard

"depd@npm:2.0.0, depd@npm:^2.0.0, depd@npm:~2.0.0":
  version: 2.0.0
  resolution: "depd@npm:2.0.0"
  checksum: abbe19c768c97ee2eed6282d8ce3031126662252c58d711f646921c9623f9052e3e1906443066beec1095832f534e57c523b7333f8e7e0d93051ab6baef5ab3a
  languageName: node
  linkType: hard

"depd@npm:~1.1.2":
  version: 1.1.2
  resolution: "depd@npm:1.1.2"
  checksum: 6b406620d269619852885ce15965272b829df6f409724415e0002c8632ab6a8c0a08ec1f0bd2add05dc7bd7507606f7e2cc034fa24224ab829580040b835ecd9
  languageName: node
  linkType: hard

"destroy@npm:^1.0.4":
  version: 1.2.0
  resolution: "destroy@npm:1.2.0"
  checksum: 0acb300b7478a08b92d810ab229d5afe0d2f4399272045ab22affa0d99dbaf12637659411530a6fcd597a9bdac718fc94373a61a95b4651bbc7b83684a565e38
  languageName: node
  linkType: hard

"didyoumean@npm:^1.2.2":
  version: 1.2.2
  resolution: "didyoumean@npm:1.2.2"
  checksum: d5d98719d58b3c2fa59663c4c42ba9716f1fd01245c31d5fce31915bd3aa26e6aac149788e007358f778ebbd68a2256eb5973e8ca6f221df221ba060115acf2e
  languageName: node
  linkType: hard

"diff@npm:^4.0.1":
  version: 4.0.2
  resolution: "diff@npm:4.0.2"
  checksum: f2c09b0ce4e6b301c221addd83bf3f454c0bc00caa3dd837cf6c127d6edf7223aa2bbe3b688feea110b7f262adbfc845b757c44c8a9f8c0c5b15d8fa9ce9d20d
  languageName: node
  linkType: hard

"dir-glob@npm:^3.0.1":
  version: 3.0.1
  resolution: "dir-glob@npm:3.0.1"
  dependencies:
    path-type: ^4.0.0
  checksum: fa05e18324510d7283f55862f3161c6759a3f2f8dbce491a2fc14c8324c498286c54282c1f0e933cb930da8419b30679389499b919122952a4f8592362ef4615
  languageName: node
  linkType: hard

"dlv@npm:^1.1.3":
  version: 1.1.3
  resolution: "dlv@npm:1.1.3"
  checksum: d7381bca22ed11933a1ccf376db7a94bee2c57aa61e490f680124fa2d1cd27e94eba641d9f45be57caab4f9a6579de0983466f620a2cd6230d7ec93312105ae7
  languageName: node
  linkType: hard

"doctrine@npm:^2.1.0":
  version: 2.1.0
  resolution: "doctrine@npm:2.1.0"
  dependencies:
    esutils: ^2.0.2
  checksum: a45e277f7feaed309fe658ace1ff286c6e2002ac515af0aaf37145b8baa96e49899638c7cd47dccf84c3d32abfc113246625b3ac8f552d1046072adee13b0dc8
  languageName: node
  linkType: hard

"doctrine@npm:^3.0.0":
  version: 3.0.0
  resolution: "doctrine@npm:3.0.0"
  dependencies:
    esutils: ^2.0.2
  checksum: fd7673ca77fe26cd5cba38d816bc72d641f500f1f9b25b83e8ce28827fe2da7ad583a8da26ab6af85f834138cf8dae9f69b0cd6ab925f52ddab1754db44d99ce
  languageName: node
  linkType: hard

"dom-serializer@npm:^2.0.0":
  version: 2.0.0
  resolution: "dom-serializer@npm:2.0.0"
  dependencies:
    domelementtype: ^2.3.0
    domhandler: ^5.0.2
    entities: ^4.2.0
  checksum: cd1810544fd8cdfbd51fa2c0c1128ec3a13ba92f14e61b7650b5de421b88205fd2e3f0cc6ace82f13334114addb90ed1c2f23074a51770a8e9c1273acbc7f3e6
  languageName: node
  linkType: hard

"domelementtype@npm:^2.3.0":
  version: 2.3.0
  resolution: "domelementtype@npm:2.3.0"
  checksum: ee837a318ff702622f383409d1f5b25dd1024b692ef64d3096ff702e26339f8e345820f29a68bcdcea8cfee3531776b3382651232fbeae95612d6f0a75efb4f6
  languageName: node
  linkType: hard

"domhandler@npm:^5.0.2, domhandler@npm:^5.0.3":
  version: 5.0.3
  resolution: "domhandler@npm:5.0.3"
  dependencies:
    domelementtype: ^2.3.0
  checksum: 0f58f4a6af63e6f3a4320aa446d28b5790a009018707bce2859dcb1d21144c7876482b5188395a188dfa974238c019e0a1e610d2fc269a12b2c192ea2b0b131c
  languageName: node
  linkType: hard

"domutils@npm:^3.0.1, domutils@npm:^3.1.0":
  version: 3.1.0
  resolution: "domutils@npm:3.1.0"
  dependencies:
    dom-serializer: ^2.0.0
    domelementtype: ^2.3.0
    domhandler: ^5.0.3
  checksum: e5757456ddd173caa411cfc02c2bb64133c65546d2c4081381a3bafc8a57411a41eed70494551aa58030be9e58574fcc489828bebd673863d39924fb4878f416
  languageName: node
  linkType: hard

"dot-prop@npm:^5.2.0":
  version: 5.3.0
  resolution: "dot-prop@npm:5.3.0"
  dependencies:
    is-obj: ^2.0.0
  checksum: d5775790093c234ef4bfd5fbe40884ff7e6c87573e5339432870616331189f7f5d86575c5b5af2dcf0f61172990f4f734d07844b1f23482fff09e3c4bead05ea
  languageName: node
  linkType: hard

"dot-prop@npm:^6.0.1":
  version: 6.0.1
  resolution: "dot-prop@npm:6.0.1"
  dependencies:
    is-obj: ^2.0.0
  checksum: 0f47600a4b93e1dc37261da4e6909652c008832a5d3684b5bf9a9a0d3f4c67ea949a86dceed9b72f5733ed8e8e6383cc5958df3bbd0799ee317fd181f2ece700
  languageName: node
  linkType: hard

"dotenv-expand@npm:^11.0.6":
  version: 11.0.7
  resolution: "dotenv-expand@npm:11.0.7"
  dependencies:
    dotenv: ^16.4.5
  checksum: 58455ad9ffedbf6180b49f8f35596da54f10b02efcaabcba5400363f432e1da057113eee39b42365535da41df1e794d54a4aa67b22b37c41686c3dce4e6a28c5
  languageName: node
  linkType: hard

"dotenv@npm:^16.4.5":
  version: 16.4.7
  resolution: "dotenv@npm:16.4.7"
  checksum: c27419b5875a44addcc56cc69b7dc5b0e6587826ca85d5b355da9303c6fc317fc9989f1f18366a16378c9fdd9532d14117a1abe6029cc719cdbbef6eaef2cea4
  languageName: node
  linkType: hard

"dunder-proto@npm:^1.0.0":
  version: 1.0.0
  resolution: "dunder-proto@npm:1.0.0"
  dependencies:
    call-bind-apply-helpers: ^1.0.0
    es-errors: ^1.3.0
    gopd: ^1.2.0
  checksum: 6f0697b17c47377efc00651f43f34e71c09ebba85fafb4d91fe67f5810931f3fa3f45a1ef5d207debbd5682ad9abc3b71b49cb3e67222dcad71fafc92cf6199b
  languageName: node
  linkType: hard

"duplexer3@npm:^0.1.4":
  version: 0.1.5
  resolution: "duplexer3@npm:0.1.5"
  checksum: e677cb4c48f031ca728601d6a20bf6aed4c629d69ef9643cb89c67583d673c4ec9317cc6427501f38bd8c368d3a18f173987cc02bd99d8cf8fe3d94259a22a20
  languageName: node
  linkType: hard

"eastasianwidth@npm:^0.2.0":
  version: 0.2.0
  resolution: "eastasianwidth@npm:0.2.0"
  checksum: 7d00d7cd8e49b9afa762a813faac332dee781932d6f2c848dc348939c4253f1d4564341b7af1d041853bc3f32c2ef141b58e0a4d9862c17a7f08f68df1e0f1ed
  languageName: node
  linkType: hard

"ee-first@npm:1.1.1":
  version: 1.1.1
  resolution: "ee-first@npm:1.1.1"
  checksum: 1b4cac778d64ce3b582a7e26b218afe07e207a0f9bfe13cc7395a6d307849cfe361e65033c3251e00c27dd060cab43014c2d6b2647676135e18b77d2d05b3f4f
  languageName: node
  linkType: hard

"electron-to-chromium@npm:^1.5.73":
  version: 1.5.73
  resolution: "electron-to-chromium@npm:1.5.73"
  checksum: 9e23966afabda22090ebd603e8312af5045aca55f02b8490f5dc66e3bcd2dfefbe3ab0968a587050604e9f398bded342315aa2ec78e418d37c7f237c2a2c69b9
  languageName: node
  linkType: hard

"emoji-regex@npm:^8.0.0":
  version: 8.0.0
  resolution: "emoji-regex@npm:8.0.0"
  checksum: d4c5c39d5a9868b5fa152f00cada8a936868fd3367f33f71be515ecee4c803132d11b31a6222b2571b1e5f7e13890156a94880345594d0ce7e3c9895f560f192
  languageName: node
  linkType: hard

"emoji-regex@npm:^9.2.2":
  version: 9.2.2
  resolution: "emoji-regex@npm:9.2.2"
  checksum: 8487182da74aabd810ac6d6f1994111dfc0e331b01271ae01ec1eb0ad7b5ecc2bbbbd2f053c05cb55a1ac30449527d819bbfbf0e3de1023db308cbcb47f86601
  languageName: node
  linkType: hard

"enabled@npm:2.0.x":
  version: 2.0.0
  resolution: "enabled@npm:2.0.0"
  checksum: 9d256d89f4e8a46ff988c6a79b22fa814b4ffd82826c4fdacd9b42e9b9465709d3b748866d0ab4d442dfc6002d81de7f7b384146ccd1681f6a7f868d2acca063
  languageName: node
  linkType: hard

"encodeurl@npm:^1.0.2":
  version: 1.0.2
  resolution: "encodeurl@npm:1.0.2"
  checksum: e50e3d508cdd9c4565ba72d2012e65038e5d71bdc9198cb125beb6237b5b1ade6c0d343998da9e170fb2eae52c1bed37d4d6d98a46ea423a0cddbed5ac3f780c
  languageName: node
  linkType: hard

"encoding-sniffer@npm:^0.2.0":
  version: 0.2.0
  resolution: "encoding-sniffer@npm:0.2.0"
  dependencies:
    iconv-lite: ^0.6.3
    whatwg-encoding: ^3.1.1
  checksum: 05ad76b674066e62abc80427eb9e89ecf5ed50f4d20c392f7465992d309215687e3ae1ae8b5d5694fb258f4517c759694c3b413d6c724e1024e1cf98750390eb
  languageName: node
  linkType: hard

"encoding@npm:^0.1.13":
  version: 0.1.13
  resolution: "encoding@npm:0.1.13"
  dependencies:
    iconv-lite: ^0.6.2
  checksum: bb98632f8ffa823996e508ce6a58ffcf5856330fde839ae42c9e1f436cc3b5cc651d4aeae72222916545428e54fd0f6aa8862fd8d25bdbcc4589f1e3f3715e7f
  languageName: node
  linkType: hard

"end-of-stream@npm:^1.1.0":
  version: 1.4.4
  resolution: "end-of-stream@npm:1.4.4"
  dependencies:
    once: ^1.4.0
  checksum: 530a5a5a1e517e962854a31693dbb5c0b2fc40b46dad2a56a2deec656ca040631124f4795823acc68238147805f8b021abbe221f4afed5ef3c8e8efc2024908b
  languageName: node
  linkType: hard

"entities@npm:^4.2.0, entities@npm:^4.5.0":
  version: 4.5.0
  resolution: "entities@npm:4.5.0"
  checksum: 853f8ebd5b425d350bffa97dd6958143179a5938352ccae092c62d1267c4e392a039be1bae7d51b6e4ffad25f51f9617531fedf5237f15df302ccfb452cbf2d7
  languageName: node
  linkType: hard

"env-paths@npm:^2.2.0, env-paths@npm:^2.2.1":
  version: 2.2.1
  resolution: "env-paths@npm:2.2.1"
  checksum: 65b5df55a8bab92229ab2b40dad3b387fad24613263d103a97f91c9fe43ceb21965cd3392b1ccb5d77088021e525c4e0481adb309625d0cb94ade1d1fb8dc17e
  languageName: node
  linkType: hard

"err-code@npm:^2.0.2":
  version: 2.0.3
  resolution: "err-code@npm:2.0.3"
  checksum: 8b7b1be20d2de12d2255c0bc2ca638b7af5171142693299416e6a9339bd7d88fc8d7707d913d78e0993176005405a236b066b45666b27b797252c771156ace54
  languageName: node
  linkType: hard

"es-abstract@npm:^1.22.1, es-abstract@npm:^1.22.3, es-abstract@npm:^1.23.2, es-abstract@npm:^1.23.5":
  version: 1.23.5
  resolution: "es-abstract@npm:1.23.5"
  dependencies:
    array-buffer-byte-length: ^1.0.1
    arraybuffer.prototype.slice: ^1.0.3
    available-typed-arrays: ^1.0.7
    call-bind: ^1.0.7
    data-view-buffer: ^1.0.1
    data-view-byte-length: ^1.0.1
    data-view-byte-offset: ^1.0.0
    es-define-property: ^1.0.0
    es-errors: ^1.3.0
    es-object-atoms: ^1.0.0
    es-set-tostringtag: ^2.0.3
    es-to-primitive: ^1.2.1
    function.prototype.name: ^1.1.6
    get-intrinsic: ^1.2.4
    get-symbol-description: ^1.0.2
    globalthis: ^1.0.4
    gopd: ^1.0.1
    has-property-descriptors: ^1.0.2
    has-proto: ^1.0.3
    has-symbols: ^1.0.3
    hasown: ^2.0.2
    internal-slot: ^1.0.7
    is-array-buffer: ^3.0.4
    is-callable: ^1.2.7
    is-data-view: ^1.0.1
    is-negative-zero: ^2.0.3
    is-regex: ^1.1.4
    is-shared-array-buffer: ^1.0.3
    is-string: ^1.0.7
    is-typed-array: ^1.1.13
    is-weakref: ^1.0.2
    object-inspect: ^1.13.3
    object-keys: ^1.1.1
    object.assign: ^4.1.5
    regexp.prototype.flags: ^1.5.3
    safe-array-concat: ^1.1.2
    safe-regex-test: ^1.0.3
    string.prototype.trim: ^1.2.9
    string.prototype.trimend: ^1.0.8
    string.prototype.trimstart: ^1.0.8
    typed-array-buffer: ^1.0.2
    typed-array-byte-length: ^1.0.1
    typed-array-byte-offset: ^1.0.2
    typed-array-length: ^1.0.6
    unbox-primitive: ^1.0.2
    which-typed-array: ^1.1.15
  checksum: 17c81f8a42f0322fd11e0025d3c2229ecfd7923560c710906b8e68660e19c42322750dcedf8ba5cf28bae50d5befd8174d3903ac50dbabb336d3efc3aabed2ee
  languageName: node
  linkType: hard

"es-define-property@npm:^1.0.0, es-define-property@npm:^1.0.1":
  version: 1.0.1
  resolution: "es-define-property@npm:1.0.1"
  checksum: 0512f4e5d564021c9e3a644437b0155af2679d10d80f21adaf868e64d30efdfbd321631956f20f42d655fedb2e3a027da479fad3fa6048f768eb453a80a5f80a
  languageName: node
  linkType: hard

"es-errors@npm:^1.2.1, es-errors@npm:^1.3.0":
  version: 1.3.0
  resolution: "es-errors@npm:1.3.0"
  checksum: ec1414527a0ccacd7f15f4a3bc66e215f04f595ba23ca75cdae0927af099b5ec865f9f4d33e9d7e86f512f252876ac77d4281a7871531a50678132429b1271b5
  languageName: node
  linkType: hard

"es-object-atoms@npm:^1.0.0":
  version: 1.0.0
  resolution: "es-object-atoms@npm:1.0.0"
  dependencies:
    es-errors: ^1.3.0
  checksum: 26f0ff78ab93b63394e8403c353842b2272836968de4eafe97656adfb8a7c84b9099bf0fe96ed58f4a4cddc860f6e34c77f91649a58a5daa4a9c40b902744e3c
  languageName: node
  linkType: hard

"es-set-tostringtag@npm:^2.0.3":
  version: 2.0.3
  resolution: "es-set-tostringtag@npm:2.0.3"
  dependencies:
    get-intrinsic: ^1.2.4
    has-tostringtag: ^1.0.2
    hasown: ^2.0.1
  checksum: 7227fa48a41c0ce83e0377b11130d324ac797390688135b8da5c28994c0165be8b252e15cd1de41e1325e5a5412511586960213e88f9ab4a5e7d028895db5129
  languageName: node
  linkType: hard

"es-shim-unscopables@npm:^1.0.0, es-shim-unscopables@npm:^1.0.2":
  version: 1.0.2
  resolution: "es-shim-unscopables@npm:1.0.2"
  dependencies:
    hasown: ^2.0.0
  checksum: 432bd527c62065da09ed1d37a3f8e623c423683285e6188108286f4a1e8e164a5bcbfbc0051557c7d14633cd2a41ce24c7048e6bbb66a985413fd32f1be72626
  languageName: node
  linkType: hard

"es-to-primitive@npm:^1.2.1":
  version: 1.3.0
  resolution: "es-to-primitive@npm:1.3.0"
  dependencies:
    is-callable: ^1.2.7
    is-date-object: ^1.0.5
    is-symbol: ^1.0.4
  checksum: 966965880356486cd4d1fe9a523deda2084c81b3702d951212c098f5f2ee93605d1b7c1840062efb48a07d892641c7ed1bc194db563645c0dd2b919cb6d65b93
  languageName: node
  linkType: hard

"esbuild@npm:^0.21.3":
  version: 0.21.5
  resolution: "esbuild@npm:0.21.5"
  dependencies:
    "@esbuild/aix-ppc64": 0.21.5
    "@esbuild/android-arm": 0.21.5
    "@esbuild/android-arm64": 0.21.5
    "@esbuild/android-x64": 0.21.5
    "@esbuild/darwin-arm64": 0.21.5
    "@esbuild/darwin-x64": 0.21.5
    "@esbuild/freebsd-arm64": 0.21.5
    "@esbuild/freebsd-x64": 0.21.5
    "@esbuild/linux-arm": 0.21.5
    "@esbuild/linux-arm64": 0.21.5
    "@esbuild/linux-ia32": 0.21.5
    "@esbuild/linux-loong64": 0.21.5
    "@esbuild/linux-mips64el": 0.21.5
    "@esbuild/linux-ppc64": 0.21.5
    "@esbuild/linux-riscv64": 0.21.5
    "@esbuild/linux-s390x": 0.21.5
    "@esbuild/linux-x64": 0.21.5
    "@esbuild/netbsd-x64": 0.21.5
    "@esbuild/openbsd-x64": 0.21.5
    "@esbuild/sunos-x64": 0.21.5
    "@esbuild/win32-arm64": 0.21.5
    "@esbuild/win32-ia32": 0.21.5
    "@esbuild/win32-x64": 0.21.5
  dependenciesMeta:
    "@esbuild/aix-ppc64":
      optional: true
    "@esbuild/android-arm":
      optional: true
    "@esbuild/android-arm64":
      optional: true
    "@esbuild/android-x64":
      optional: true
    "@esbuild/darwin-arm64":
      optional: true
    "@esbuild/darwin-x64":
      optional: true
    "@esbuild/freebsd-arm64":
      optional: true
    "@esbuild/freebsd-x64":
      optional: true
    "@esbuild/linux-arm":
      optional: true
    "@esbuild/linux-arm64":
      optional: true
    "@esbuild/linux-ia32":
      optional: true
    "@esbuild/linux-loong64":
      optional: true
    "@esbuild/linux-mips64el":
      optional: true
    "@esbuild/linux-ppc64":
      optional: true
    "@esbuild/linux-riscv64":
      optional: true
    "@esbuild/linux-s390x":
      optional: true
    "@esbuild/linux-x64":
      optional: true
    "@esbuild/netbsd-x64":
      optional: true
    "@esbuild/openbsd-x64":
      optional: true
    "@esbuild/sunos-x64":
      optional: true
    "@esbuild/win32-arm64":
      optional: true
    "@esbuild/win32-ia32":
      optional: true
    "@esbuild/win32-x64":
      optional: true
  bin:
    esbuild: bin/esbuild
  checksum: 2911c7b50b23a9df59a7d6d4cdd3a4f85855787f374dce751148dbb13305e0ce7e880dde1608c2ab7a927fc6cec3587b80995f7fc87a64b455f8b70b55fd8ec1
  languageName: node
  linkType: hard

"escalade@npm:^3.2.0":
  version: 3.2.0
  resolution: "escalade@npm:3.2.0"
  checksum: 47b029c83de01b0d17ad99ed766347b974b0d628e848de404018f3abee728e987da0d2d370ad4574aa3d5b5bfc368754fd085d69a30f8e75903486ec4b5b709e
  languageName: node
  linkType: hard

"escape-goat@npm:^2.0.0":
  version: 2.1.1
  resolution: "escape-goat@npm:2.1.1"
  checksum: ce05c70c20dd7007b60d2d644b625da5412325fdb57acf671ba06cb2ab3cd6789e2087026921a05b665b0a03fadee2955e7fc0b9a67da15a6551a980b260eba7
  languageName: node
  linkType: hard

"escape-html@npm:^1.0.3":
  version: 1.0.3
  resolution: "escape-html@npm:1.0.3"
  checksum: 6213ca9ae00d0ab8bccb6d8d4e0a98e76237b2410302cf7df70aaa6591d509a2a37ce8998008cbecae8fc8ffaadf3fb0229535e6a145f3ce0b211d060decbb24
  languageName: node
  linkType: hard

"escape-string-regexp@npm:^1.0.5":
  version: 1.0.5
  resolution: "escape-string-regexp@npm:1.0.5"
  checksum: 6092fda75c63b110c706b6a9bfde8a612ad595b628f0bd2147eea1d3406723020810e591effc7db1da91d80a71a737a313567c5abb3813e8d9c71f4aa595b410
  languageName: node
  linkType: hard

"escape-string-regexp@npm:^4.0.0":
  version: 4.0.0
  resolution: "escape-string-regexp@npm:4.0.0"
  checksum: 98b48897d93060f2322108bf29db0feba7dd774be96cd069458d1453347b25ce8682ecc39859d4bca2203cc0ab19c237bcc71755eff49a0f8d90beadeeba5cc5
  languageName: node
  linkType: hard

"escape-string-regexp@npm:^5.0.0":
  version: 5.0.0
  resolution: "escape-string-regexp@npm:5.0.0"
  checksum: 20daabe197f3cb198ec28546deebcf24b3dbb1a5a269184381b3116d12f0532e06007f4bc8da25669d6a7f8efb68db0758df4cd981f57bc5b57f521a3e12c59e
  languageName: node
  linkType: hard

"eslint-compat-utils@npm:^0.5.1":
  version: 0.5.1
  resolution: "eslint-compat-utils@npm:0.5.1"
  dependencies:
    semver: ^7.5.4
  peerDependencies:
    eslint: ">=6.0.0"
  checksum: beccf2a5bd7c7974e3584b269f8a02667c83bca64cfd4c866f3055867f187e78b00ee826721765bdee9b13efaaa248f8068c581f7bb05803e8f47abb116e68fc
  languageName: node
  linkType: hard

"eslint-config-prettier@npm:^9.1.0":
  version: 9.1.0
  resolution: "eslint-config-prettier@npm:9.1.0"
  peerDependencies:
    eslint: ">=7.0.0"
  bin:
    eslint-config-prettier: bin/cli.js
  checksum: 9229b768c879f500ee54ca05925f31b0c0bafff3d9f5521f98ff05127356de78c81deb9365c86a5ec4efa990cb72b74df8612ae15965b14136044c73e1f6a907
  languageName: node
  linkType: hard

"eslint-config-standard@npm:^17.1.0":
  version: 17.1.0
  resolution: "eslint-config-standard@npm:17.1.0"
  peerDependencies:
    eslint: ^8.0.1
    eslint-plugin-import: ^2.25.2
    eslint-plugin-n: "^15.0.0 || ^16.0.0 "
    eslint-plugin-promise: ^6.0.0
  checksum: 8ed14ffe424b8a7e67b85e44f75c46dc4c6954f7c474c871c56fb0daf40b6b2a7af2db55102b12a440158b2be898e1fb8333b05e3dbeaeaef066fdbc863eaa88
  languageName: node
  linkType: hard

"eslint-import-resolver-node@npm:^0.3.9":
  version: 0.3.9
  resolution: "eslint-import-resolver-node@npm:0.3.9"
  dependencies:
    debug: ^3.2.7
    is-core-module: ^2.13.0
    resolve: ^1.22.4
  checksum: 439b91271236b452d478d0522a44482e8c8540bf9df9bd744062ebb89ab45727a3acd03366a6ba2bdbcde8f9f718bab7fe8db64688aca75acf37e04eafd25e22
  languageName: node
  linkType: hard

"eslint-module-utils@npm:^2.12.0":
  version: 2.12.0
  resolution: "eslint-module-utils@npm:2.12.0"
  dependencies:
    debug: ^3.2.7
  peerDependenciesMeta:
    eslint:
      optional: true
  checksum: be3ac52e0971c6f46daeb1a7e760e45c7c45f820c8cc211799f85f10f04ccbf7afc17039165d56cb2da7f7ca9cec2b3a777013cddf0b976784b37eb9efa24180
  languageName: node
  linkType: hard

"eslint-plugin-es-x@npm:^7.5.0":
  version: 7.8.0
  resolution: "eslint-plugin-es-x@npm:7.8.0"
  dependencies:
    "@eslint-community/eslint-utils": ^4.1.2
    "@eslint-community/regexpp": ^4.11.0
    eslint-compat-utils: ^0.5.1
  peerDependencies:
    eslint: ">=8"
  checksum: c30fc6bd94f86781eaf34dec59e7d52ee68b8a12305ae76222d8d0ff6cc0a5c94e8306ed079b4234d64f7464bcd162a5fef59e7cc69a978ba77950e0395c79f8
  languageName: node
  linkType: hard

"eslint-plugin-es@npm:^3.0.0":
  version: 3.0.1
  resolution: "eslint-plugin-es@npm:3.0.1"
  dependencies:
    eslint-utils: ^2.0.0
    regexpp: ^3.0.0
  peerDependencies:
    eslint: ">=4.19.1"
  checksum: e57592c52301ee8ddc296ae44216df007f3a870bcb3be8d1fbdb909a1d3a3efe3fa3785de02066f9eba1d6466b722d3eb3cc3f8b75b3cf6a1cbded31ac6298e4
  languageName: node
  linkType: hard

"eslint-plugin-import@npm:^2.29.1":
  version: 2.31.0
  resolution: "eslint-plugin-import@npm:2.31.0"
  dependencies:
    "@rtsao/scc": ^1.1.0
    array-includes: ^3.1.8
    array.prototype.findlastindex: ^1.2.5
    array.prototype.flat: ^1.3.2
    array.prototype.flatmap: ^1.3.2
    debug: ^3.2.7
    doctrine: ^2.1.0
    eslint-import-resolver-node: ^0.3.9
    eslint-module-utils: ^2.12.0
    hasown: ^2.0.2
    is-core-module: ^2.15.1
    is-glob: ^4.0.3
    minimatch: ^3.1.2
    object.fromentries: ^2.0.8
    object.groupby: ^1.0.3
    object.values: ^1.2.0
    semver: ^6.3.1
    string.prototype.trimend: ^1.0.8
    tsconfig-paths: ^3.15.0
  peerDependencies:
    eslint: ^2 || ^3 || ^4 || ^5 || ^6 || ^7.2.0 || ^8 || ^9
  checksum: b1d2ac268b3582ff1af2a72a2c476eae4d250c100f2e335b6e102036e4a35efa530b80ec578dfc36761fabb34a635b9bf5ab071abe9d4404a4bb054fdf22d415
  languageName: node
  linkType: hard

"eslint-plugin-n@npm:^16.6.2":
  version: 16.6.2
  resolution: "eslint-plugin-n@npm:16.6.2"
  dependencies:
    "@eslint-community/eslint-utils": ^4.4.0
    builtins: ^5.0.1
    eslint-plugin-es-x: ^7.5.0
    get-tsconfig: ^4.7.0
    globals: ^13.24.0
    ignore: ^5.2.4
    is-builtin-module: ^3.2.1
    is-core-module: ^2.12.1
    minimatch: ^3.1.2
    resolve: ^1.22.2
    semver: ^7.5.3
  peerDependencies:
    eslint: ">=7.0.0"
  checksum: 3b468da0038cf25af582608983491b33ac2d481b6a94a0ff2e715d3b85e1ff8cb93df4cd67b689d520bea1bfb8f2b717f01606bf6b2ea19fe8f9c0999ea7057d
  languageName: node
  linkType: hard

"eslint-plugin-node@npm:^11.1.0":
  version: 11.1.0
  resolution: "eslint-plugin-node@npm:11.1.0"
  dependencies:
    eslint-plugin-es: ^3.0.0
    eslint-utils: ^2.0.0
    ignore: ^5.1.1
    minimatch: ^3.0.4
    resolve: ^1.10.1
    semver: ^6.1.0
  peerDependencies:
    eslint: ">=5.16.0"
  checksum: 5804c4f8a6e721f183ef31d46fbe3b4e1265832f352810060e0502aeac7de034df83352fc88643b19641bb2163f2587f1bd4119aff0fd21e8d98c57c450e013b
  languageName: node
  linkType: hard

"eslint-plugin-promise@npm:^6.1.1":
  version: 6.6.0
  resolution: "eslint-plugin-promise@npm:6.6.0"
  peerDependencies:
    eslint: ^7.0.0 || ^8.0.0 || ^9.0.0
  checksum: 5098fbf38585ad411737c389c462df72b11a7db2f0241eca23cf990e5535a2de3fac7fb24258c3e6bf05433ef2a59425ec1ca1cef456360614eb7cdbfefcec66
  languageName: node
  linkType: hard

"eslint-plugin-tailwindcss@npm:^3.14.3":
  version: 3.17.5
  resolution: "eslint-plugin-tailwindcss@npm:3.17.5"
  dependencies:
    fast-glob: ^3.2.5
    postcss: ^8.4.4
  peerDependencies:
    tailwindcss: ^3.4.0
  checksum: d1205b640bd3d4224f9e5905f5699b8ebd5999e8ffc921e64bed82bc9ce4761b0e5b55699649e936a71740dd1f5de70b848a26a840b15fc14b0659920d85ec48
  languageName: node
  linkType: hard

"eslint-plugin-vue@npm:^9.22.0":
  version: 9.32.0
  resolution: "eslint-plugin-vue@npm:9.32.0"
  dependencies:
    "@eslint-community/eslint-utils": ^4.4.0
    globals: ^13.24.0
    natural-compare: ^1.4.0
    nth-check: ^2.1.1
    postcss-selector-parser: ^6.0.15
    semver: ^7.6.3
    vue-eslint-parser: ^9.4.3
    xml-name-validator: ^4.0.0
  peerDependencies:
    eslint: ^6.2.0 || ^7.0.0 || ^8.0.0 || ^9.0.0
  checksum: 14320cdb5f8c9f7cb23cad4595c1fd22f61c0b68ef1b7bd9071060d9f5bd32277ad7d022a7bf49fcb4c302d32a60045dbe1e734461edde6af160e0ade2a06410
  languageName: node
  linkType: hard

"eslint-scope@npm:^7.1.1, eslint-scope@npm:^7.2.2":
  version: 7.2.2
  resolution: "eslint-scope@npm:7.2.2"
  dependencies:
    esrecurse: ^4.3.0
    estraverse: ^5.2.0
  checksum: ec97dbf5fb04b94e8f4c5a91a7f0a6dd3c55e46bfc7bbcd0e3138c3a76977570e02ed89a1810c778dcd72072ff0e9621ba1379b4babe53921d71e2e4486fda3e
  languageName: node
  linkType: hard

"eslint-utils@npm:^2.0.0":
  version: 2.1.0
  resolution: "eslint-utils@npm:2.1.0"
  dependencies:
    eslint-visitor-keys: ^1.1.0
  checksum: 27500938f348da42100d9e6ad03ae29b3de19ba757ae1a7f4a087bdcf83ac60949bbb54286492ca61fac1f5f3ac8692dd21537ce6214240bf95ad0122f24d71d
  languageName: node
  linkType: hard

"eslint-visitor-keys@npm:^1.1.0":
  version: 1.3.0
  resolution: "eslint-visitor-keys@npm:1.3.0"
  checksum: 37a19b712f42f4c9027e8ba98c2b06031c17e0c0a4c696cd429bd9ee04eb43889c446f2cd545e1ff51bef9593fcec94ecd2c2ef89129fcbbf3adadbef520376a
  languageName: node
  linkType: hard

"eslint-visitor-keys@npm:^3.3.0, eslint-visitor-keys@npm:^3.4.1, eslint-visitor-keys@npm:^3.4.3":
  version: 3.4.3
  resolution: "eslint-visitor-keys@npm:3.4.3"
  checksum: 36e9ef87fca698b6fd7ca5ca35d7b2b6eeaaf106572e2f7fd31c12d3bfdaccdb587bba6d3621067e5aece31c8c3a348b93922ab8f7b2cbc6aaab5e1d89040c60
  languageName: node
  linkType: hard

"eslint@npm:^8.57.0":
  version: 8.57.1
  resolution: "eslint@npm:8.57.1"
  dependencies:
    "@eslint-community/eslint-utils": ^4.2.0
    "@eslint-community/regexpp": ^4.6.1
    "@eslint/eslintrc": ^2.1.4
    "@eslint/js": 8.57.1
    "@humanwhocodes/config-array": ^0.13.0
    "@humanwhocodes/module-importer": ^1.0.1
    "@nodelib/fs.walk": ^1.2.8
    "@ungap/structured-clone": ^1.2.0
    ajv: ^6.12.4
    chalk: ^4.0.0
    cross-spawn: ^7.0.2
    debug: ^4.3.2
    doctrine: ^3.0.0
    escape-string-regexp: ^4.0.0
    eslint-scope: ^7.2.2
    eslint-visitor-keys: ^3.4.3
    espree: ^9.6.1
    esquery: ^1.4.2
    esutils: ^2.0.2
    fast-deep-equal: ^3.1.3
    file-entry-cache: ^6.0.1
    find-up: ^5.0.0
    glob-parent: ^6.0.2
    globals: ^13.19.0
    graphemer: ^1.4.0
    ignore: ^5.2.0
    imurmurhash: ^0.1.4
    is-glob: ^4.0.0
    is-path-inside: ^3.0.3
    js-yaml: ^4.1.0
    json-stable-stringify-without-jsonify: ^1.0.1
    levn: ^0.4.1
    lodash.merge: ^4.6.2
    minimatch: ^3.1.2
    natural-compare: ^1.4.0
    optionator: ^0.9.3
    strip-ansi: ^6.0.1
    text-table: ^0.2.0
  bin:
    eslint: bin/eslint.js
  checksum: e2489bb7f86dd2011967759a09164e65744ef7688c310bc990612fc26953f34cc391872807486b15c06833bdff737726a23e9b4cdba5de144c311377dc41d91b
  languageName: node
  linkType: hard

"espree@npm:^9.3.1, espree@npm:^9.6.0, espree@npm:^9.6.1":
  version: 9.6.1
  resolution: "espree@npm:9.6.1"
  dependencies:
    acorn: ^8.9.0
    acorn-jsx: ^5.3.2
    eslint-visitor-keys: ^3.4.1
  checksum: eb8c149c7a2a77b3f33a5af80c10875c3abd65450f60b8af6db1bfcfa8f101e21c1e56a561c6dc13b848e18148d43469e7cd208506238554fb5395a9ea5a1ab9
  languageName: node
  linkType: hard

"esquery@npm:^1.4.0, esquery@npm:^1.4.2":
  version: 1.6.0
  resolution: "esquery@npm:1.6.0"
  dependencies:
    estraverse: ^5.1.0
  checksum: 08ec4fe446d9ab27186da274d979558557fbdbbd10968fa9758552482720c54152a5640e08b9009e5a30706b66aba510692054d4129d32d0e12e05bbc0b96fb2
  languageName: node
  linkType: hard

"esrecurse@npm:^4.3.0":
  version: 4.3.0
  resolution: "esrecurse@npm:4.3.0"
  dependencies:
    estraverse: ^5.2.0
  checksum: ebc17b1a33c51cef46fdc28b958994b1dc43cd2e86237515cbc3b4e5d2be6a811b2315d0a1a4d9d340b6d2308b15322f5c8291059521cc5f4802f65e7ec32837
  languageName: node
  linkType: hard

"estraverse@npm:^5.1.0, estraverse@npm:^5.2.0":
  version: 5.3.0
  resolution: "estraverse@npm:5.3.0"
  checksum: 072780882dc8416ad144f8fe199628d2b3e7bbc9989d9ed43795d2c90309a2047e6bc5979d7e2322a341163d22cfad9e21f4110597fe487519697389497e4e2b
  languageName: node
  linkType: hard

"estree-walker@npm:^2.0.2":
  version: 2.0.2
  resolution: "estree-walker@npm:2.0.2"
  checksum: 6151e6f9828abe2259e57f5fd3761335bb0d2ebd76dc1a01048ccee22fabcfef3c0859300f6d83ff0d1927849368775ec5a6d265dde2f6de5a1be1721cd94efc
  languageName: node
  linkType: hard

"estree-walker@npm:^3.0.3":
  version: 3.0.3
  resolution: "estree-walker@npm:3.0.3"
  dependencies:
    "@types/estree": ^1.0.0
  checksum: a65728d5727b71de172c5df323385755a16c0fdab8234dc756c3854cfee343261ddfbb72a809a5660fac8c75d960bb3e21aa898c2d7e9b19bb298482ca58a3af
  languageName: node
  linkType: hard

"esutils@npm:^2.0.2":
  version: 2.0.3
  resolution: "esutils@npm:2.0.3"
  checksum: 22b5b08f74737379a840b8ed2036a5fb35826c709ab000683b092d9054e5c2a82c27818f12604bfc2a9a76b90b6834ef081edbc1c7ae30d1627012e067c6ec87
  languageName: node
  linkType: hard

"event-target-shim@npm:^5.0.0":
  version: 5.0.1
  resolution: "event-target-shim@npm:5.0.1"
  checksum: 1ffe3bb22a6d51bdeb6bf6f7cf97d2ff4a74b017ad12284cc9e6a279e727dc30a5de6bb613e5596ff4dc3e517841339ad09a7eec44266eccb1aa201a30448166
  languageName: node
  linkType: hard

"eventemitter3@npm:^5.0.1":
  version: 5.0.1
  resolution: "eventemitter3@npm:5.0.1"
  checksum: 543d6c858ab699303c3c32e0f0f47fc64d360bf73c3daf0ac0b5079710e340d6fe9f15487f94e66c629f5f82cd1a8678d692f3dbb6f6fcd1190e1b97fcad36f8
  languageName: node
  linkType: hard

"execa@npm:^1.0.0":
  version: 1.0.0
  resolution: "execa@npm:1.0.0"
  dependencies:
    cross-spawn: ^6.0.0
    get-stream: ^4.0.0
    is-stream: ^1.1.0
    npm-run-path: ^2.0.0
    p-finally: ^1.0.0
    signal-exit: ^3.0.0
    strip-eof: ^1.0.0
  checksum: ddf1342c1c7d02dd93b41364cd847640f6163350d9439071abf70bf4ceb1b9b2b2e37f54babb1d8dc1df8e0d8def32d0e81e74a2e62c3e1d70c303eb4c306bc4
  languageName: node
  linkType: hard

"execa@npm:^5.1.1":
  version: 5.1.1
  resolution: "execa@npm:5.1.1"
  dependencies:
    cross-spawn: ^7.0.3
    get-stream: ^6.0.0
    human-signals: ^2.1.0
    is-stream: ^2.0.0
    merge-stream: ^2.0.0
    npm-run-path: ^4.0.1
    onetime: ^5.1.2
    signal-exit: ^3.0.3
    strip-final-newline: ^2.0.0
  checksum: fba9022c8c8c15ed862847e94c252b3d946036d7547af310e344a527e59021fd8b6bb0723883ea87044dc4f0201f949046993124a42ccb0855cae5bf8c786343
  languageName: node
  linkType: hard

"exponential-backoff@npm:^3.1.1":
  version: 3.1.1
  resolution: "exponential-backoff@npm:3.1.1"
  checksum: 3d21519a4f8207c99f7457287291316306255a328770d320b401114ec8481986e4e467e854cb9914dd965e0a1ca810a23ccb559c642c88f4c7f55c55778a9b48
  languageName: node
  linkType: hard

"external-editor@npm:^3.1.0":
  version: 3.1.0
  resolution: "external-editor@npm:3.1.0"
  dependencies:
    chardet: ^0.7.0
    iconv-lite: ^0.4.24
    tmp: ^0.0.33
  checksum: 1c2a616a73f1b3435ce04030261bed0e22d4737e14b090bb48e58865da92529c9f2b05b893de650738d55e692d071819b45e1669259b2b354bc3154d27a698c7
  languageName: node
  linkType: hard

"fast-deep-equal@npm:^3.1.1, fast-deep-equal@npm:^3.1.3":
  version: 3.1.3
  resolution: "fast-deep-equal@npm:3.1.3"
  checksum: e21a9d8d84f53493b6aa15efc9cfd53dd5b714a1f23f67fb5dc8f574af80df889b3bce25dc081887c6d25457cce704e636395333abad896ccdec03abaf1f3f9d
  languageName: node
  linkType: hard

"fast-glob@npm:^3.2.5, fast-glob@npm:^3.2.9, fast-glob@npm:^3.3.1, fast-glob@npm:^3.3.2":
  version: 3.3.2
  resolution: "fast-glob@npm:3.3.2"
  dependencies:
    "@nodelib/fs.stat": ^2.0.2
    "@nodelib/fs.walk": ^1.2.3
    glob-parent: ^5.1.2
    merge2: ^1.3.0
    micromatch: ^4.0.4
  checksum: 900e4979f4dbc3313840078419245621259f349950411ca2fa445a2f9a1a6d98c3b5e7e0660c5ccd563aa61abe133a21765c6c0dec8e57da1ba71d8000b05ec1
  languageName: node
  linkType: hard

"fast-json-stable-stringify@npm:^2.0.0":
  version: 2.1.0
  resolution: "fast-json-stable-stringify@npm:2.1.0"
  checksum: b191531e36c607977e5b1c47811158733c34ccb3bfde92c44798929e9b4154884378536d26ad90dfecd32e1ffc09c545d23535ad91b3161a27ddbb8ebe0cbecb
  languageName: node
  linkType: hard

"fast-levenshtein@npm:^2.0.6":
  version: 2.0.6
  resolution: "fast-levenshtein@npm:2.0.6"
  checksum: 92cfec0a8dfafd9c7a15fba8f2cc29cd0b62b85f056d99ce448bbcd9f708e18ab2764bda4dd5158364f4145a7c72788538994f0d1787b956ef0d1062b0f7c24c
  languageName: node
  linkType: hard

"fast-uri@npm:^3.0.1":
  version: 3.0.3
  resolution: "fast-uri@npm:3.0.3"
  checksum: c52e6c86465f5c240e84a4485fb001088cc743d261a4b54b0050ce4758b1648bdbe53da1328ef9620149dca1435e3de64184f226d7c0a3656cb5837b3491e149
  languageName: node
  linkType: hard

"fastq@npm:^1.6.0":
  version: 1.17.1
  resolution: "fastq@npm:1.17.1"
  dependencies:
    reusify: ^1.0.4
  checksum: a8c5b26788d5a1763f88bae56a8ddeee579f935a831c5fe7a8268cea5b0a91fbfe705f612209e02d639b881d7b48e461a50da4a10cfaa40da5ca7cc9da098d88
  languageName: node
  linkType: hard

"fecha@npm:^4.2.0":
  version: 4.2.3
  resolution: "fecha@npm:4.2.3"
  checksum: f94e2fb3acf5a7754165d04549460d3ae6c34830394d20c552197e3e000035d69732d74af04b9bed3283bf29fe2a9ebdcc0085e640b0be3cc3658b9726265e31
  languageName: node
  linkType: hard

"fetch-sse@npm:1.0.23":
  version: 1.0.23
  resolution: "fetch-sse@npm:1.0.23"
  checksum: e9a629c1d820741d01ec750acbb9443a5a85c70f8b10ff6601d77d3556895c28ae585c4d598ec89e50e6ecd2ba4a3eb7c15a3401c5bab7b8e41ebf0066b29ddb
  languageName: node
  linkType: hard

"figures@npm:^3.2.0":
  version: 3.2.0
  resolution: "figures@npm:3.2.0"
  dependencies:
    escape-string-regexp: ^1.0.5
  checksum: 85a6ad29e9aca80b49b817e7c89ecc4716ff14e3779d9835af554db91bac41c0f289c418923519392a1e582b4d10482ad282021330cd045bb7b80c84152f2a2b
  languageName: node
  linkType: hard

"file-entry-cache@npm:^6.0.1":
  version: 6.0.1
  resolution: "file-entry-cache@npm:6.0.1"
  dependencies:
    flat-cache: ^3.0.4
  checksum: ********************************************************************************************************************************
  languageName: node
  linkType: hard

"fill-range@npm:^7.1.1":
  version: 7.1.1
  resolution: "fill-range@npm:7.1.1"
  dependencies:
    to-regex-range: ^5.0.1
  checksum: b4abfbca3839a3d55e4ae5ec62e131e2e356bf4859ce8480c64c4876100f4df292a63e5bb1618e1d7460282ca2b305653064f01654474aa35c68000980f17798
  languageName: node
  linkType: hard

"find-up@npm:^3.0.0":
  version: 3.0.0
  resolution: "find-up@npm:3.0.0"
  dependencies:
    locate-path: ^3.0.0
  checksum: 38eba3fe7a66e4bc7f0f5a1366dc25508b7cfc349f852640e3678d26ad9a6d7e2c43eff0a472287de4a9753ef58f066a0ea892a256fa3636ad51b3fe1e17fae9
  languageName: node
  linkType: hard

"find-up@npm:^5.0.0":
  version: 5.0.0
  resolution: "find-up@npm:5.0.0"
  dependencies:
    locate-path: ^6.0.0
    path-exists: ^4.0.0
  checksum: 07955e357348f34660bde7920783204ff5a26ac2cafcaa28bace494027158a97b9f56faaf2d89a6106211a8174db650dd9f503f9c0d526b1202d5554a00b9095
  languageName: node
  linkType: hard

"flat-cache@npm:^3.0.4":
  version: 3.2.0
  resolution: "flat-cache@npm:3.2.0"
  dependencies:
    flatted: ^3.2.9
    keyv: ^4.5.3
    rimraf: ^3.0.2
  checksum: e7e0f59801e288b54bee5cb9681e9ee21ee28ef309f886b312c9d08415b79fc0f24ac842f84356ce80f47d6a53de62197ce0e6e148dc42d5db005992e2a756ec
  languageName: node
  linkType: hard

"flatted@npm:^3.2.9":
  version: 3.3.2
  resolution: "flatted@npm:3.3.2"
  checksum: ********************************************************************************************************************************
  languageName: node
  linkType: hard

"fn.name@npm:1.x.x":
  version: 1.1.0
  resolution: "fn.name@npm:1.1.0"
  checksum: e357144f48cfc9a7f52a82bbc6c23df7c8de639fce049cac41d41d62cabb740cdb9f14eddc6485e29c933104455bdd7a69bb14a9012cef9cd4fa252a4d0cf293
  languageName: node
  linkType: hard

"for-each@npm:^0.3.3":
  version: 0.3.3
  resolution: "for-each@npm:0.3.3"
  dependencies:
    is-callable: ^1.1.3
  checksum: 6c48ff2bc63362319c65e2edca4a8e1e3483a2fabc72fbe7feaf8c73db94fc7861bd53bc02c8a66a0c1dd709da6b04eec42e0abdd6b40ce47305ae92a25e5d28
  languageName: node
  linkType: hard

"foreground-child@npm:^3.1.0":
  version: 3.3.0
  resolution: "foreground-child@npm:3.3.0"
  dependencies:
    cross-spawn: ^7.0.0
    signal-exit: ^4.0.1
  checksum: 1989698488f725b05b26bc9afc8a08f08ec41807cd7b92ad85d96004ddf8243fd3e79486b8348c64a3011ae5cc2c9f0936af989e1f28339805d8bc178a75b451
  languageName: node
  linkType: hard

"form-data-encoder@npm:1.7.2":
  version: 1.7.2
  resolution: "form-data-encoder@npm:1.7.2"
  checksum: aeebd87a1cb009e13cbb5e4e4008e6202ed5f6551eb6d9582ba8a062005178907b90f4887899d3c993de879159b6c0c940af8196725b428b4248cec5af3acf5f
  languageName: node
  linkType: hard

"form-data@npm:^3.0.0":
  version: 3.0.2
  resolution: "form-data@npm:3.0.2"
  dependencies:
    asynckit: ^0.4.0
    combined-stream: ^1.0.8
    mime-types: ^2.1.12
  checksum: 25ffdeed693c8fc59b56082d15ad63f11688fabac2d14918fb339170020f66295e520a6659f3a698217f15c7924fbc593117ecd61d8391a146ea06d686793622
  languageName: node
  linkType: hard

"form-data@npm:^4.0.0":
  version: 4.0.1
  resolution: "form-data@npm:4.0.1"
  dependencies:
    asynckit: ^0.4.0
    combined-stream: ^1.0.8
    mime-types: ^2.1.12
  checksum: ccee458cd5baf234d6b57f349fe9cc5f9a2ea8fd1af5ecda501a18fd1572a6dd3bf08a49f00568afd995b6a65af34cb8dec083cf9d582c4e621836499498dd84
  languageName: node
  linkType: hard

"formdata-node@npm:^4.3.2":
  version: 4.4.1
  resolution: "formdata-node@npm:4.4.1"
  dependencies:
    node-domexception: 1.0.0
    web-streams-polyfill: 4.0.0-beta.3
  checksum: d91d4f667cfed74827fc281594102c0dabddd03c9f8b426fc97123eedbf73f5060ee43205d89284d6854e2fc5827e030cd352ef68b93beda8decc2d72128c576
  languageName: node
  linkType: hard

"fraction.js@npm:^4.3.7":
  version: 4.3.7
  resolution: "fraction.js@npm:4.3.7"
  checksum: e1553ae3f08e3ba0e8c06e43a3ab20b319966dfb7ddb96fd9b5d0ee11a66571af7f993229c88ebbb0d4a816eb813a24ed48207b140d442a8f76f33763b8d1f3f
  languageName: node
  linkType: hard

"fresh@npm:~0.5.2":
  version: 0.5.2
  resolution: "fresh@npm:0.5.2"
  checksum: 13ea8b08f91e669a64e3ba3a20eb79d7ca5379a81f1ff7f4310d54e2320645503cc0c78daedc93dfb6191287295f6479544a649c64d8e41a1c0fb0c221552346
  languageName: node
  linkType: hard

"fs-minipass@npm:^3.0.0":
  version: 3.0.3
  resolution: "fs-minipass@npm:3.0.3"
  dependencies:
    minipass: ^7.0.3
  checksum: 8722a41109130851d979222d3ec88aabaceeaaf8f57b2a8f744ef8bd2d1ce95453b04a61daa0078822bc5cd21e008814f06fe6586f56fef511e71b8d2394d802
  languageName: node
  linkType: hard

"fs.realpath@npm:^1.0.0":
  version: 1.0.0
  resolution: "fs.realpath@npm:1.0.0"
  checksum: 99ddea01a7e75aa276c250a04eedeffe5662bce66c65c07164ad6264f9de18fb21be9433ead460e54cff20e31721c811f4fb5d70591799df5f85dce6d6746fd0
  languageName: node
  linkType: hard

"fsevents@npm:~2.3.2, fsevents@npm:~2.3.3":
  version: 2.3.3
  resolution: "fsevents@npm:2.3.3"
  dependencies:
    node-gyp: latest
  checksum: 11e6ea6fea15e42461fc55b4b0e4a0a3c654faa567f1877dbd353f39156f69def97a69936d1746619d656c4b93de2238bf731f6085a03a50cabf287c9d024317
  conditions: os=darwin
  languageName: node
  linkType: hard

"fsevents@patch:fsevents@~2.3.2#~builtin<compat/fsevents>, fsevents@patch:fsevents@~2.3.3#~builtin<compat/fsevents>":
  version: 2.3.3
  resolution: "fsevents@patch:fsevents@npm%3A2.3.3#~builtin<compat/fsevents>::version=2.3.3&hash=df0bf1"
  dependencies:
    node-gyp: latest
  conditions: os=darwin
  languageName: node
  linkType: hard

"function-bind@npm:^1.1.2":
  version: 1.1.2
  resolution: "function-bind@npm:1.1.2"
  checksum: 2b0ff4ce708d99715ad14a6d1f894e2a83242e4a52ccfcefaee5e40050562e5f6dafc1adbb4ce2d4ab47279a45dc736ab91ea5042d843c3c092820dfe032efb1
  languageName: node
  linkType: hard

"function.prototype.name@npm:^1.1.6":
  version: 1.1.6
  resolution: "function.prototype.name@npm:1.1.6"
  dependencies:
    call-bind: ^1.0.2
    define-properties: ^1.2.0
    es-abstract: ^1.22.1
    functions-have-names: ^1.2.3
  checksum: 7a3f9bd98adab09a07f6e1f03da03d3f7c26abbdeaeee15223f6c04a9fb5674792bdf5e689dac19b97ac71de6aad2027ba3048a9b883aa1b3173eed6ab07f479
  languageName: node
  linkType: hard

"functions-have-names@npm:^1.2.3":
  version: 1.2.3
  resolution: "functions-have-names@npm:1.2.3"
  checksum: c3f1f5ba20f4e962efb71344ce0a40722163e85bee2101ce25f88214e78182d2d2476aa85ef37950c579eb6cf6ee811c17b3101bb84004bb75655f3e33f3fdb5
  languageName: node
  linkType: hard

"gensync@npm:^1.0.0-beta.2":
  version: 1.0.0-beta.2
  resolution: "gensync@npm:1.0.0-beta.2"
  checksum: a7437e58c6be12aa6c90f7730eac7fa9833dc78872b4ad2963d2031b00a3367a93f98aec75f9aaac7220848e4026d67a8655e870b24f20a543d103c0d65952ec
  languageName: node
  linkType: hard

"get-intrinsic@npm:^1.2.1, get-intrinsic@npm:^1.2.3, get-intrinsic@npm:^1.2.4, get-intrinsic@npm:^1.2.5, get-intrinsic@npm:^1.2.6":
  version: 1.2.6
  resolution: "get-intrinsic@npm:1.2.6"
  dependencies:
    call-bind-apply-helpers: ^1.0.1
    dunder-proto: ^1.0.0
    es-define-property: ^1.0.1
    es-errors: ^1.3.0
    es-object-atoms: ^1.0.0
    function-bind: ^1.1.2
    gopd: ^1.2.0
    has-symbols: ^1.1.0
    hasown: ^2.0.2
    math-intrinsics: ^1.0.0
  checksum: a7592a0b7f023a2e83c0121fa9449ca83780e370a5feeebe8452119474d148016e43b455049134ae7a683b9b11b93d3f65eac199a0ad452ab740d5f0c299de47
  languageName: node
  linkType: hard

"get-stream@npm:^4.0.0, get-stream@npm:^4.1.0":
  version: 4.1.0
  resolution: "get-stream@npm:4.1.0"
  dependencies:
    pump: ^3.0.0
  checksum: 443e1914170c15bd52ff8ea6eff6dfc6d712b031303e36302d2778e3de2506af9ee964d6124010f7818736dcfde05c04ba7ca6cc26883106e084357a17ae7d73
  languageName: node
  linkType: hard

"get-stream@npm:^5.1.0":
  version: 5.2.0
  resolution: "get-stream@npm:5.2.0"
  dependencies:
    pump: ^3.0.0
  checksum: 8bc1a23174a06b2b4ce600df38d6c98d2ef6d84e020c1ddad632ad75bac4e092eeb40e4c09e0761c35fc2dbc5e7fff5dab5e763a383582c4a167dd69a905bd12
  languageName: node
  linkType: hard

"get-stream@npm:^6.0.0":
  version: 6.0.1
  resolution: "get-stream@npm:6.0.1"
  checksum: e04ecece32c92eebf5b8c940f51468cd53554dcbb0ea725b2748be583c9523d00128137966afce410b9b051eb2ef16d657cd2b120ca8edafcf5a65e81af63cad
  languageName: node
  linkType: hard

"get-symbol-description@npm:^1.0.2":
  version: 1.0.2
  resolution: "get-symbol-description@npm:1.0.2"
  dependencies:
    call-bind: ^1.0.5
    es-errors: ^1.3.0
    get-intrinsic: ^1.2.4
  checksum: e1cb53bc211f9dbe9691a4f97a46837a553c4e7caadd0488dc24ac694db8a390b93edd412b48dcdd0b4bbb4c595de1709effc75fc87c0839deedc6968f5bd973
  languageName: node
  linkType: hard

"get-tsconfig@npm:^4.7.0":
  version: 4.8.1
  resolution: "get-tsconfig@npm:4.8.1"
  dependencies:
    resolve-pkg-maps: ^1.0.0
  checksum: 12df01672e691d2ff6db8cf7fed1ddfef90ed94a5f3d822c63c147a26742026d582acd86afcd6f65db67d809625d17dd7f9d34f4d3f38f69bc2f48e19b2bdd5b
  languageName: node
  linkType: hard

"github-markdown-css@npm:^5.6.1":
  version: 5.8.1
  resolution: "github-markdown-css@npm:5.8.1"
  checksum: 040c4cb99d3bb42ac1ae183e4d542417731b628127b0a22f7ce771f64c567ee2125e29352aeee8827f336ab9503c42b0769e66aa6eaabf762fc935b96e6a8ebf
  languageName: node
  linkType: hard

"glob-parent@npm:^5.1.2, glob-parent@npm:~5.1.2":
  version: 5.1.2
  resolution: "glob-parent@npm:5.1.2"
  dependencies:
    is-glob: ^4.0.1
  checksum: f4f2bfe2425296e8a47e36864e4f42be38a996db40420fe434565e4480e3322f18eb37589617a98640c5dc8fdec1a387007ee18dbb1f3f5553409c34d17f425e
  languageName: node
  linkType: hard

"glob-parent@npm:^6.0.2":
  version: 6.0.2
  resolution: "glob-parent@npm:6.0.2"
  dependencies:
    is-glob: ^4.0.3
  checksum: c13ee97978bef4f55106b71e66428eb1512e71a7466ba49025fc2aec59a5bfb0954d5abd58fc5ee6c9b076eef4e1f6d3375c2e964b88466ca390da4419a786a8
  languageName: node
  linkType: hard

"glob@npm:^10.2.2, glob@npm:^10.3.10, glob@npm:^10.3.7":
  version: 10.4.5
  resolution: "glob@npm:10.4.5"
  dependencies:
    foreground-child: ^3.1.0
    jackspeak: ^3.1.2
    minimatch: ^9.0.4
    minipass: ^7.1.2
    package-json-from-dist: ^1.0.0
    path-scurry: ^1.11.1
  bin:
    glob: dist/esm/bin.mjs
  checksum: 0bc725de5e4862f9f387fd0f2b274baf16850dcd2714502ccf471ee401803997983e2c05590cb65f9675a3c6f2a58e7a53f9e365704108c6ad3cbf1d60934c4a
  languageName: node
  linkType: hard

"glob@npm:^7.1.3":
  version: 7.2.3
  resolution: "glob@npm:7.2.3"
  dependencies:
    fs.realpath: ^1.0.0
    inflight: ^1.0.4
    inherits: 2
    minimatch: ^3.1.1
    once: ^1.3.0
    path-is-absolute: ^1.0.0
  checksum: 29452e97b38fa704dabb1d1045350fb2467cf0277e155aa9ff7077e90ad81d1ea9d53d3ee63bd37c05b09a065e90f16aec4a65f5b8de401d1dac40bc5605d133
  languageName: node
  linkType: hard

"global-dirs@npm:^3.0.0":
  version: 3.0.1
  resolution: "global-dirs@npm:3.0.1"
  dependencies:
    ini: 2.0.0
  checksum: 70147b80261601fd40ac02a104581432325c1c47329706acd773f3a6ce99bb36d1d996038c85ccacd482ad22258ec233c586b6a91535b1a116b89663d49d6438
  languageName: node
  linkType: hard

"globals@npm:^11.1.0":
  version: 11.12.0
  resolution: "globals@npm:11.12.0"
  checksum: 67051a45eca3db904aee189dfc7cd53c20c7d881679c93f6146ddd4c9f4ab2268e68a919df740d39c71f4445d2b38ee360fc234428baea1dbdfe68bbcb46979e
  languageName: node
  linkType: hard

"globals@npm:^13.19.0, globals@npm:^13.24.0":
  version: 13.24.0
  resolution: "globals@npm:13.24.0"
  dependencies:
    type-fest: ^0.20.2
  checksum: 56066ef058f6867c04ff203b8a44c15b038346a62efbc3060052a1016be9f56f4cf0b2cd45b74b22b81e521a889fc7786c73691b0549c2f3a6e825b3d394f43c
  languageName: node
  linkType: hard

"globalthis@npm:^1.0.4":
  version: 1.0.4
  resolution: "globalthis@npm:1.0.4"
  dependencies:
    define-properties: ^1.2.1
    gopd: ^1.0.1
  checksum: 39ad667ad9f01476474633a1834a70842041f70a55571e8dcef5fb957980a92da5022db5430fca8aecc5d47704ae30618c0bc877a579c70710c904e9ef06108a
  languageName: node
  linkType: hard

"globby@npm:^11.1.0":
  version: 11.1.0
  resolution: "globby@npm:11.1.0"
  dependencies:
    array-union: ^2.1.0
    dir-glob: ^3.0.1
    fast-glob: ^3.2.9
    ignore: ^5.2.0
    merge2: ^1.4.1
    slash: ^3.0.0
  checksum: b4be8885e0cfa018fc783792942d53926c35c50b3aefd3fdcfb9d22c627639dc26bd2327a40a0b74b074100ce95bb7187bfeae2f236856aa3de183af7a02aea6
  languageName: node
  linkType: hard

"gopd@npm:^1.0.1, gopd@npm:^1.2.0":
  version: 1.2.0
  resolution: "gopd@npm:1.2.0"
  checksum: cc6d8e655e360955bdccaca51a12a474268f95bb793fc3e1f2bdadb075f28bfd1fd988dab872daf77a61d78cbaf13744bc8727a17cfb1d150d76047d805375f3
  languageName: node
  linkType: hard

"got@npm:^9.6.0":
  version: 9.6.0
  resolution: "got@npm:9.6.0"
  dependencies:
    "@sindresorhus/is": ^0.14.0
    "@szmarczak/http-timer": ^1.1.2
    cacheable-request: ^6.0.0
    decompress-response: ^3.3.0
    duplexer3: ^0.1.4
    get-stream: ^4.1.0
    lowercase-keys: ^1.0.1
    mimic-response: ^1.0.1
    p-cancelable: ^1.0.0
    to-readable-stream: ^1.0.0
    url-parse-lax: ^3.0.0
  checksum: 941807bd9704bacf5eb401f0cc1212ffa1f67c6642f2d028fd75900471c221b1da2b8527f4553d2558f3faeda62ea1cf31665f8b002c6137f5de8732f07370b0
  languageName: node
  linkType: hard

"graceful-fs@npm:^4.1.2, graceful-fs@npm:^4.2.6":
  version: 4.2.11
  resolution: "graceful-fs@npm:4.2.11"
  checksum: ac85f94da92d8eb6b7f5a8b20ce65e43d66761c55ce85ac96df6865308390da45a8d3f0296dd3a663de65d30ba497bd46c696cc1e248c72b13d6d567138a4fc7
  languageName: node
  linkType: hard

"graphemer@npm:^1.4.0":
  version: 1.4.0
  resolution: "graphemer@npm:1.4.0"
  checksum: bab8f0be9b568857c7bec9fda95a89f87b783546d02951c40c33f84d05bb7da3fd10f863a9beb901463669b6583173a8c8cc6d6b306ea2b9b9d5d3d943c3a673
  languageName: node
  linkType: hard

"has-bigints@npm:^1.0.2":
  version: 1.0.2
  resolution: "has-bigints@npm:1.0.2"
  checksum: 390e31e7be7e5c6fe68b81babb73dfc35d413604d7ee5f56da101417027a4b4ce6a27e46eff97ad040c835b5d228676eae99a9b5c3bc0e23c8e81a49241ff45b
  languageName: node
  linkType: hard

"has-flag@npm:^3.0.0":
  version: 3.0.0
  resolution: "has-flag@npm:3.0.0"
  checksum: 4a15638b454bf086c8148979aae044dd6e39d63904cd452d970374fa6a87623423da485dfb814e7be882e05c096a7ccf1ebd48e7e7501d0208d8384ff4dea73b
  languageName: node
  linkType: hard

"has-flag@npm:^4.0.0":
  version: 4.0.0
  resolution: "has-flag@npm:4.0.0"
  checksum: 261a1357037ead75e338156b1f9452c016a37dcd3283a972a30d9e4a87441ba372c8b81f818cd0fbcd9c0354b4ae7e18b9e1afa1971164aef6d18c2b6095a8ad
  languageName: node
  linkType: hard

"has-property-descriptors@npm:^1.0.0, has-property-descriptors@npm:^1.0.2":
  version: 1.0.2
  resolution: "has-property-descriptors@npm:1.0.2"
  dependencies:
    es-define-property: ^1.0.0
  checksum: fcbb246ea2838058be39887935231c6d5788babed499d0e9d0cc5737494c48aba4fe17ba1449e0d0fbbb1e36175442faa37f9c427ae357d6ccb1d895fbcd3de3
  languageName: node
  linkType: hard

"has-proto@npm:^1.0.3":
  version: 1.2.0
  resolution: "has-proto@npm:1.2.0"
  dependencies:
    dunder-proto: ^1.0.0
  checksum: f55010cb94caa56308041d77967c72a02ffd71386b23f9afa8447e58bc92d49d15c19bf75173713468e92fe3fb1680b03b115da39c21c32c74886d1d50d3e7ff
  languageName: node
  linkType: hard

"has-symbols@npm:^1.0.3, has-symbols@npm:^1.1.0":
  version: 1.1.0
  resolution: "has-symbols@npm:1.1.0"
  checksum: b2316c7302a0e8ba3aaba215f834e96c22c86f192e7310bdf689dd0e6999510c89b00fbc5742571507cebf25764d68c988b3a0da217369a73596191ac0ce694b
  languageName: node
  linkType: hard

"has-tostringtag@npm:^1.0.0, has-tostringtag@npm:^1.0.2":
  version: 1.0.2
  resolution: "has-tostringtag@npm:1.0.2"
  dependencies:
    has-symbols: ^1.0.3
  checksum: 999d60bb753ad714356b2c6c87b7fb74f32463b8426e159397da4bde5bca7e598ab1073f4d8d4deafac297f2eb311484cd177af242776bf05f0d11565680468d
  languageName: node
  linkType: hard

"has-yarn@npm:^2.1.0":
  version: 2.1.0
  resolution: "has-yarn@npm:2.1.0"
  checksum: 5eb1d0bb8518103d7da24532bdbc7124ffc6d367b5d3c10840b508116f2f1bcbcf10fd3ba843ff6e2e991bdf9969fd862d42b2ed58aade88343326c950b7e7f7
  languageName: node
  linkType: hard

"hasown@npm:^2.0.0, hasown@npm:^2.0.1, hasown@npm:^2.0.2":
  version: 2.0.2
  resolution: "hasown@npm:2.0.2"
  dependencies:
    function-bind: ^1.1.2
  checksum: e8516f776a15149ca6c6ed2ae3110c417a00b62260e222590e54aa367cbcd6ed99122020b37b7fbdf05748df57b265e70095d7bf35a47660587619b15ffb93db
  languageName: node
  linkType: hard

"he@npm:^1.2.0":
  version: 1.2.0
  resolution: "he@npm:1.2.0"
  bin:
    he: bin/he
  checksum: 3d4d6babccccd79c5c5a3f929a68af33360d6445587d628087f39a965079d84f18ce9c3d3f917ee1e3978916fc833bb8b29377c3b403f919426f91bc6965e7a7
  languageName: node
  linkType: hard

"html-tags@npm:^3.3.1":
  version: 3.3.1
  resolution: "html-tags@npm:3.3.1"
  checksum: b4ef1d5a76b678e43cce46e3783d563607b1d550cab30b4f511211564574770aa8c658a400b100e588bc60b8234e59b35ff72c7851cc28f3b5403b13a2c6cbce
  languageName: node
  linkType: hard

"htmlparser2@npm:^9.1.0":
  version: 9.1.0
  resolution: "htmlparser2@npm:9.1.0"
  dependencies:
    domelementtype: ^2.3.0
    domhandler: ^5.0.3
    domutils: ^3.1.0
    entities: ^4.5.0
  checksum: e5f8d5193967e4a500226f37bdf2c0f858cecb39dde14d0439f24bf2c461a4342778740d988fbaba652b0e4cb6052f7f2e99e69fc1a329a86c629032bb76e7c8
  languageName: node
  linkType: hard

"http-assert@npm:^1.3.0":
  version: 1.5.0
  resolution: "http-assert@npm:1.5.0"
  dependencies:
    deep-equal: ~1.0.1
    http-errors: ~1.8.0
  checksum: 69c9b3c14cf8b2822916360a365089ce936c883c49068f91c365eccba5c141a9964d19fdda589150a480013bf503bf37d8936c732e9635819339e730ab0e7527
  languageName: node
  linkType: hard

"http-cache-semantics@npm:^4.0.0, http-cache-semantics@npm:^4.1.1":
  version: 4.1.1
  resolution: "http-cache-semantics@npm:4.1.1"
  checksum: 83ac0bc60b17a3a36f9953e7be55e5c8f41acc61b22583060e8dedc9dd5e3607c823a88d0926f9150e571f90946835c7fe150732801010845c72cd8bbff1a236
  languageName: node
  linkType: hard

"http-errors@npm:2.0.0, http-errors@npm:^2.0.0":
  version: 2.0.0
  resolution: "http-errors@npm:2.0.0"
  dependencies:
    depd: 2.0.0
    inherits: 2.0.4
    setprototypeof: 1.2.0
    statuses: 2.0.1
    toidentifier: 1.0.1
  checksum: 9b0a3782665c52ce9dc658a0d1560bcb0214ba5699e4ea15aefb2a496e2ca83db03ebc42e1cce4ac1f413e4e0d2d736a3fd755772c556a9a06853ba2a0b7d920
  languageName: node
  linkType: hard

"http-errors@npm:^1.6.3, http-errors@npm:^1.7.3, http-errors@npm:~1.8.0":
  version: 1.8.1
  resolution: "http-errors@npm:1.8.1"
  dependencies:
    depd: ~1.1.2
    inherits: 2.0.4
    setprototypeof: 1.2.0
    statuses: ">= 1.5.0 < 2"
    toidentifier: 1.0.1
  checksum: d3c7e7e776fd51c0a812baff570bdf06fe49a5dc448b700ab6171b1250e4cf7db8b8f4c0b133e4bfe2451022a5790c1ca6c2cae4094dedd6ac8304a1267f91d2
  languageName: node
  linkType: hard

"http-errors@npm:~1.6.2":
  version: 1.6.3
  resolution: "http-errors@npm:1.6.3"
  dependencies:
    depd: ~1.1.2
    inherits: 2.0.3
    setprototypeof: 1.1.0
    statuses: ">= 1.4.0 < 2"
  checksum: a9654ee027e3d5de305a56db1d1461f25709ac23267c6dc28cdab8323e3f96caa58a9a6a5e93ac15d7285cee0c2f019378c3ada9026e7fe19c872d695f27de7c
  languageName: node
  linkType: hard

"http-proxy-agent@npm:^7.0.0":
  version: 7.0.2
  resolution: "http-proxy-agent@npm:7.0.2"
  dependencies:
    agent-base: ^7.1.0
    debug: ^4.3.4
  checksum: 670858c8f8f3146db5889e1fa117630910101db601fff7d5a8aa637da0abedf68c899f03d3451cac2f83bcc4c3d2dabf339b3aa00ff8080571cceb02c3ce02f3
  languageName: node
  linkType: hard

"https-proxy-agent@npm:^5.0.0":
  version: 5.0.1
  resolution: "https-proxy-agent@npm:5.0.1"
  dependencies:
    agent-base: 6
    debug: 4
  checksum: 571fccdf38184f05943e12d37d6ce38197becdd69e58d03f43637f7fa1269cf303a7d228aa27e5b27bbd3af8f09fd938e1c91dcfefff2df7ba77c20ed8dfc765
  languageName: node
  linkType: hard

"https-proxy-agent@npm:^7.0.1":
  version: 7.0.6
  resolution: "https-proxy-agent@npm:7.0.6"
  dependencies:
    agent-base: ^7.1.2
    debug: 4
  checksum: b882377a120aa0544846172e5db021fa8afbf83fea2a897d397bd2ddd8095ab268c24bc462f40a15f2a8c600bf4aa05ce52927f70038d4014e68aefecfa94e8d
  languageName: node
  linkType: hard

"human-signals@npm:^2.1.0":
  version: 2.1.0
  resolution: "human-signals@npm:2.1.0"
  checksum: b87fd89fce72391625271454e70f67fe405277415b48bcc0117ca73d31fa23a4241787afdc8d67f5a116cf37258c052f59ea82daffa72364d61351423848e3b8
  languageName: node
  linkType: hard

"humanize-ms@npm:^1.2.1":
  version: 1.2.1
  resolution: "humanize-ms@npm:1.2.1"
  dependencies:
    ms: ^2.0.0
  checksum: 9c7a74a2827f9294c009266c82031030eae811ca87b0da3dceb8d6071b9bde22c9f3daef0469c3c533cc67a97d8a167cd9fc0389350e5f415f61a79b171ded16
  languageName: node
  linkType: hard

"iconv-lite@npm:0.4.24, iconv-lite@npm:^0.4.24":
  version: 0.4.24
  resolution: "iconv-lite@npm:0.4.24"
  dependencies:
    safer-buffer: ">= 2.1.2 < 3"
  checksum: bd9f120f5a5b306f0bc0b9ae1edeb1577161503f5f8252a20f1a9e56ef8775c9959fd01c55f2d3a39d9a8abaf3e30c1abeb1895f367dcbbe0a8fd1c9ca01c4f6
  languageName: node
  linkType: hard

"iconv-lite@npm:0.6.3, iconv-lite@npm:^0.6.2, iconv-lite@npm:^0.6.3":
  version: 0.6.3
  resolution: "iconv-lite@npm:0.6.3"
  dependencies:
    safer-buffer: ">= 2.1.2 < 3.0.0"
  checksum: 3f60d47a5c8fc3313317edfd29a00a692cc87a19cac0159e2ce711d0ebc9019064108323b5e493625e25594f11c6236647d8e256fbe7a58f4a3b33b89e6d30bf
  languageName: node
  linkType: hard

"ieee754@npm:^1.1.13":
  version: 1.2.1
  resolution: "ieee754@npm:1.2.1"
  checksum: 5144c0c9815e54ada181d80a0b810221a253562422e7c6c3a60b1901154184f49326ec239d618c416c1c5945a2e197107aee8d986a3dd836b53dffefd99b5e7e
  languageName: node
  linkType: hard

"ignore-by-default@npm:^1.0.1":
  version: 1.0.1
  resolution: "ignore-by-default@npm:1.0.1"
  checksum: 441509147b3615e0365e407a3c18e189f78c07af08564176c680be1fabc94b6c789cad1342ad887175d4ecd5225de86f73d376cec8e06b42fd9b429505ffcf8a
  languageName: node
  linkType: hard

"ignore@npm:^5.1.1, ignore@npm:^5.2.0, ignore@npm:^5.2.4, ignore@npm:^5.3.0, ignore@npm:^5.3.1":
  version: 5.3.2
  resolution: "ignore@npm:5.3.2"
  checksum: 2acfd32a573260ea522ea0bfeff880af426d68f6831f973129e2ba7363f422923cf53aab62f8369cbf4667c7b25b6f8a3761b34ecdb284ea18e87a5262a865be
  languageName: node
  linkType: hard

"import-fresh@npm:^3.2.1":
  version: 3.3.0
  resolution: "import-fresh@npm:3.3.0"
  dependencies:
    parent-module: ^1.0.0
    resolve-from: ^4.0.0
  checksum: 2cacfad06e652b1edc50be650f7ec3be08c5e5a6f6d12d035c440a42a8cc028e60a5b99ca08a77ab4d6b1346da7d971915828f33cdab730d3d42f08242d09baa
  languageName: node
  linkType: hard

"import-lazy@npm:^2.1.0":
  version: 2.1.0
  resolution: "import-lazy@npm:2.1.0"
  checksum: 05294f3b9dd4971d3a996f0d2f176410fb6745d491d6e73376429189f5c1c3d290548116b2960a7cf3e89c20cdf11431739d1d2d8c54b84061980795010e803a
  languageName: node
  linkType: hard

"imurmurhash@npm:^0.1.4":
  version: 0.1.4
  resolution: "imurmurhash@npm:0.1.4"
  checksum: 7cae75c8cd9a50f57dadd77482359f659eaebac0319dd9368bcd1714f55e65badd6929ca58569da2b6494ef13fdd5598cd700b1eba23f8b79c5f19d195a3ecf7
  languageName: node
  linkType: hard

"inflation@npm:^2.0.0":
  version: 2.1.0
  resolution: "inflation@npm:2.1.0"
  checksum: ********************************************************************************************************************************
  languageName: node
  linkType: hard

"inflight@npm:^1.0.4":
  version: 1.0.6
  resolution: "inflight@npm:1.0.6"
  dependencies:
    once: ^1.3.0
    wrappy: 1
  checksum: f4f76aa072ce19fae87ce1ef7d221e709afb59d445e05d47fba710e85470923a75de35bfae47da6de1b18afc3ce83d70facf44cfb0aff89f0a3f45c0a0244dfd
  languageName: node
  linkType: hard

"inherits@npm:2, inherits@npm:2.0.4, inherits@npm:^2.0.3, inherits@npm:^2.0.4":
  version: 2.0.4
  resolution: "inherits@npm:2.0.4"
  checksum: 4a48a733847879d6cf6691860a6b1e3f0f4754176e4d71494c41f3475553768b10f84b5ce1d40fbd0e34e6bfbb864ee35858ad4dd2cf31e02fc4a154b724d7f1
  languageName: node
  linkType: hard

"inherits@npm:2.0.3":
  version: 2.0.3
  resolution: "inherits@npm:2.0.3"
  checksum: 78cb8d7d850d20a5e9a7f3620db31483aa00ad5f722ce03a55b110e5a723539b3716a3b463e2b96ce3fe286f33afc7c131fa2f91407528ba80cea98a7545d4c0
  languageName: node
  linkType: hard

"ini@npm:2.0.0":
  version: 2.0.0
  resolution: "ini@npm:2.0.0"
  checksum: e7aadc5fb2e4aefc666d74ee2160c073995a4061556b1b5b4241ecb19ad609243b9cceafe91bae49c219519394bbd31512516cb22a3b1ca6e66d869e0447e84e
  languageName: node
  linkType: hard

"ini@npm:~1.3.0":
  version: 1.3.8
  resolution: "ini@npm:1.3.8"
  checksum: dfd98b0ca3a4fc1e323e38a6c8eb8936e31a97a918d3b377649ea15bdb15d481207a0dda1021efbd86b464cae29a0d33c1d7dcaf6c5672bee17fa849bc50a1b3
  languageName: node
  linkType: hard

"internal-slot@npm:^1.0.7":
  version: 1.1.0
  resolution: "internal-slot@npm:1.1.0"
  dependencies:
    es-errors: ^1.3.0
    hasown: ^2.0.2
    side-channel: ^1.1.0
  checksum: 8e0991c2d048cc08dab0a91f573c99f6a4215075887517ea4fa32203ce8aea60fa03f95b177977fa27eb502e5168366d0f3e02c762b799691411d49900611861
  languageName: node
  linkType: hard

"ip-address@npm:^9.0.5":
  version: 9.0.5
  resolution: "ip-address@npm:9.0.5"
  dependencies:
    jsbn: 1.1.0
    sprintf-js: ^1.1.3
  checksum: aa15f12cfd0ef5e38349744e3654bae649a34c3b10c77a674a167e99925d1549486c5b14730eebce9fea26f6db9d5e42097b00aa4f9f612e68c79121c71652dc
  languageName: node
  linkType: hard

"is-array-buffer@npm:^3.0.4":
  version: 3.0.4
  resolution: "is-array-buffer@npm:3.0.4"
  dependencies:
    call-bind: ^1.0.2
    get-intrinsic: ^1.2.1
  checksum: e4e3e6ef0ff2239e75371d221f74bc3c26a03564a22efb39f6bb02609b598917ddeecef4e8c877df2a25888f247a98198959842a5e73236bc7f22cabdf6351a7
  languageName: node
  linkType: hard

"is-arrayish@npm:^0.3.1":
  version: 0.3.2
  resolution: "is-arrayish@npm:0.3.2"
  checksum: 977e64f54d91c8f169b59afcd80ff19227e9f5c791fa28fa2e5bce355cbaf6c2c356711b734656e80c9dd4a854dd7efcf7894402f1031dfc5de5d620775b4d5f
  languageName: node
  linkType: hard

"is-async-function@npm:^2.0.0":
  version: 2.0.0
  resolution: "is-async-function@npm:2.0.0"
  dependencies:
    has-tostringtag: ^1.0.0
  checksum: e3471d95e6c014bf37cad8a93f2f4b6aac962178e0a5041e8903147166964fdc1c5c1d2ef87e86d77322c370ca18f2ea004fa7420581fa747bcaf7c223069dbd
  languageName: node
  linkType: hard

"is-bigint@npm:^1.1.0":
  version: 1.1.0
  resolution: "is-bigint@npm:1.1.0"
  dependencies:
    has-bigints: ^1.0.2
  checksum: ee1544f0e664f253306786ed1dce494b8cf242ef415d6375d8545b4d8816b0f054bd9f948a8988ae2c6325d1c28260dd02978236b2f7b8fb70dfc4838a6c9fa7
  languageName: node
  linkType: hard

"is-binary-path@npm:~2.1.0":
  version: 2.1.0
  resolution: "is-binary-path@npm:2.1.0"
  dependencies:
    binary-extensions: ^2.0.0
  checksum: 84192eb88cff70d320426f35ecd63c3d6d495da9d805b19bc65b518984b7c0760280e57dbf119b7e9be6b161784a5a673ab2c6abe83abb5198a432232ad5b35c
  languageName: node
  linkType: hard

"is-boolean-object@npm:^1.2.0":
  version: 1.2.1
  resolution: "is-boolean-object@npm:1.2.1"
  dependencies:
    call-bound: ^1.0.2
    has-tostringtag: ^1.0.2
  checksum: 2672609f0f2536172873810a38ec006a415e43ddc6a240f7638a1659cb20dfa91cc75c8a1bed36247bb046aa8f0eab945f20d1203bc69606418bd129c745f861
  languageName: node
  linkType: hard

"is-builtin-module@npm:^3.2.1":
  version: 3.2.1
  resolution: "is-builtin-module@npm:3.2.1"
  dependencies:
    builtin-modules: ^3.3.0
  checksum: e8f0ffc19a98240bda9c7ada84d846486365af88d14616e737d280d378695c8c448a621dcafc8332dbf0fcd0a17b0763b845400709963fa9151ddffece90ae88
  languageName: node
  linkType: hard

"is-callable@npm:^1.1.3, is-callable@npm:^1.2.7":
  version: 1.2.7
  resolution: "is-callable@npm:1.2.7"
  checksum: 61fd57d03b0d984e2ed3720fb1c7a897827ea174bd44402878e059542ea8c4aeedee0ea0985998aa5cc2736b2fa6e271c08587addb5b3959ac52cf665173d1ac
  languageName: node
  linkType: hard

"is-ci@npm:^2.0.0":
  version: 2.0.0
  resolution: "is-ci@npm:2.0.0"
  dependencies:
    ci-info: ^2.0.0
  bin:
    is-ci: bin.js
  checksum: 77b869057510f3efa439bbb36e9be429d53b3f51abd4776eeea79ab3b221337fe1753d1e50058a9e2c650d38246108beffb15ccfd443929d77748d8c0cc90144
  languageName: node
  linkType: hard

"is-core-module@npm:^2.12.1, is-core-module@npm:^2.13.0, is-core-module@npm:^2.15.1, is-core-module@npm:^2.16.0":
  version: 2.16.0
  resolution: "is-core-module@npm:2.16.0"
  dependencies:
    hasown: ^2.0.2
  checksum: 98aa14eaee864c2e86fff4e08813e0da3ca7ac38dd0a43b9f10aeec48bfbc4827e2677349adb626f66921caef7fa9e2745c80b8b7cafe8ac24d46dc8124cc216
  languageName: node
  linkType: hard

"is-data-view@npm:^1.0.1":
  version: 1.0.2
  resolution: "is-data-view@npm:1.0.2"
  dependencies:
    call-bound: ^1.0.2
    get-intrinsic: ^1.2.6
    is-typed-array: ^1.1.13
  checksum: 31600dd19932eae7fd304567e465709ffbfa17fa236427c9c864148e1b54eb2146357fcf3aed9b686dee13c217e1bb5a649cb3b9c479e1004c0648e9febde1b2
  languageName: node
  linkType: hard

"is-date-object@npm:^1.0.5, is-date-object@npm:^1.1.0":
  version: 1.1.0
  resolution: "is-date-object@npm:1.1.0"
  dependencies:
    call-bound: ^1.0.2
    has-tostringtag: ^1.0.2
  checksum: d6c36ab9d20971d65f3fc64cef940d57a4900a2ac85fb488a46d164c2072a33da1cb51eefcc039e3e5c208acbce343d3480b84ab5ff0983f617512da2742562a
  languageName: node
  linkType: hard

"is-docker@npm:^2.0.0, is-docker@npm:^2.1.1":
  version: 2.2.1
  resolution: "is-docker@npm:2.2.1"
  bin:
    is-docker: cli.js
  checksum: 3fef7ddbf0be25958e8991ad941901bf5922ab2753c46980b60b05c1bf9c9c2402d35e6dc32e4380b980ef5e1970a5d9d5e5aa2e02d77727c3b6b5e918474c56
  languageName: node
  linkType: hard

"is-extglob@npm:^2.1.1":
  version: 2.1.1
  resolution: "is-extglob@npm:2.1.1"
  checksum: df033653d06d0eb567461e58a7a8c9f940bd8c22274b94bf7671ab36df5719791aae15eef6d83bbb5e23283967f2f984b8914559d4449efda578c775c4be6f85
  languageName: node
  linkType: hard

"is-finalizationregistry@npm:^1.1.0":
  version: 1.1.0
  resolution: "is-finalizationregistry@npm:1.1.0"
  dependencies:
    call-bind: ^1.0.7
  checksum: 480818ab86e112a00444410a2fd551a5363bca0c39c7bc66e29df665b1e47c803ba107227c1db86d67264a3f020779fab257061463ce02b01b6abbe5966e33b8
  languageName: node
  linkType: hard

"is-fullwidth-code-point@npm:^3.0.0":
  version: 3.0.0
  resolution: "is-fullwidth-code-point@npm:3.0.0"
  checksum: 44a30c29457c7fb8f00297bce733f0a64cd22eca270f83e58c105e0d015e45c019491a4ab2faef91ab51d4738c670daff901c799f6a700e27f7314029e99e348
  languageName: node
  linkType: hard

"is-generator-function@npm:^1.0.10, is-generator-function@npm:^1.0.7":
  version: 1.0.10
  resolution: "is-generator-function@npm:1.0.10"
  dependencies:
    has-tostringtag: ^1.0.0
  checksum: d54644e7dbaccef15ceb1e5d91d680eb5068c9ee9f9eb0a9e04173eb5542c9b51b5ab52c5537f5703e48d5fddfd376817c1ca07a84a407b7115b769d4bdde72b
  languageName: node
  linkType: hard

"is-glob@npm:^4.0.0, is-glob@npm:^4.0.1, is-glob@npm:^4.0.3, is-glob@npm:~4.0.1":
  version: 4.0.3
  resolution: "is-glob@npm:4.0.3"
  dependencies:
    is-extglob: ^2.1.1
  checksum: d381c1319fcb69d341cc6e6c7cd588e17cd94722d9a32dbd60660b993c4fb7d0f19438674e68dfec686d09b7c73139c9166b47597f846af387450224a8101ab4
  languageName: node
  linkType: hard

"is-installed-globally@npm:^0.4.0":
  version: 0.4.0
  resolution: "is-installed-globally@npm:0.4.0"
  dependencies:
    global-dirs: ^3.0.0
    is-path-inside: ^3.0.2
  checksum: 3359840d5982d22e9b350034237b2cda2a12bac1b48a721912e1ab8e0631dd07d45a2797a120b7b87552759a65ba03e819f1bd63f2d7ab8657ec0b44ee0bf399
  languageName: node
  linkType: hard

"is-interactive@npm:^1.0.0":
  version: 1.0.0
  resolution: "is-interactive@npm:1.0.0"
  checksum: 824808776e2d468b2916cdd6c16acacebce060d844c35ca6d82267da692e92c3a16fdba624c50b54a63f38bdc4016055b6f443ce57d7147240de4f8cdabaf6f9
  languageName: node
  linkType: hard

"is-map@npm:^2.0.3":
  version: 2.0.3
  resolution: "is-map@npm:2.0.3"
  checksum: e6ce5f6380f32b141b3153e6ba9074892bbbbd655e92e7ba5ff195239777e767a976dcd4e22f864accaf30e53ebf961ab1995424aef91af68788f0591b7396cc
  languageName: node
  linkType: hard

"is-negative-zero@npm:^2.0.3":
  version: 2.0.3
  resolution: "is-negative-zero@npm:2.0.3"
  checksum: c1e6b23d2070c0539d7b36022d5a94407132411d01aba39ec549af824231f3804b1aea90b5e4e58e807a65d23ceb538ed6e355ce76b267bdd86edb757ffcbdcd
  languageName: node
  linkType: hard

"is-npm@npm:^5.0.0":
  version: 5.0.0
  resolution: "is-npm@npm:5.0.0"
  checksum: 9baff02b0c69a3d3c79b162cb2f9e67fb40ef6d172c16601b2e2471c21e9a4fa1fc9885a308d7bc6f3a3cd2a324c27fa0bf284c133c3349bb22571ab70d041cc
  languageName: node
  linkType: hard

"is-number-object@npm:^1.1.0":
  version: 1.1.0
  resolution: "is-number-object@npm:1.1.0"
  dependencies:
    call-bind: ^1.0.7
    has-tostringtag: ^1.0.2
  checksum: 965f91493e5c02a44bb9c5d8dd4ae40da20bd9bd1cff9cd92e2f2e66a486935a0a01f8a4744eab033c450888f01a4ec3226e1c75bbcff973ce12d06ed79eb17b
  languageName: node
  linkType: hard

"is-number@npm:^7.0.0":
  version: 7.0.0
  resolution: "is-number@npm:7.0.0"
  checksum: 456ac6f8e0f3111ed34668a624e45315201dff921e5ac181f8ec24923b99e9f32ca1a194912dc79d539c97d33dba17dc635202ff0b2cf98326f608323276d27a
  languageName: node
  linkType: hard

"is-obj@npm:^2.0.0":
  version: 2.0.0
  resolution: "is-obj@npm:2.0.0"
  checksum: c9916ac8f4621962a42f5e80e7ffdb1d79a3fab7456ceaeea394cd9e0858d04f985a9ace45be44433bf605673c8be8810540fe4cc7f4266fc7526ced95af5a08
  languageName: node
  linkType: hard

"is-path-inside@npm:^3.0.2, is-path-inside@npm:^3.0.3":
  version: 3.0.3
  resolution: "is-path-inside@npm:3.0.3"
  checksum: abd50f06186a052b349c15e55b182326f1936c89a78bf6c8f2b707412517c097ce04bc49a0ca221787bc44e1049f51f09a2ffb63d22899051988d3a618ba13e9
  languageName: node
  linkType: hard

"is-regex@npm:^1.1.4, is-regex@npm:^1.2.1":
  version: 1.2.1
  resolution: "is-regex@npm:1.2.1"
  dependencies:
    call-bound: ^1.0.2
    gopd: ^1.2.0
    has-tostringtag: ^1.0.2
    hasown: ^2.0.2
  checksum: 99ee0b6d30ef1bb61fa4b22fae7056c6c9b3c693803c0c284ff7a8570f83075a7d38cda53b06b7996d441215c27895ea5d1af62124562e13d91b3dbec41a5e13
  languageName: node
  linkType: hard

"is-set@npm:^2.0.3":
  version: 2.0.3
  resolution: "is-set@npm:2.0.3"
  checksum: 36e3f8c44bdbe9496c9689762cc4110f6a6a12b767c5d74c0398176aa2678d4467e3bf07595556f2dba897751bde1422480212b97d973c7b08a343100b0c0dfe
  languageName: node
  linkType: hard

"is-shared-array-buffer@npm:^1.0.2, is-shared-array-buffer@npm:^1.0.3":
  version: 1.0.3
  resolution: "is-shared-array-buffer@npm:1.0.3"
  dependencies:
    call-bind: ^1.0.7
  checksum: a4fff602c309e64ccaa83b859255a43bb011145a42d3f56f67d9268b55bc7e6d98a5981a1d834186ad3105d6739d21547083fe7259c76c0468483fc538e716d8
  languageName: node
  linkType: hard

"is-stream@npm:^1.1.0":
  version: 1.1.0
  resolution: "is-stream@npm:1.1.0"
  checksum: 063c6bec9d5647aa6d42108d4c59723d2bd4ae42135a2d4db6eadbd49b7ea05b750fd69d279e5c7c45cf9da753ad2c00d8978be354d65aa9f6bb434969c6a2ae
  languageName: node
  linkType: hard

"is-stream@npm:^2.0.0":
  version: 2.0.1
  resolution: "is-stream@npm:2.0.1"
  checksum: b8e05ccdf96ac330ea83c12450304d4a591f9958c11fd17bed240af8d5ffe08aedafa4c0f4cfccd4d28dc9d4d129daca1023633d5c11601a6cbc77521f6fae66
  languageName: node
  linkType: hard

"is-string@npm:^1.0.7, is-string@npm:^1.1.0":
  version: 1.1.0
  resolution: "is-string@npm:1.1.0"
  dependencies:
    call-bind: ^1.0.7
    has-tostringtag: ^1.0.2
  checksum: 1e330e9fe0984cdf37371f704f9babf9b56d50b1e9d2e6c19b8b78443be3e9771c33309b4aadde9ba2a8870769374538681e01f54113a335dd393c80a72e7d11
  languageName: node
  linkType: hard

"is-symbol@npm:^1.0.4, is-symbol@npm:^1.1.0":
  version: 1.1.1
  resolution: "is-symbol@npm:1.1.1"
  dependencies:
    call-bound: ^1.0.2
    has-symbols: ^1.1.0
    safe-regex-test: ^1.1.0
  checksum: bfafacf037af6f3c9d68820b74be4ae8a736a658a3344072df9642a090016e281797ba8edbeb1c83425879aae55d1cb1f30b38bf132d703692b2570367358032
  languageName: node
  linkType: hard

"is-typed-array@npm:^1.1.13":
  version: 1.1.13
  resolution: "is-typed-array@npm:1.1.13"
  dependencies:
    which-typed-array: ^1.1.14
  checksum: 150f9ada183a61554c91e1c4290086d2c100b0dff45f60b028519be72a8db964da403c48760723bf5253979b8dffe7b544246e0e5351dcd05c5fdb1dcc1dc0f0
  languageName: node
  linkType: hard

"is-typedarray@npm:^1.0.0":
  version: 1.0.0
  resolution: "is-typedarray@npm:1.0.0"
  checksum: 3508c6cd0a9ee2e0df2fa2e9baabcdc89e911c7bd5cf64604586697212feec525aa21050e48affb5ffc3df20f0f5d2e2cf79b08caa64e1ccc9578e251763aef7
  languageName: node
  linkType: hard

"is-unicode-supported@npm:^0.1.0":
  version: 0.1.0
  resolution: "is-unicode-supported@npm:0.1.0"
  checksum: a2aab86ee7712f5c2f999180daaba5f361bdad1efadc9610ff5b8ab5495b86e4f627839d085c6530363c6d6d4ecbde340fb8e54bdb83da4ba8e0865ed5513c52
  languageName: node
  linkType: hard

"is-weakmap@npm:^2.0.2":
  version: 2.0.2
  resolution: "is-weakmap@npm:2.0.2"
  checksum: f36aef758b46990e0d3c37269619c0a08c5b29428c0bb11ecba7f75203442d6c7801239c2f31314bc79199217ef08263787f3837d9e22610ad1da62970d6616d
  languageName: node
  linkType: hard

"is-weakref@npm:^1.0.2":
  version: 1.1.0
  resolution: "is-weakref@npm:1.1.0"
  dependencies:
    call-bound: ^1.0.2
  checksum: 2a2f3a1746ee1baecf9ac6483d903cd3f8ef3cca88e2baa42f2e85ea064bd246d218eed5f6d479fc1c76dae2231e71133b6b86160e821d176932be9fae3da4da
  languageName: node
  linkType: hard

"is-weakset@npm:^2.0.3":
  version: 2.0.3
  resolution: "is-weakset@npm:2.0.3"
  dependencies:
    call-bind: ^1.0.7
    get-intrinsic: ^1.2.4
  checksum: 8b6a20ee9f844613ff8f10962cfee49d981d584525f2357fee0a04dfbcde9fd607ed60cb6dab626dbcc470018ae6392e1ff74c0c1aced2d487271411ad9d85ae
  languageName: node
  linkType: hard

"is-wsl@npm:^2.1.1, is-wsl@npm:^2.2.0":
  version: 2.2.0
  resolution: "is-wsl@npm:2.2.0"
  dependencies:
    is-docker: ^2.0.0
  checksum: 20849846ae414997d290b75e16868e5261e86ff5047f104027026fd61d8b5a9b0b3ade16239f35e1a067b3c7cc02f70183cb661010ed16f4b6c7c93dad1b19d8
  languageName: node
  linkType: hard

"is-yarn-global@npm:^0.3.0":
  version: 0.3.0
  resolution: "is-yarn-global@npm:0.3.0"
  checksum: bca013d65fee2862024c9fbb3ba13720ffca2fe750095174c1c80922fdda16402b5c233f5ac9e265bc12ecb5446e7b7f519a32d9541788f01d4d44e24d2bf481
  languageName: node
  linkType: hard

"isarray@npm:^2.0.5":
  version: 2.0.5
  resolution: "isarray@npm:2.0.5"
  checksum: bd5bbe4104438c4196ba58a54650116007fa0262eccef13a4c55b2e09a5b36b59f1e75b9fcc49883dd9d4953892e6fc007eef9e9155648ceea036e184b0f930a
  languageName: node
  linkType: hard

"isexe@npm:^2.0.0":
  version: 2.0.0
  resolution: "isexe@npm:2.0.0"
  checksum: 26bf6c5480dda5161c820c5b5c751ae1e766c587b1f951ea3fcfc973bafb7831ae5b54a31a69bd670220e42e99ec154475025a468eae58ea262f813fdc8d1c62
  languageName: node
  linkType: hard

"isexe@npm:^3.1.1":
  version: 3.1.1
  resolution: "isexe@npm:3.1.1"
  checksum: 7fe1931ee4e88eb5aa524cd3ceb8c882537bc3a81b02e438b240e47012eef49c86904d0f0e593ea7c3a9996d18d0f1f3be8d3eaa92333977b0c3a9d353d5563e
  languageName: node
  linkType: hard

"jackspeak@npm:^3.1.2":
  version: 3.4.3
  resolution: "jackspeak@npm:3.4.3"
  dependencies:
    "@isaacs/cliui": ^8.0.2
    "@pkgjs/parseargs": ^0.11.0
  dependenciesMeta:
    "@pkgjs/parseargs":
      optional: true
  checksum: be31027fc72e7cc726206b9f560395604b82e0fddb46c4cbf9f97d049bcef607491a5afc0699612eaa4213ca5be8fd3e1e7cd187b3040988b65c9489838a7c00
  languageName: node
  linkType: hard

"jiti@npm:^1.21.6":
  version: 1.21.6
  resolution: "jiti@npm:1.21.6"
  bin:
    jiti: bin/jiti.js
  checksum: 9ea4a70a7bb950794824683ed1c632e2ede26949fbd348e2ba5ec8dc5efa54dc42022d85ae229cadaa60d4b95012e80ea07d625797199b688cc22ab0e8891d32
  languageName: node
  linkType: hard

"js-tokens@npm:^4.0.0":
  version: 4.0.0
  resolution: "js-tokens@npm:4.0.0"
  checksum: 8a95213a5a77deb6cbe94d86340e8d9ace2b93bc367790b260101d2f36a2eaf4e4e22d9fa9cf459b38af3a32fb4190e638024cf82ec95ef708680e405ea7cc78
  languageName: node
  linkType: hard

"js-tokens@npm:^9.0.1":
  version: 9.0.1
  resolution: "js-tokens@npm:9.0.1"
  checksum: 8b604020b1a550e575404bfdde4d12c11a7991ffe0c58a2cf3515b9a512992dc7010af788f0d8b7485e403d462d9e3d3b96c4ff03201550fdbb09e17c811e054
  languageName: node
  linkType: hard

"js-yaml@npm:^4.1.0":
  version: 4.1.0
  resolution: "js-yaml@npm:4.1.0"
  dependencies:
    argparse: ^2.0.1
  bin:
    js-yaml: bin/js-yaml.js
  checksum: c7830dfd456c3ef2c6e355cc5a92e6700ceafa1d14bba54497b34a99f0376cecbb3e9ac14d3e5849b426d5a5140709a66237a8c991c675431271c4ce5504151a
  languageName: node
  linkType: hard

"jsbn@npm:1.1.0":
  version: 1.1.0
  resolution: "jsbn@npm:1.1.0"
  checksum: 944f924f2bd67ad533b3850eee47603eed0f6ae425fd1ee8c760f477e8c34a05f144c1bd4f5a5dd1963141dc79a2c55f89ccc5ab77d039e7077f3ad196b64965
  languageName: node
  linkType: hard

"jsesc@npm:^3.0.2":
  version: 3.1.0
  resolution: "jsesc@npm:3.1.0"
  bin:
    jsesc: bin/jsesc
  checksum: 19c94095ea026725540c0d29da33ab03144f6bcf2d4159e4833d534976e99e0c09c38cefa9a575279a51fc36b31166f8d6d05c9fe2645d5f15851d690b41f17f
  languageName: node
  linkType: hard

"json-bigint@npm:^1.0.0":
  version: 1.0.0
  resolution: "json-bigint@npm:1.0.0"
  dependencies:
    bignumber.js: ^9.0.0
  checksum: c67bb93ccb3c291e60eb4b62931403e378906aab113ec1c2a8dd0f9a7f065ad6fd9713d627b732abefae2e244ac9ce1721c7a3142b2979532f12b258634ce6f6
  languageName: node
  linkType: hard

"json-buffer@npm:3.0.0":
  version: 3.0.0
  resolution: "json-buffer@npm:3.0.0"
  checksum: 0cecacb8025370686a916069a2ff81f7d55167421b6aa7270ee74e244012650dd6bce22b0852202ea7ff8624fce50ff0ec1bdf95914ccb4553426e290d5a63fa
  languageName: node
  linkType: hard

"json-buffer@npm:3.0.1":
  version: 3.0.1
  resolution: "json-buffer@npm:3.0.1"
  checksum: 9026b03edc2847eefa2e37646c579300a1f3a4586cfb62bf857832b60c852042d0d6ae55d1afb8926163fa54c2b01d83ae24705f34990348bdac6273a29d4581
  languageName: node
  linkType: hard

"json-schema-traverse@npm:^0.4.1":
  version: 0.4.1
  resolution: "json-schema-traverse@npm:0.4.1"
  checksum: 7486074d3ba247769fda17d5181b345c9fb7d12e0da98b22d1d71a5db9698d8b4bd900a3ec1a4ffdd60846fc2556274a5c894d0c48795f14cb03aeae7b55260b
  languageName: node
  linkType: hard

"json-schema-traverse@npm:^1.0.0":
  version: 1.0.0
  resolution: "json-schema-traverse@npm:1.0.0"
  checksum: 02f2f466cdb0362558b2f1fd5e15cce82ef55d60cd7f8fa828cf35ba74330f8d767fcae5c5c2adb7851fa811766c694b9405810879bc4e1ddd78a7c0e03658ad
  languageName: node
  linkType: hard

"json-schema-typed@npm:^7.0.3":
  version: 7.0.3
  resolution: "json-schema-typed@npm:7.0.3"
  checksum: e861b19e97e3cc2b29a429147890157827eeda16ab639a0765b935cf3e22aeb6abbba108e23aef442da806bb1f402bdff21da9c5cb30015f8007594565e110b5
  languageName: node
  linkType: hard

"json-stable-stringify-without-jsonify@npm:^1.0.1":
  version: 1.0.1
  resolution: "json-stable-stringify-without-jsonify@npm:1.0.1"
  checksum: cff44156ddce9c67c44386ad5cddf91925fe06b1d217f2da9c4910d01f358c6e3989c4d5a02683c7a5667f9727ff05831f7aa8ae66c8ff691c556f0884d49215
  languageName: node
  linkType: hard

"json5@npm:^1.0.2":
  version: 1.0.2
  resolution: "json5@npm:1.0.2"
  dependencies:
    minimist: ^1.2.0
  bin:
    json5: lib/cli.js
  checksum: 866458a8c58a95a49bef3adba929c625e82532bcff1fe93f01d29cb02cac7c3fe1f4b79951b7792c2da9de0b32871a8401a6e3c5b36778ad852bf5b8a61165d7
  languageName: node
  linkType: hard

"json5@npm:^2.2.3":
  version: 2.2.3
  resolution: "json5@npm:2.2.3"
  bin:
    json5: lib/cli.js
  checksum: 2a7436a93393830bce797d4626275152e37e877b265e94ca69c99e3d20c2b9dab021279146a39cdb700e71b2dd32a4cebd1514cd57cee102b1af906ce5040349
  languageName: node
  linkType: hard

"keygrip@npm:~1.1.0":
  version: 1.1.0
  resolution: "keygrip@npm:1.1.0"
  dependencies:
    tsscmp: 1.0.6
  checksum: 078cd16a463d187121f0a27c1c9c95c52ad392b620f823431689f345a0501132cee60f6e96914b07d570105af470b96960402accd6c48a0b1f3cd8fac4fa2cae
  languageName: node
  linkType: hard

"keyv@npm:^3.0.0":
  version: 3.1.0
  resolution: "keyv@npm:3.1.0"
  dependencies:
    json-buffer: 3.0.0
  checksum: bb7e8f3acffdbafbc2dd5b63f377fe6ec4c0e2c44fc82720449ef8ab54f4a7ce3802671ed94c0f475ae0a8549703353a2124561fcf3317010c141b32ca1ce903
  languageName: node
  linkType: hard

"keyv@npm:^4.5.3":
  version: 4.5.4
  resolution: "keyv@npm:4.5.4"
  dependencies:
    json-buffer: 3.0.1
  checksum: 74a24395b1c34bd44ad5cb2b49140d087553e170625240b86755a6604cd65aa16efdbdeae5cdb17ba1284a0fbb25ad06263755dbc71b8d8b06f74232ce3cdd72
  languageName: node
  linkType: hard

"koa-compose@npm:^4.1.0":
  version: 4.1.0
  resolution: "koa-compose@npm:4.1.0"
  checksum: 46cb16792d96425e977c2ae4e5cb04930280740e907242ec9c25e3fb8b4a1d7b54451d7432bc24f40ec62255edea71894d2ceeb8238501842b4e48014f2e83db
  languageName: node
  linkType: hard

"koa-convert@npm:^2.0.0":
  version: 2.0.0
  resolution: "koa-convert@npm:2.0.0"
  dependencies:
    co: ^4.6.0
    koa-compose: ^4.1.0
  checksum: 7385b3391995f59c1312142e110d5dff677f9850dbfbcf387cd36a7b0af03b5d26e82b811eb9bb008b4f3e661cdab1f8817596e46b1929da2cf6e97a2f7456ed
  languageName: node
  linkType: hard

"koa-send@npm:^5.0.0":
  version: 5.0.1
  resolution: "koa-send@npm:5.0.1"
  dependencies:
    debug: ^4.1.1
    http-errors: ^1.7.3
    resolve-path: ^1.4.0
  checksum: a9fbaadbe0f50efd157a733df4a1cc2b3b79b0cdf12e67c718641e6038d1792c0bebe40913e6d4ceb707d970301155be3859b98d1ef08b0fd1766f7326b82853
  languageName: node
  linkType: hard

"koa-static@npm:^5.0.0":
  version: 5.0.0
  resolution: "koa-static@npm:5.0.0"
  dependencies:
    debug: ^3.1.0
    koa-send: ^5.0.0
  checksum: 8d9b9c4d2b3b13e8818e804245d784099c4b353b55ddd7dbeeb90f27a2e9f5b6f86bd16a4909e337cb89db4d332d9002e6c0f5056caf75749cab62f93c1f0cc5
  languageName: node
  linkType: hard

"koa2-connect-history-api-fallback@npm:^0.1.3":
  version: 0.1.3
  resolution: "koa2-connect-history-api-fallback@npm:0.1.3"
  checksum: 2ed3bf83c2b300d1d1ff87ab1c5b417df85c4c920c8692bb617c46c5713f374ec96aa6984dd97505fcb9e04c7a67a150def75f5ffd5824c7fd3ea4ad3b5f076b
  languageName: node
  linkType: hard

"koa@npm:^2.15.0":
  version: 2.15.3
  resolution: "koa@npm:2.15.3"
  dependencies:
    accepts: ^1.3.5
    cache-content-type: ^1.0.0
    content-disposition: ~0.5.2
    content-type: ^1.0.4
    cookies: ~0.9.0
    debug: ^4.3.2
    delegates: ^1.0.0
    depd: ^2.0.0
    destroy: ^1.0.4
    encodeurl: ^1.0.2
    escape-html: ^1.0.3
    fresh: ~0.5.2
    http-assert: ^1.3.0
    http-errors: ^1.6.3
    is-generator-function: ^1.0.7
    koa-compose: ^4.1.0
    koa-convert: ^2.0.0
    on-finished: ^2.3.0
    only: ~0.0.2
    parseurl: ^1.3.2
    statuses: ^1.5.0
    type-is: ^1.6.16
    vary: ^1.1.2
  checksum: 7c3537443b1a588cf5c3e5554b914ff2bad510323d22b41861d5e0c97d47e9c5997965f303ede8be8bd83d309a4eea1f82cd45d35d6838bc21bb1bb6a90d5d25
  languageName: node
  linkType: hard

"kuler@npm:^2.0.0":
  version: 2.0.0
  resolution: "kuler@npm:2.0.0"
  checksum: 9e10b5a1659f9ed8761d38df3c35effabffbd19fc6107324095238e4ef0ff044392cae9ac64a1c2dda26e532426485342226b93806bd97504b174b0dcf04ed81
  languageName: node
  linkType: hard

"latest-version@npm:^5.1.0":
  version: 5.1.0
  resolution: "latest-version@npm:5.1.0"
  dependencies:
    package-json: ^6.3.0
  checksum: fbc72b071eb66c40f652441fd783a9cca62f08bf42433651937f078cd9ef94bf728ec7743992777826e4e89305aef24f234b515e6030503a2cbee7fc9bdc2c0f
  languageName: node
  linkType: hard

"levn@npm:^0.4.1":
  version: 0.4.1
  resolution: "levn@npm:0.4.1"
  dependencies:
    prelude-ls: ^1.2.1
    type-check: ~0.4.0
  checksum: 12c5021c859bd0f5248561bf139121f0358285ec545ebf48bb3d346820d5c61a4309535c7f387ed7d84361cf821e124ce346c6b7cef8ee09a67c1473b46d0fc4
  languageName: node
  linkType: hard

"lilconfig@npm:^3.0.0, lilconfig@npm:^3.1.3":
  version: 3.1.3
  resolution: "lilconfig@npm:3.1.3"
  checksum: 644eb10830350f9cdc88610f71a921f510574ed02424b57b0b3abb66ea725d7a082559552524a842f4e0272c196b88dfe1ff7d35ffcc6f45736777185cd67c9a
  languageName: node
  linkType: hard

"lines-and-columns@npm:^1.1.6":
  version: 1.2.4
  resolution: "lines-and-columns@npm:1.2.4"
  checksum: 0c37f9f7fa212b38912b7145e1cd16a5f3cd34d782441c3e6ca653485d326f58b3caccda66efce1c5812bde4961bbde3374fae4b0d11bf1226152337f3894aa5
  languageName: node
  linkType: hard

"local-pkg@npm:^0.4.3":
  version: 0.4.3
  resolution: "local-pkg@npm:0.4.3"
  checksum: 7825aca531dd6afa3a3712a0208697aa4a5cd009065f32e3fb732aafcc42ed11f277b5ac67229222e96f4def55197171cdf3d5522d0381b489d2e5547b407d55
  languageName: node
  linkType: hard

"local-pkg@npm:^0.5.0, local-pkg@npm:^0.5.1":
  version: 0.5.1
  resolution: "local-pkg@npm:0.5.1"
  dependencies:
    mlly: ^1.7.3
    pkg-types: ^1.2.1
  checksum: 478effb440780d412bff78ed80d1593d707a504931a7e5899d6570d207da1e661a6128c3087286ff964696a55c607c2bbd2bbe98377401c7d395891c160fa6e1
  languageName: node
  linkType: hard

"locate-path@npm:^3.0.0":
  version: 3.0.0
  resolution: "locate-path@npm:3.0.0"
  dependencies:
    p-locate: ^3.0.0
    path-exists: ^3.0.0
  checksum: 53db3996672f21f8b0bf2a2c645ae2c13ffdae1eeecfcd399a583bce8516c0b88dcb4222ca6efbbbeb6949df7e46860895be2c02e8d3219abd373ace3bfb4e11
  languageName: node
  linkType: hard

"locate-path@npm:^6.0.0":
  version: 6.0.0
  resolution: "locate-path@npm:6.0.0"
  dependencies:
    p-locate: ^5.0.0
  checksum: 72eb661788a0368c099a184c59d2fee760b3831c9c1c33955e8a19ae4a21b4116e53fa736dc086cdeb9fce9f7cc508f2f92d2d3aae516f133e16a2bb59a39f5a
  languageName: node
  linkType: hard

"lodash.clonedeep@npm:^4.5.0":
  version: 4.5.0
  resolution: "lodash.clonedeep@npm:4.5.0"
  checksum: 92c46f094b064e876a23c97f57f81fbffd5d760bf2d8a1c61d85db6d1e488c66b0384c943abee4f6af7debf5ad4e4282e74ff83177c9e63d8ff081a4837c3489
  languageName: node
  linkType: hard

"lodash.merge@npm:^4.6.2":
  version: 4.6.2
  resolution: "lodash.merge@npm:4.6.2"
  checksum: ad580b4bdbb7ca1f7abf7e1bce63a9a0b98e370cf40194b03380a46b4ed799c9573029599caebc1b14e3f24b111aef72b96674a56cfa105e0f5ac70546cdc005
  languageName: node
  linkType: hard

"lodash@npm:^4.17.21":
  version: 4.17.21
  resolution: "lodash@npm:4.17.21"
  checksum: eb835a2e51d381e561e508ce932ea50a8e5a68f4ebdd771ea240d3048244a8d13658acbd502cd4829768c56f2e16bdd4340b9ea141297d472517b83868e677f7
  languageName: node
  linkType: hard

"log-symbols@npm:^4.1.0":
  version: 4.1.0
  resolution: "log-symbols@npm:4.1.0"
  dependencies:
    chalk: ^4.1.0
    is-unicode-supported: ^0.1.0
  checksum: fce1497b3135a0198803f9f07464165e9eb83ed02ceb2273930a6f8a508951178d8cf4f0378e9d28300a2ed2bc49050995d2bd5f53ab716bb15ac84d58c6ef74
  languageName: node
  linkType: hard

"logform@npm:^2.7.0":
  version: 2.7.0
  resolution: "logform@npm:2.7.0"
  dependencies:
    "@colors/colors": 1.6.0
    "@types/triple-beam": ^1.3.2
    fecha: ^4.2.0
    ms: ^2.1.1
    safe-stable-stringify: ^2.3.1
    triple-beam: ^1.3.0
  checksum: a202d10897254735ead75a640f889998f9b91a0c36be9cac3f5471fa740d36bc2fbbcf9d113dcdadec4ddf09e257393ff800e6aab80019bdc7456363d6ea21f6
  languageName: node
  linkType: hard

"lowercase-keys@npm:^1.0.0, lowercase-keys@npm:^1.0.1":
  version: 1.0.1
  resolution: "lowercase-keys@npm:1.0.1"
  checksum: 4d045026595936e09953e3867722e309415ff2c80d7701d067546d75ef698dac218a4f53c6d1d0e7368b47e45fd7529df47e6cb56fbb90523ba599f898b3d147
  languageName: node
  linkType: hard

"lowercase-keys@npm:^2.0.0":
  version: 2.0.0
  resolution: "lowercase-keys@npm:2.0.0"
  checksum: 24d7ebd56ccdf15ff529ca9e08863f3c54b0b9d1edb97a3ae1af34940ae666c01a1e6d200707bce730a8ef76cb57cc10e65f245ecaaf7e6bc8639f2fb460ac23
  languageName: node
  linkType: hard

"lru-cache@npm:^10.0.1, lru-cache@npm:^10.2.0, lru-cache@npm:^10.2.2":
  version: 10.4.3
  resolution: "lru-cache@npm:10.4.3"
  checksum: 6476138d2125387a6d20f100608c2583d415a4f64a0fecf30c9e2dda976614f09cad4baa0842447bd37dd459a7bd27f57d9d8f8ce558805abd487c583f3d774a
  languageName: node
  linkType: hard

"lru-cache@npm:^5.1.1":
  version: 5.1.1
  resolution: "lru-cache@npm:5.1.1"
  dependencies:
    yallist: ^3.0.2
  checksum: c154ae1cbb0c2206d1501a0e94df349653c92c8cbb25236d7e85190bcaf4567a03ac6eb43166fabfa36fd35623694da7233e88d9601fbf411a9a481d85dbd2cb
  languageName: node
  linkType: hard

"magic-string@npm:^0.30.10, magic-string@npm:^0.30.11, magic-string@npm:^0.30.14, magic-string@npm:^0.30.3":
  version: 0.30.15
  resolution: "magic-string@npm:0.30.15"
  dependencies:
    "@jridgewell/sourcemap-codec": ^1.5.0
  checksum: 9da8c45a9d545c74a2f138b3f61240d921b167e9b24deb1a3dc297900c94cf05bf5099e8b20b9642e11c046dfe5f02b5e61679d017cfb7ddb44ee74f1e7a343d
  languageName: node
  linkType: hard

"make-dir@npm:^3.0.0":
  version: 3.1.0
  resolution: "make-dir@npm:3.1.0"
  dependencies:
    semver: ^6.0.0
  checksum: 484200020ab5a1fdf12f393fe5f385fc8e4378824c940fba1729dcd198ae4ff24867bc7a5646331e50cead8abff5d9270c456314386e629acec6dff4b8016b78
  languageName: node
  linkType: hard

"make-error@npm:^1.1.1":
  version: 1.3.6
  resolution: "make-error@npm:1.3.6"
  checksum: b86e5e0e25f7f777b77fabd8e2cbf15737972869d852a22b7e73c17623928fccb826d8e46b9951501d3f20e51ad74ba8c59ed584f610526a48f8ccf88aaec402
  languageName: node
  linkType: hard

"make-fetch-happen@npm:^14.0.3":
  version: 14.0.3
  resolution: "make-fetch-happen@npm:14.0.3"
  dependencies:
    "@npmcli/agent": ^3.0.0
    cacache: ^19.0.1
    http-cache-semantics: ^4.1.1
    minipass: ^7.0.2
    minipass-fetch: ^4.0.0
    minipass-flush: ^1.0.5
    minipass-pipeline: ^1.2.4
    negotiator: ^1.0.0
    proc-log: ^5.0.0
    promise-retry: ^2.0.1
    ssri: ^12.0.0
  checksum: 6fb2fee6da3d98f1953b03d315826b5c5a4ea1f908481afc113782d8027e19f080c85ae998454de4e5f27a681d3ec58d57278f0868d4e0b736f51d396b661691
  languageName: node
  linkType: hard

"marked@npm:^14.1.2":
  version: 14.1.4
  resolution: "marked@npm:14.1.4"
  bin:
    marked: bin/marked.js
  checksum: 6f2f7dd0ba2b8ccfe612185d83521a1264d0707ed6abe2d4a36e437f775844e76db147840a21fb5bd33518b9470c58e0f281e3a2ea1659f0b1e6cdcd4b3a6d3a
  languageName: node
  linkType: hard

"math-intrinsics@npm:^1.0.0":
  version: 1.0.0
  resolution: "math-intrinsics@npm:1.0.0"
  checksum: ad9edf8b5bec32c78d25163a9343dbe960331c8b4815b099181de7be4681e5abff9642a4b2fbeb3e882d7616567ffc45a5bae59dc8fec417cf5c76d47b92b197
  languageName: node
  linkType: hard

"media-typer@npm:0.3.0":
  version: 0.3.0
  resolution: "media-typer@npm:0.3.0"
  checksum: af1b38516c28ec95d6b0826f6c8f276c58aec391f76be42aa07646b4e39d317723e869700933ca6995b056db4b09a78c92d5440dc23657e6764be5d28874bba1
  languageName: node
  linkType: hard

"merge-stream@npm:^2.0.0":
  version: 2.0.0
  resolution: "merge-stream@npm:2.0.0"
  checksum: 6fa4dcc8d86629705cea944a4b88ef4cb0e07656ebf223fa287443256414283dd25d91c1cd84c77987f2aec5927af1a9db6085757cb43d90eb170ebf4b47f4f4
  languageName: node
  linkType: hard

"merge2@npm:^1.3.0, merge2@npm:^1.4.1":
  version: 1.4.1
  resolution: "merge2@npm:1.4.1"
  checksum: 7268db63ed5169466540b6fb947aec313200bcf6d40c5ab722c22e242f651994619bcd85601602972d3c85bd2cc45a358a4c61937e9f11a061919a1da569b0c2
  languageName: node
  linkType: hard

"methods@npm:^1.1.2":
  version: 1.1.2
  resolution: "methods@npm:1.1.2"
  checksum: 0917ff4041fa8e2f2fda5425a955fe16ca411591fbd123c0d722fcf02b73971ed6f764d85f0a6f547ce49ee0221ce2c19a5fa692157931cecb422984f1dcd13a
  languageName: node
  linkType: hard

"micromatch@npm:^4.0.4, micromatch@npm:^4.0.8":
  version: 4.0.8
  resolution: "micromatch@npm:4.0.8"
  dependencies:
    braces: ^3.0.3
    picomatch: ^2.3.1
  checksum: 79920eb634e6f400b464a954fcfa589c4e7c7143209488e44baf627f9affc8b1e306f41f4f0deedde97e69cb725920879462d3e750ab3bd3c1aed675bb3a8966
  languageName: node
  linkType: hard

"mime-db@npm:1.52.0":
  version: 1.52.0
  resolution: "mime-db@npm:1.52.0"
  checksum: 0d99a03585f8b39d68182803b12ac601d9c01abfa28ec56204fa330bc9f3d1c5e14beb049bafadb3dbdf646dfb94b87e24d4ec7b31b7279ef906a8ea9b6a513f
  languageName: node
  linkType: hard

"mime-types@npm:^2.1.12, mime-types@npm:^2.1.18, mime-types@npm:~2.1.24, mime-types@npm:~2.1.34":
  version: 2.1.35
  resolution: "mime-types@npm:2.1.35"
  dependencies:
    mime-db: 1.52.0
  checksum: 89a5b7f1def9f3af5dad6496c5ed50191ae4331cc5389d7c521c8ad28d5fdad2d06fd81baf38fed813dc4e46bb55c8145bb0ff406330818c9cf712fb2e9b3836
  languageName: node
  linkType: hard

"mimic-fn@npm:^2.1.0":
  version: 2.1.0
  resolution: "mimic-fn@npm:2.1.0"
  checksum: d2421a3444848ce7f84bd49115ddacff29c15745db73f54041edc906c14b131a38d05298dae3081667627a59b2eb1ca4b436ff2e1b80f69679522410418b478a
  languageName: node
  linkType: hard

"mimic-fn@npm:^3.0.0":
  version: 3.1.0
  resolution: "mimic-fn@npm:3.1.0"
  checksum: f7b167f9115b8bbdf2c3ee55dce9149d14be9e54b237259c4bc1d8d0512ea60f25a1b323f814eb1fe8f5a541662804bcfcfff3202ca58df143edb986849d58db
  languageName: node
  linkType: hard

"mimic-response@npm:^1.0.0, mimic-response@npm:^1.0.1":
  version: 1.0.1
  resolution: "mimic-response@npm:1.0.1"
  checksum: 034c78753b0e622bc03c983663b1cdf66d03861050e0c8606563d149bc2b02d63f62ce4d32be4ab50d0553ae0ffe647fc34d1f5281184c6e1e8cf4d85e8d9823
  languageName: node
  linkType: hard

"minimatch@npm:^3.0.4, minimatch@npm:^3.0.5, minimatch@npm:^3.1.1, minimatch@npm:^3.1.2":
  version: 3.1.2
  resolution: "minimatch@npm:3.1.2"
  dependencies:
    brace-expansion: ^1.1.7
  checksum: c154e566406683e7bcb746e000b84d74465b3a832c45d59912b9b55cd50dee66e5c4b1e5566dba26154040e51672f9aa450a9aef0c97cfc7336b78b7afb9540a
  languageName: node
  linkType: hard

"minimatch@npm:^9.0.3, minimatch@npm:^9.0.4":
  version: 9.0.5
  resolution: "minimatch@npm:9.0.5"
  dependencies:
    brace-expansion: ^2.0.1
  checksum: 2c035575eda1e50623c731ec6c14f65a85296268f749b9337005210bb2b34e2705f8ef1a358b188f69892286ab99dc42c8fb98a57bde55c8d81b3023c19cea28
  languageName: node
  linkType: hard

"minimist@npm:^1.2.0, minimist@npm:^1.2.6":
  version: 1.2.8
  resolution: "minimist@npm:1.2.8"
  checksum: 75a6d645fb122dad29c06a7597bddea977258957ed88d7a6df59b5cd3fe4a527e253e9bbf2e783e4b73657f9098b96a5fe96ab8a113655d4109108577ecf85b0
  languageName: node
  linkType: hard

"minipass-collect@npm:^2.0.1":
  version: 2.0.1
  resolution: "minipass-collect@npm:2.0.1"
  dependencies:
    minipass: ^7.0.3
  checksum: b251bceea62090f67a6cced7a446a36f4cd61ee2d5cea9aee7fff79ba8030e416327a1c5aa2908dc22629d06214b46d88fdab8c51ac76bacbf5703851b5ad342
  languageName: node
  linkType: hard

"minipass-fetch@npm:^4.0.0":
  version: 4.0.0
  resolution: "minipass-fetch@npm:4.0.0"
  dependencies:
    encoding: ^0.1.13
    minipass: ^7.0.3
    minipass-sized: ^1.0.3
    minizlib: ^3.0.1
  dependenciesMeta:
    encoding:
      optional: true
  checksum: 7d59a31011ab9e4d1af6562dd4c4440e425b2baf4c5edbdd2e22fb25a88629e1cdceca39953ff209da504a46021df520f18fd9a519f36efae4750ff724ddadea
  languageName: node
  linkType: hard

"minipass-flush@npm:^1.0.5":
  version: 1.0.5
  resolution: "minipass-flush@npm:1.0.5"
  dependencies:
    minipass: ^3.0.0
  checksum: 56269a0b22bad756a08a94b1ffc36b7c9c5de0735a4dd1ab2b06c066d795cfd1f0ac44a0fcae13eece5589b908ecddc867f04c745c7009be0b566421ea0944cf
  languageName: node
  linkType: hard

"minipass-pipeline@npm:^1.2.4":
  version: 1.2.4
  resolution: "minipass-pipeline@npm:1.2.4"
  dependencies:
    minipass: ^3.0.0
  checksum: b14240dac0d29823c3d5911c286069e36d0b81173d7bdf07a7e4a91ecdef92cdff4baaf31ea3746f1c61e0957f652e641223970870e2353593f382112257971b
  languageName: node
  linkType: hard

"minipass-sized@npm:^1.0.3":
  version: 1.0.3
  resolution: "minipass-sized@npm:1.0.3"
  dependencies:
    minipass: ^3.0.0
  checksum: 79076749fcacf21b5d16dd596d32c3b6bf4d6e62abb43868fac21674078505c8b15eaca4e47ed844985a4514854f917d78f588fcd029693709417d8f98b2bd60
  languageName: node
  linkType: hard

"minipass@npm:^3.0.0":
  version: 3.3.6
  resolution: "minipass@npm:3.3.6"
  dependencies:
    yallist: ^4.0.0
  checksum: a30d083c8054cee83cdcdc97f97e4641a3f58ae743970457b1489ce38ee1167b3aaf7d815cd39ec7a99b9c40397fd4f686e83750e73e652b21cb516f6d845e48
  languageName: node
  linkType: hard

"minipass@npm:^5.0.0 || ^6.0.2 || ^7.0.0, minipass@npm:^7.0.2, minipass@npm:^7.0.3, minipass@npm:^7.0.4, minipass@npm:^7.1.2":
  version: 7.1.2
  resolution: "minipass@npm:7.1.2"
  checksum: 2bfd325b95c555f2b4d2814d49325691c7bee937d753814861b0b49d5edcda55cbbf22b6b6a60bb91eddac8668771f03c5ff647dcd9d0f798e9548b9cdc46ee3
  languageName: node
  linkType: hard

"minizlib@npm:^3.0.1":
  version: 3.0.1
  resolution: "minizlib@npm:3.0.1"
  dependencies:
    minipass: ^7.0.4
    rimraf: ^5.0.5
  checksum: da0a53899252380475240c587e52c824f8998d9720982ba5c4693c68e89230718884a209858c156c6e08d51aad35700a3589987e540593c36f6713fe30cd7338
  languageName: node
  linkType: hard

"mitt@npm:^3.0.1":
  version: 3.0.1
  resolution: "mitt@npm:3.0.1"
  checksum: b55a489ac9c2949ab166b7f060601d3b6d893a852515ae9eca4e11df01c013876df777ea109317622b5c1c60e8aae252558e33c8c94e14124db38f64a39614b1
  languageName: node
  linkType: hard

"mkdirp@npm:^3.0.1":
  version: 3.0.1
  resolution: "mkdirp@npm:3.0.1"
  bin:
    mkdirp: dist/cjs/src/bin.js
  checksum: 972deb188e8fb55547f1e58d66bd6b4a3623bf0c7137802582602d73e6480c1c2268dcbafbfb1be466e00cc7e56ac514d7fd9334b7cf33e3e2ab547c16f83a8d
  languageName: node
  linkType: hard

"mlly@npm:^1.7.2, mlly@npm:^1.7.3":
  version: 1.7.3
  resolution: "mlly@npm:1.7.3"
  dependencies:
    acorn: ^8.14.0
    pathe: ^1.1.2
    pkg-types: ^1.2.1
    ufo: ^1.5.4
  checksum: 60d309c7ce2ac162224a087fcd683a891260511f57011b2f436b54dfef146b8aae7473013958a58d5b6039f2a8692c32a2599c8390c5b307d1119ad0d917b414
  languageName: node
  linkType: hard

"ms@npm:^2.0.0, ms@npm:^2.1.1, ms@npm:^2.1.3":
  version: 2.1.3
  resolution: "ms@npm:2.1.3"
  checksum: aa92de608021b242401676e35cfa5aa42dd70cbdc082b916da7fb925c542173e36bce97ea3e804923fe92c0ad991434e4a38327e15a1b5b5f945d66df615ae6d
  languageName: node
  linkType: hard

"muggle-string@npm:^0.4.1":
  version: 0.4.1
  resolution: "muggle-string@npm:0.4.1"
  checksum: 85fe1766d18d43cf22b6da7d047203a65b2e2b1ccfac505b699c2a459644f95ebb3c854a96db5be559eea0e213f6ee32b986b8c2f73c48e6c89e1fd829616532
  languageName: node
  linkType: hard

"mute-stream@npm:^1.0.0":
  version: 1.0.0
  resolution: "mute-stream@npm:1.0.0"
  checksum: 36fc968b0e9c9c63029d4f9dc63911950a3bdf55c9a87f58d3a266289b67180201cade911e7699f8b2fa596b34c9db43dad37649e3f7fdd13c3bb9edb0017ee7
  languageName: node
  linkType: hard

"mz@npm:^2.7.0":
  version: 2.7.0
  resolution: "mz@npm:2.7.0"
  dependencies:
    any-promise: ^1.0.0
    object-assign: ^4.0.1
    thenify-all: ^1.0.0
  checksum: 8427de0ece99a07e9faed3c0c6778820d7543e3776f9a84d22cf0ec0a8eb65f6e9aee9c9d353ff9a105ff62d33a9463c6ca638974cc652ee8140cd1e35951c87
  languageName: node
  linkType: hard

"nanoid@npm:^3.3.7":
  version: 3.3.8
  resolution: "nanoid@npm:3.3.8"
  bin:
    nanoid: bin/nanoid.cjs
  checksum: dfe0adbc0c77e9655b550c333075f51bb28cfc7568afbf3237249904f9c86c9aaaed1f113f0fddddba75673ee31c758c30c43d4414f014a52a7a626efc5958c9
  languageName: node
  linkType: hard

"natural-compare@npm:^1.4.0":
  version: 1.4.0
  resolution: "natural-compare@npm:1.4.0"
  checksum: 23ad088b08f898fc9b53011d7bb78ec48e79de7627e01ab5518e806033861bef68d5b0cd0e2205c2f36690ac9571ff6bcb05eb777ced2eeda8d4ac5b44592c3d
  languageName: node
  linkType: hard

"negotiator@npm:0.6.3":
  version: 0.6.3
  resolution: "negotiator@npm:0.6.3"
  checksum: b8ffeb1e262eff7968fc90a2b6767b04cfd9842582a9d0ece0af7049537266e7b2506dfb1d107a32f06dd849ab2aea834d5830f7f4d0e5cb7d36e1ae55d021d9
  languageName: node
  linkType: hard

"negotiator@npm:^1.0.0":
  version: 1.0.0
  resolution: "negotiator@npm:1.0.0"
  checksum: 20ebfe79b2d2e7cf9cbc8239a72662b584f71164096e6e8896c8325055497c96f6b80cd22c258e8a2f2aa382a787795ec3ee8b37b422a302c7d4381b0d5ecfbb
  languageName: node
  linkType: hard

"nice-try@npm:^1.0.4":
  version: 1.0.5
  resolution: "nice-try@npm:1.0.5"
  checksum: 0b4af3b5bb5d86c289f7a026303d192a7eb4417231fe47245c460baeabae7277bcd8fd9c728fb6bd62c30b3e15cd6620373e2cf33353b095d8b403d3e8a15aff
  languageName: node
  linkType: hard

"node-domexception@npm:1.0.0":
  version: 1.0.0
  resolution: "node-domexception@npm:1.0.0"
  checksum: ee1d37dd2a4eb26a8a92cd6b64dfc29caec72bff5e1ed9aba80c294f57a31ba4895a60fd48347cf17dd6e766da0ae87d75657dfd1f384ebfa60462c2283f5c7f
  languageName: node
  linkType: hard

"node-fetch@npm:^2.2.0, node-fetch@npm:^2.6.7":
  version: 2.7.0
  resolution: "node-fetch@npm:2.7.0"
  dependencies:
    whatwg-url: ^5.0.0
  peerDependencies:
    encoding: ^0.1.0
  peerDependenciesMeta:
    encoding:
      optional: true
  checksum: d76d2f5edb451a3f05b15115ec89fc6be39de37c6089f1b6368df03b91e1633fd379a7e01b7ab05089a25034b2023d959b47e59759cb38d88341b2459e89d6e5
  languageName: node
  linkType: hard

"node-gyp-build@npm:^4.3.0":
  version: 4.8.4
  resolution: "node-gyp-build@npm:4.8.4"
  bin:
    node-gyp-build: bin.js
    node-gyp-build-optional: optional.js
    node-gyp-build-test: build-test.js
  checksum: 8b81ca8ffd5fa257ad8d067896d07908a36918bc84fb04647af09d92f58310def2d2b8614d8606d129d9cd9b48890a5d2bec18abe7fcff54818f72bedd3a7d74
  languageName: node
  linkType: hard

"node-gyp@npm:latest":
  version: 11.0.0
  resolution: "node-gyp@npm:11.0.0"
  dependencies:
    env-paths: ^2.2.0
    exponential-backoff: ^3.1.1
    glob: ^10.3.10
    graceful-fs: ^4.2.6
    make-fetch-happen: ^14.0.3
    nopt: ^8.0.0
    proc-log: ^5.0.0
    semver: ^7.3.5
    tar: ^7.4.3
    which: ^5.0.0
  bin:
    node-gyp: bin/node-gyp.js
  checksum: d7d5055ccc88177f721c7cd4f8f9440c29a0eb40e7b79dba89ef882ec957975dfc1dcb8225e79ab32481a02016eb13bbc051a913ea88d482d3cbdf2131156af4
  languageName: node
  linkType: hard

"node-releases@npm:^2.0.19":
  version: 2.0.19
  resolution: "node-releases@npm:2.0.19"
  checksum: 917dbced519f48c6289a44830a0ca6dc944c3ee9243c468ebd8515a41c97c8b2c256edb7f3f750416bc37952cc9608684e6483c7b6c6f39f6bd8d86c52cfe658
  languageName: node
  linkType: hard

"nodemon@npm:^3.1.0":
  version: 3.1.9
  resolution: "nodemon@npm:3.1.9"
  dependencies:
    chokidar: ^3.5.2
    debug: ^4
    ignore-by-default: ^1.0.1
    minimatch: ^3.1.2
    pstree.remy: ^1.1.8
    semver: ^7.5.3
    simple-update-notifier: ^2.0.0
    supports-color: ^5.5.0
    touch: ^3.1.0
    undefsafe: ^2.0.5
  bin:
    nodemon: bin/nodemon.js
  checksum: d045065dea08904f1356d18132538e71a61df12cb4e2852730310492943676d4789bedb28c343a5d85d5e07558bf47b73f000a8017409f0b7d522a3c1c42b2e5
  languageName: node
  linkType: hard

"nopt@npm:^8.0.0":
  version: 8.0.0
  resolution: "nopt@npm:8.0.0"
  dependencies:
    abbrev: ^2.0.0
  bin:
    nopt: bin/nopt.js
  checksum: 2cfc65e7ee38af2e04aea98f054753b0230011c0eeca4ecf131bd7d25984cbbf6f214586e0ae5dfcc2e830bc0bffa5a7fb28ea8d0b306ffd4ae8ea2d814c1ab3
  languageName: node
  linkType: hard

"normalize-path@npm:^3.0.0, normalize-path@npm:~3.0.0":
  version: 3.0.0
  resolution: "normalize-path@npm:3.0.0"
  checksum: 88eeb4da891e10b1318c4b2476b6e2ecbeb5ff97d946815ffea7794c31a89017c70d7f34b3c2ebf23ef4e9fc9fb99f7dffe36da22011b5b5c6ffa34f4873ec20
  languageName: node
  linkType: hard

"normalize-range@npm:^0.1.2":
  version: 0.1.2
  resolution: "normalize-range@npm:0.1.2"
  checksum: 9b2f14f093593f367a7a0834267c24f3cb3e887a2d9809c77d8a7e5fd08738bcd15af46f0ab01cc3a3d660386f015816b5c922cea8bf2ee79777f40874063184
  languageName: node
  linkType: hard

"normalize-url@npm:^4.1.0":
  version: 4.5.1
  resolution: "normalize-url@npm:4.5.1"
  checksum: 9a9dee01df02ad23e171171893e56e22d752f7cff86fb96aafeae074819b572ea655b60f8302e2d85dbb834dc885c972cc1c573892fea24df46b2765065dd05a
  languageName: node
  linkType: hard

"npm-run-path@npm:^2.0.0":
  version: 2.0.2
  resolution: "npm-run-path@npm:2.0.2"
  dependencies:
    path-key: ^2.0.0
  checksum: acd5ad81648ba4588ba5a8effb1d98d2b339d31be16826a118d50f182a134ac523172101b82eab1d01cb4c2ba358e857d54cfafd8163a1ffe7bd52100b741125
  languageName: node
  linkType: hard

"npm-run-path@npm:^4.0.1":
  version: 4.0.1
  resolution: "npm-run-path@npm:4.0.1"
  dependencies:
    path-key: ^3.0.0
  checksum: 5374c0cea4b0bbfdfae62da7bbdf1e1558d338335f4cacf2515c282ff358ff27b2ecb91ffa5330a8b14390ac66a1e146e10700440c1ab868208430f56b5f4d23
  languageName: node
  linkType: hard

"nth-check@npm:^2.0.1, nth-check@npm:^2.1.1":
  version: 2.1.1
  resolution: "nth-check@npm:2.1.1"
  dependencies:
    boolbase: ^1.0.0
  checksum: 5afc3dafcd1573b08877ca8e6148c52abd565f1d06b1eb08caf982e3fa289a82f2cae697ffb55b5021e146d60443f1590a5d6b944844e944714a5b549675bcd3
  languageName: node
  linkType: hard

"object-assign@npm:^4.0.1":
  version: 4.1.1
  resolution: "object-assign@npm:4.1.1"
  checksum: fcc6e4ea8c7fe48abfbb552578b1c53e0d194086e2e6bbbf59e0a536381a292f39943c6e9628af05b5528aa5e3318bb30d6b2e53cadaf5b8fe9e12c4b69af23f
  languageName: node
  linkType: hard

"object-hash@npm:^3.0.0":
  version: 3.0.0
  resolution: "object-hash@npm:3.0.0"
  checksum: 80b4904bb3857c52cc1bfd0b52c0352532ca12ed3b8a6ff06a90cd209dfda1b95cee059a7625eb9da29537027f68ac4619363491eedb2f5d3dddbba97494fd6c
  languageName: node
  linkType: hard

"object-inspect@npm:^1.13.3":
  version: 1.13.3
  resolution: "object-inspect@npm:1.13.3"
  checksum: 8c962102117241e18ea403b84d2521f78291b774b03a29ee80a9863621d88265ffd11d0d7e435c4c2cea0dc2a2fbf8bbc92255737a05536590f2df2e8756f297
  languageName: node
  linkType: hard

"object-keys@npm:^1.1.1":
  version: 1.1.1
  resolution: "object-keys@npm:1.1.1"
  checksum: b363c5e7644b1e1b04aa507e88dcb8e3a2f52b6ffd0ea801e4c7a62d5aa559affe21c55a07fd4b1fd55fc03a33c610d73426664b20032405d7b92a1414c34d6a
  languageName: node
  linkType: hard

"object-treeify@npm:1.1.33":
  version: 1.1.33
  resolution: "object-treeify@npm:1.1.33"
  checksum: 3af7f889349571ee73f5bdfb5ac478270c85eda8bcba950b454eb598ce41759a1ed6b0b43fbd624cb449080a4eb2df906b602e5138b6186b9563b692231f1694
  languageName: node
  linkType: hard

"object.assign@npm:^4.1.5":
  version: 4.1.5
  resolution: "object.assign@npm:4.1.5"
  dependencies:
    call-bind: ^1.0.5
    define-properties: ^1.2.1
    has-symbols: ^1.0.3
    object-keys: ^1.1.1
  checksum: f9aeac0541661370a1fc86e6a8065eb1668d3e771f7dbb33ee54578201336c057b21ee61207a186dd42db0c62201d91aac703d20d12a79fc79c353eed44d4e25
  languageName: node
  linkType: hard

"object.fromentries@npm:^2.0.8":
  version: 2.0.8
  resolution: "object.fromentries@npm:2.0.8"
  dependencies:
    call-bind: ^1.0.7
    define-properties: ^1.2.1
    es-abstract: ^1.23.2
    es-object-atoms: ^1.0.0
  checksum: 29b2207a2db2782d7ced83f93b3ff5d425f901945f3665ffda1821e30a7253cd1fd6b891a64279976098137ddfa883d748787a6fea53ecdb51f8df8b8cec0ae1
  languageName: node
  linkType: hard

"object.groupby@npm:^1.0.3":
  version: 1.0.3
  resolution: "object.groupby@npm:1.0.3"
  dependencies:
    call-bind: ^1.0.7
    define-properties: ^1.2.1
    es-abstract: ^1.23.2
  checksum: 0d30693ca3ace29720bffd20b3130451dca7a56c612e1926c0a1a15e4306061d84410bdb1456be2656c5aca53c81b7a3661eceaa362db1bba6669c2c9b6d1982
  languageName: node
  linkType: hard

"object.values@npm:^1.2.0":
  version: 1.2.0
  resolution: "object.values@npm:1.2.0"
  dependencies:
    call-bind: ^1.0.7
    define-properties: ^1.2.1
    es-object-atoms: ^1.0.0
  checksum: 51fef456c2a544275cb1766897f34ded968b22adfc13ba13b5e4815fdaf4304a90d42a3aee114b1f1ede048a4890381d47a5594d84296f2767c6a0364b9da8fa
  languageName: node
  linkType: hard

"ollama@npm:^0.5.9":
  version: 0.5.11
  resolution: "ollama@npm:0.5.11"
  dependencies:
    whatwg-fetch: ^3.6.20
  checksum: 9b55a85f9771e81527b66f169329ed5b3d92de2af495f26a00bf4e9d3f224614ffda6a047135baf4bc9960a122fb18656613b733e5e37db02195ebaf0fba79b4
  languageName: node
  linkType: hard

"on-finished@npm:^2.3.0":
  version: 2.4.1
  resolution: "on-finished@npm:2.4.1"
  dependencies:
    ee-first: 1.1.1
  checksum: d20929a25e7f0bb62f937a425b5edeb4e4cde0540d77ba146ec9357f00b0d497cdb3b9b05b9c8e46222407d1548d08166bff69cc56dfa55ba0e4469228920ff0
  languageName: node
  linkType: hard

"once@npm:^1.3.0, once@npm:^1.3.1, once@npm:^1.4.0":
  version: 1.4.0
  resolution: "once@npm:1.4.0"
  dependencies:
    wrappy: 1
  checksum: cd0a88501333edd640d95f0d2700fbde6bff20b3d4d9bdc521bdd31af0656b5706570d6c6afe532045a20bb8dc0849f8332d6f2a416e0ba6d3d3b98806c7db68
  languageName: node
  linkType: hard

"one-time@npm:^1.0.0":
  version: 1.0.0
  resolution: "one-time@npm:1.0.0"
  dependencies:
    fn.name: 1.x.x
  checksum: fd008d7e992bdec1c67f53a2f9b46381ee12a9b8c309f88b21f0223546003fb47e8ad7c1fd5843751920a8d276c63bd4b45670ef80c61fb3e07dbccc962b5c7d
  languageName: node
  linkType: hard

"onetime@npm:^5.1.0, onetime@npm:^5.1.2":
  version: 5.1.2
  resolution: "onetime@npm:5.1.2"
  dependencies:
    mimic-fn: ^2.1.0
  checksum: 2478859ef817fc5d4e9c2f9e5728512ddd1dbc9fb7829ad263765bb6d3b91ce699d6e2332eef6b7dff183c2f490bd3349f1666427eaba4469fba0ac38dfd0d34
  languageName: node
  linkType: hard

"only@npm:~0.0.2":
  version: 0.0.2
  resolution: "only@npm:0.0.2"
  checksum: d399710db867a1ef436dd3ce74499c87ece794aa81ab0370b5d153968766ee4aed2f98d3f92fc87c963e45b7a74d400d6f463ef651a5e7cfb861b15e88e9efe6
  languageName: node
  linkType: hard

"open@npm:^8.4.2":
  version: 8.4.2
  resolution: "open@npm:8.4.2"
  dependencies:
    define-lazy-prop: ^2.0.0
    is-docker: ^2.1.1
    is-wsl: ^2.2.0
  checksum: 6388bfff21b40cb9bd8f913f9130d107f2ed4724ea81a8fd29798ee322b361ca31fa2cdfb491a5c31e43a3996cfe9566741238c7a741ada8d7af1cb78d85cf26
  languageName: node
  linkType: hard

"openai@npm:^4.61.1":
  version: 4.76.3
  resolution: "openai@npm:4.76.3"
  dependencies:
    "@types/node": ^18.11.18
    "@types/node-fetch": ^2.6.4
    abort-controller: ^3.0.0
    agentkeepalive: ^4.2.1
    form-data-encoder: 1.7.2
    formdata-node: ^4.3.2
    node-fetch: ^2.6.7
  peerDependencies:
    zod: ^3.23.8
  peerDependenciesMeta:
    zod:
      optional: true
  bin:
    openai: bin/cli
  checksum: 432560b09777c31d436cc004bd28a9f1029cc19f8ca597a55c30963e9237e6ebba3dbdd665da75942598c1a9eac09f8b46f6037850beb83ab25262ee5fa48e58
  languageName: node
  linkType: hard

"optionator@npm:^0.9.3":
  version: 0.9.4
  resolution: "optionator@npm:0.9.4"
  dependencies:
    deep-is: ^0.1.3
    fast-levenshtein: ^2.0.6
    levn: ^0.4.1
    prelude-ls: ^1.2.1
    type-check: ^0.4.0
    word-wrap: ^1.2.5
  checksum: ecbd010e3dc73e05d239976422d9ef54a82a13f37c11ca5911dff41c98a6c7f0f163b27f922c37e7f8340af9d36febd3b6e9cef508f3339d4c393d7276d716bb
  languageName: node
  linkType: hard

"ora@npm:^5.4.1":
  version: 5.4.1
  resolution: "ora@npm:5.4.1"
  dependencies:
    bl: ^4.1.0
    chalk: ^4.1.0
    cli-cursor: ^3.1.0
    cli-spinners: ^2.5.0
    is-interactive: ^1.0.0
    is-unicode-supported: ^0.1.0
    log-symbols: ^4.1.0
    strip-ansi: ^6.0.0
    wcwidth: ^1.0.1
  checksum: 28d476ee6c1049d68368c0dc922e7225e3b5600c3ede88fade8052837f9ed342625fdaa84a6209302587c8ddd9b664f71f0759833cbdb3a4cf81344057e63c63
  languageName: node
  linkType: hard

"os-tmpdir@npm:~1.0.2":
  version: 1.0.2
  resolution: "os-tmpdir@npm:1.0.2"
  checksum: 5666560f7b9f10182548bf7013883265be33620b1c1b4a4d405c25be2636f970c5488ff3e6c48de75b55d02bde037249fe5dbfbb4c0fb7714953d56aed062e6d
  languageName: node
  linkType: hard

"p-cancelable@npm:^1.0.0":
  version: 1.1.0
  resolution: "p-cancelable@npm:1.1.0"
  checksum: 2db3814fef6d9025787f30afaee4496a8857a28be3c5706432cbad76c688a6db1874308f48e364a42f5317f5e41e8e7b4f2ff5c8ff2256dbb6264bc361704ece
  languageName: node
  linkType: hard

"p-finally@npm:^1.0.0":
  version: 1.0.0
  resolution: "p-finally@npm:1.0.0"
  checksum: 93a654c53dc805dd5b5891bab16eb0ea46db8f66c4bfd99336ae929323b1af2b70a8b0654f8f1eae924b2b73d037031366d645f1fd18b3d30cbd15950cc4b1d4
  languageName: node
  linkType: hard

"p-limit@npm:^2.0.0":
  version: 2.3.0
  resolution: "p-limit@npm:2.3.0"
  dependencies:
    p-try: ^2.0.0
  checksum: 84ff17f1a38126c3314e91ecfe56aecbf36430940e2873dadaa773ffe072dc23b7af8e46d4b6485d302a11673fe94c6b67ca2cfbb60c989848b02100d0594ac1
  languageName: node
  linkType: hard

"p-limit@npm:^3.0.2":
  version: 3.1.0
  resolution: "p-limit@npm:3.1.0"
  dependencies:
    yocto-queue: ^0.1.0
  checksum: 7c3690c4dbf62ef625671e20b7bdf1cbc9534e83352a2780f165b0d3ceba21907e77ad63401708145ca4e25bfc51636588d89a8c0aeb715e6c37d1c066430360
  languageName: node
  linkType: hard

"p-locate@npm:^3.0.0":
  version: 3.0.0
  resolution: "p-locate@npm:3.0.0"
  dependencies:
    p-limit: ^2.0.0
  checksum: 83991734a9854a05fe9dbb29f707ea8a0599391f52daac32b86f08e21415e857ffa60f0e120bfe7ce0cc4faf9274a50239c7895fc0d0579d08411e513b83a4ae
  languageName: node
  linkType: hard

"p-locate@npm:^5.0.0":
  version: 5.0.0
  resolution: "p-locate@npm:5.0.0"
  dependencies:
    p-limit: ^3.0.2
  checksum: 1623088f36cf1cbca58e9b61c4e62bf0c60a07af5ae1ca99a720837356b5b6c5ba3eb1b2127e47a06865fee59dd0453cad7cc844cda9d5a62ac1a5a51b7c86d3
  languageName: node
  linkType: hard

"p-map@npm:^7.0.2":
  version: 7.0.3
  resolution: "p-map@npm:7.0.3"
  checksum: 8c92d533acf82f0d12f7e196edccff773f384098bbb048acdd55a08778ce4fc8889d8f1bde72969487bd96f9c63212698d79744c20bedfce36c5b00b46d369f8
  languageName: node
  linkType: hard

"p-try@npm:^2.0.0":
  version: 2.2.0
  resolution: "p-try@npm:2.2.0"
  checksum: f8a8e9a7693659383f06aec604ad5ead237c7a261c18048a6e1b5b85a5f8a067e469aa24f5bc009b991ea3b058a87f5065ef4176793a200d4917349881216cae
  languageName: node
  linkType: hard

"package-json-from-dist@npm:^1.0.0":
  version: 1.0.1
  resolution: "package-json-from-dist@npm:1.0.1"
  checksum: 58ee9538f2f762988433da00e26acc788036914d57c71c246bf0be1b60cdbd77dd60b6a3e1a30465f0b248aeb80079e0b34cb6050b1dfa18c06953bb1cbc7602
  languageName: node
  linkType: hard

"package-json@npm:^6.3.0":
  version: 6.5.0
  resolution: "package-json@npm:6.5.0"
  dependencies:
    got: ^9.6.0
    registry-auth-token: ^4.0.0
    registry-url: ^5.0.0
    semver: ^6.2.0
  checksum: cc9f890d3667d7610e6184decf543278b87f657d1ace0deb4a9c9155feca738ef88f660c82200763d3348010f4e42e9c7adc91e96ab0f86a770955995b5351e2
  languageName: node
  linkType: hard

"parent-module@npm:^1.0.0":
  version: 1.0.1
  resolution: "parent-module@npm:1.0.1"
  dependencies:
    callsites: ^3.0.0
  checksum: 6ba8b255145cae9470cf5551eb74be2d22281587af787a2626683a6c20fbb464978784661478dd2a3f1dad74d1e802d403e1b03c1a31fab310259eec8ac560ff
  languageName: node
  linkType: hard

"parse5-htmlparser2-tree-adapter@npm:^7.0.0":
  version: 7.1.0
  resolution: "parse5-htmlparser2-tree-adapter@npm:7.1.0"
  dependencies:
    domhandler: ^5.0.3
    parse5: ^7.0.0
  checksum: 98326fc5443e2149e10695adbfd0b0b3383c54398799f858b4ac2914adb199af8fcc90c2143aa5f7fd5f9482338f26ef253b468722f34d50bb215ec075d89fe9
  languageName: node
  linkType: hard

"parse5-parser-stream@npm:^7.1.2":
  version: 7.1.2
  resolution: "parse5-parser-stream@npm:7.1.2"
  dependencies:
    parse5: ^7.0.0
  checksum: 75b232d460bce6bd0e35012750a78ef034f40ccf550b7c6cec3122395af6b4553202ad3663ad468cf537ead5a2e13b6727670395fd0ff548faccad1dc2dc93cf
  languageName: node
  linkType: hard

"parse5@npm:^7.0.0, parse5@npm:^7.1.2":
  version: 7.2.1
  resolution: "parse5@npm:7.2.1"
  dependencies:
    entities: ^4.5.0
  checksum: 11253cf8aa2e7fc41c004c64cba6f2c255f809663365db65bd7ad0e8cf7b89e436a563c20059346371cc543a6c1b567032088883ca6a2cbc88276c666b68236d
  languageName: node
  linkType: hard

"parseurl@npm:^1.3.2":
  version: 1.3.3
  resolution: "parseurl@npm:1.3.3"
  checksum: 407cee8e0a3a4c5cd472559bca8b6a45b82c124e9a4703302326e9ab60fc1081442ada4e02628efef1eb16197ddc7f8822f5a91fd7d7c86b51f530aedb17dfa2
  languageName: node
  linkType: hard

"path-browserify@npm:^1.0.1":
  version: 1.0.1
  resolution: "path-browserify@npm:1.0.1"
  checksum: c6d7fa376423fe35b95b2d67990060c3ee304fc815ff0a2dc1c6c3cfaff2bd0d572ee67e18f19d0ea3bbe32e8add2a05021132ac40509416459fffee35200699
  languageName: node
  linkType: hard

"path-exists@npm:^3.0.0":
  version: 3.0.0
  resolution: "path-exists@npm:3.0.0"
  checksum: 96e92643aa34b4b28d0de1cd2eba52a1c5313a90c6542d03f62750d82480e20bfa62bc865d5cfc6165f5fcd5aeb0851043c40a39be5989646f223300021bae0a
  languageName: node
  linkType: hard

"path-exists@npm:^4.0.0":
  version: 4.0.0
  resolution: "path-exists@npm:4.0.0"
  checksum: 505807199dfb7c50737b057dd8d351b82c033029ab94cb10a657609e00c1bc53b951cfdbccab8de04c5584d5eff31128ce6afd3db79281874a5ef2adbba55ed1
  languageName: node
  linkType: hard

"path-is-absolute@npm:1.0.1, path-is-absolute@npm:^1.0.0":
  version: 1.0.1
  resolution: "path-is-absolute@npm:1.0.1"
  checksum: 060840f92cf8effa293bcc1bea81281bd7d363731d214cbe5c227df207c34cd727430f70c6037b5159c8a870b9157cba65e775446b0ab06fd5ecc7e54615a3b8
  languageName: node
  linkType: hard

"path-key@npm:^2.0.0, path-key@npm:^2.0.1":
  version: 2.0.1
  resolution: "path-key@npm:2.0.1"
  checksum: f7ab0ad42fe3fb8c7f11d0c4f849871e28fbd8e1add65c370e422512fc5887097b9cf34d09c1747d45c942a8c1e26468d6356e2df3f740bf177ab8ca7301ebfd
  languageName: node
  linkType: hard

"path-key@npm:^3.0.0, path-key@npm:^3.1.0":
  version: 3.1.1
  resolution: "path-key@npm:3.1.1"
  checksum: 55cd7a9dd4b343412a8386a743f9c746ef196e57c823d90ca3ab917f90ab9f13dd0ded27252ba49dbdfcab2b091d998bc446f6220cd3cea65db407502a740020
  languageName: node
  linkType: hard

"path-parse@npm:^1.0.7":
  version: 1.0.7
  resolution: "path-parse@npm:1.0.7"
  checksum: 49abf3d81115642938a8700ec580da6e830dde670be21893c62f4e10bd7dd4c3742ddc603fe24f898cba7eb0c6bc1777f8d9ac14185d34540c6d4d80cd9cae8a
  languageName: node
  linkType: hard

"path-scurry@npm:^1.11.1":
  version: 1.11.1
  resolution: "path-scurry@npm:1.11.1"
  dependencies:
    lru-cache: ^10.2.0
    minipass: ^5.0.0 || ^6.0.2 || ^7.0.0
  checksum: 890d5abcd593a7912dcce7cf7c6bf7a0b5648e3dee6caf0712c126ca0a65c7f3d7b9d769072a4d1baf370f61ce493ab5b038d59988688e0c5f3f646ee3c69023
  languageName: node
  linkType: hard

"path-to-regexp@npm:^6.3.0":
  version: 6.3.0
  resolution: "path-to-regexp@npm:6.3.0"
  checksum: eca78602e6434a1b6799d511d375ec044e8d7e28f5a48aa5c28d57d8152fb52f3fc62fb1cfc5dfa2198e1f041c2a82ed14043d75740a2fe60e91b5089a153250
  languageName: node
  linkType: hard

"path-type@npm:^4.0.0":
  version: 4.0.0
  resolution: "path-type@npm:4.0.0"
  checksum: 5b1e2daa247062061325b8fdbfd1fb56dde0a448fb1455453276ea18c60685bdad23a445dc148cf87bc216be1573357509b7d4060494a6fd768c7efad833ee45
  languageName: node
  linkType: hard

"pathe@npm:^1.1.2":
  version: 1.1.2
  resolution: "pathe@npm:1.1.2"
  checksum: ec5f778d9790e7b9ffc3e4c1df39a5bb1ce94657a4e3ad830c1276491ca9d79f189f47609884671db173400256b005f4955f7952f52a2aeb5834ad5fb4faf134
  languageName: node
  linkType: hard

"picocolors@npm:^1.0.0, picocolors@npm:^1.0.1, picocolors@npm:^1.1.0, picocolors@npm:^1.1.1":
  version: 1.1.1
  resolution: "picocolors@npm:1.1.1"
  checksum: e1cf46bf84886c79055fdfa9dcb3e4711ad259949e3565154b004b260cd356c5d54b31a1437ce9782624bf766272fe6b0154f5f0c744fb7af5d454d2b60db045
  languageName: node
  linkType: hard

"picomatch@npm:^2.0.4, picomatch@npm:^2.2.1, picomatch@npm:^2.3.1":
  version: 2.3.1
  resolution: "picomatch@npm:2.3.1"
  checksum: 050c865ce81119c4822c45d3c84f1ced46f93a0126febae20737bd05ca20589c564d6e9226977df859ed5e03dc73f02584a2b0faad36e896936238238b0446cf
  languageName: node
  linkType: hard

"picomatch@npm:^4.0.2":
  version: 4.0.2
  resolution: "picomatch@npm:4.0.2"
  checksum: a7a5188c954f82c6585720e9143297ccd0e35ad8072231608086ca950bee672d51b0ef676254af0788205e59bd4e4deb4e7708769226bed725bf13370a7d1464
  languageName: node
  linkType: hard

"pify@npm:^2.3.0":
  version: 2.3.0
  resolution: "pify@npm:2.3.0"
  checksum: 9503aaeaf4577acc58642ad1d25c45c6d90288596238fb68f82811c08104c800e5a7870398e9f015d82b44ecbcbef3dc3d4251a1cbb582f6e5959fe09884b2ba
  languageName: node
  linkType: hard

"pinia-plugin-persistedstate@npm:^3.2.1":
  version: 3.2.3
  resolution: "pinia-plugin-persistedstate@npm:3.2.3"
  peerDependencies:
    pinia: ^2.0.0
  checksum: a2601184ecd29c1d81f22e0d116840b6c406936921b5b86f49890c62ccdeb61249f63ab8dec00fbcaf17c60484a284179366a93f4bfa4e5985bdea16425bd269
  languageName: node
  linkType: hard

"pinia@npm:^2.2.2":
  version: 2.3.0
  resolution: "pinia@npm:2.3.0"
  dependencies:
    "@vue/devtools-api": ^6.6.3
    vue-demi: ^0.14.10
  peerDependencies:
    typescript: ">=4.4.4"
    vue: ^2.7.0 || ^3.5.11
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 556dd718563c535e21f35576750afc2abfde0ab60f40b1e31e6cf2ecceee17489549d0c6ffc445a168a84505e9117cfa8d8ae859fc47332db00f88d1db480f56
  languageName: node
  linkType: hard

"pirates@npm:^4.0.1":
  version: 4.0.6
  resolution: "pirates@npm:4.0.6"
  checksum: 46a65fefaf19c6f57460388a5af9ab81e3d7fd0e7bc44ca59d753cb5c4d0df97c6c6e583674869762101836d68675f027d60f841c105d72734df9dfca97cbcc6
  languageName: node
  linkType: hard

"pkg-types@npm:^1.2.1":
  version: 1.2.1
  resolution: "pkg-types@npm:1.2.1"
  dependencies:
    confbox: ^0.1.8
    mlly: ^1.7.2
    pathe: ^1.1.2
  checksum: d2e3ad7aef36cc92b17403e61c04db521bf0beb175ccb4d432c284239f00ec32ff37feb072a260613e9ff727911cff1127a083fd52f91b9bec6b62970f385702
  languageName: node
  linkType: hard

"pkg-up@npm:^3.1.0":
  version: 3.1.0
  resolution: "pkg-up@npm:3.1.0"
  dependencies:
    find-up: ^3.0.0
  checksum: 5bac346b7c7c903613c057ae3ab722f320716199d753f4a7d053d38f2b5955460f3e6ab73b4762c62fd3e947f58e04f1343e92089e7bb6091c90877406fcd8c8
  languageName: node
  linkType: hard

"possible-typed-array-names@npm:^1.0.0":
  version: 1.0.0
  resolution: "possible-typed-array-names@npm:1.0.0"
  checksum: b32d403ece71e042385cc7856385cecf1cd8e144fa74d2f1de40d1e16035dba097bc189715925e79b67bdd1472796ff168d3a90d296356c9c94d272d5b95f3ae
  languageName: node
  linkType: hard

"postcss-import@npm:^15.1.0":
  version: 15.1.0
  resolution: "postcss-import@npm:15.1.0"
  dependencies:
    postcss-value-parser: ^4.0.0
    read-cache: ^1.0.0
    resolve: ^1.1.7
  peerDependencies:
    postcss: ^8.0.0
  checksum: 7bd04bd8f0235429009d0022cbf00faebc885de1d017f6d12ccb1b021265882efc9302006ba700af6cab24c46bfa2f3bc590be3f9aee89d064944f171b04e2a3
  languageName: node
  linkType: hard

"postcss-js@npm:^4.0.1":
  version: 4.0.1
  resolution: "postcss-js@npm:4.0.1"
  dependencies:
    camelcase-css: ^2.0.1
  peerDependencies:
    postcss: ^8.4.21
  checksum: 5c1e83efeabeb5a42676193f4357aa9c88f4dc1b3c4a0332c132fe88932b33ea58848186db117cf473049fc233a980356f67db490bd0a7832ccba9d0b3fd3491
  languageName: node
  linkType: hard

"postcss-load-config@npm:^4.0.2":
  version: 4.0.2
  resolution: "postcss-load-config@npm:4.0.2"
  dependencies:
    lilconfig: ^3.0.0
    yaml: ^2.3.4
  peerDependencies:
    postcss: ">=8.0.9"
    ts-node: ">=9.0.0"
  peerDependenciesMeta:
    postcss:
      optional: true
    ts-node:
      optional: true
  checksum: 7c27dd3801db4eae207a5116fed2db6b1ebb780b40c3dd62a3e57e087093a8e6a14ee17ada729fee903152d6ef4826c6339eb135bee6208e0f3140d7e8090185
  languageName: node
  linkType: hard

"postcss-nested@npm:^6.2.0":
  version: 6.2.0
  resolution: "postcss-nested@npm:6.2.0"
  dependencies:
    postcss-selector-parser: ^6.1.1
  peerDependencies:
    postcss: ^8.2.14
  checksum: 2c86ecf2d0ce68f27c87c7e24ae22dc6dd5515a89fcaf372b2627906e11f5c1f36e4a09e4c15c20fd4a23d628b3d945c35839f44496fbee9a25866258006671b
  languageName: node
  linkType: hard

"postcss-selector-parser@npm:^6.0.15, postcss-selector-parser@npm:^6.1.1, postcss-selector-parser@npm:^6.1.2":
  version: 6.1.2
  resolution: "postcss-selector-parser@npm:6.1.2"
  dependencies:
    cssesc: ^3.0.0
    util-deprecate: ^1.0.2
  checksum: ce9440fc42a5419d103f4c7c1847cb75488f3ac9cbe81093b408ee9701193a509f664b4d10a2b4d82c694ee7495e022f8f482d254f92b7ffd9ed9dea696c6f84
  languageName: node
  linkType: hard

"postcss-value-parser@npm:^4.0.0, postcss-value-parser@npm:^4.2.0":
  version: 4.2.0
  resolution: "postcss-value-parser@npm:4.2.0"
  checksum: 819ffab0c9d51cf0acbabf8996dffbfafbafa57afc0e4c98db88b67f2094cb44488758f06e5da95d7036f19556a4a732525e84289a425f4f6fd8e412a9d7442f
  languageName: node
  linkType: hard

"postcss@npm:^8.4.35, postcss@npm:^8.4.4, postcss@npm:^8.4.43, postcss@npm:^8.4.47, postcss@npm:^8.4.48":
  version: 8.4.49
  resolution: "postcss@npm:8.4.49"
  dependencies:
    nanoid: ^3.3.7
    picocolors: ^1.1.1
    source-map-js: ^1.2.1
  checksum: eb5d6cbdca24f50399aafa5d2bea489e4caee4c563ea1edd5a2485bc5f84e9ceef3febf170272bc83a99c31d23a316ad179213e853f34c2a7a8ffa534559d63a
  languageName: node
  linkType: hard

"prelude-ls@npm:^1.2.1":
  version: 1.2.1
  resolution: "prelude-ls@npm:1.2.1"
  checksum: cd192ec0d0a8e4c6da3bb80e4f62afe336df3f76271ac6deb0e6a36187133b6073a19e9727a1ff108cd8b9982e4768850d413baa71214dd80c7979617dca827a
  languageName: node
  linkType: hard

"prepend-http@npm:^2.0.0":
  version: 2.0.0
  resolution: "prepend-http@npm:2.0.0"
  checksum: 7694a9525405447662c1ffd352fcb41b6410c705b739b6f4e3a3e21cf5fdede8377890088e8934436b8b17ba55365a615f153960f30877bf0d0392f9e93503ea
  languageName: node
  linkType: hard

"prettier-plugin-tailwindcss@npm:^0.5.11":
  version: 0.5.14
  resolution: "prettier-plugin-tailwindcss@npm:0.5.14"
  peerDependencies:
    "@ianvs/prettier-plugin-sort-imports": "*"
    "@prettier/plugin-pug": "*"
    "@shopify/prettier-plugin-liquid": "*"
    "@trivago/prettier-plugin-sort-imports": "*"
    "@zackad/prettier-plugin-twig-melody": "*"
    prettier: ^3.0
    prettier-plugin-astro: "*"
    prettier-plugin-css-order: "*"
    prettier-plugin-import-sort: "*"
    prettier-plugin-jsdoc: "*"
    prettier-plugin-marko: "*"
    prettier-plugin-organize-attributes: "*"
    prettier-plugin-organize-imports: "*"
    prettier-plugin-sort-imports: "*"
    prettier-plugin-style-order: "*"
    prettier-plugin-svelte: "*"
  peerDependenciesMeta:
    "@ianvs/prettier-plugin-sort-imports":
      optional: true
    "@prettier/plugin-pug":
      optional: true
    "@shopify/prettier-plugin-liquid":
      optional: true
    "@trivago/prettier-plugin-sort-imports":
      optional: true
    "@zackad/prettier-plugin-twig-melody":
      optional: true
    prettier-plugin-astro:
      optional: true
    prettier-plugin-css-order:
      optional: true
    prettier-plugin-import-sort:
      optional: true
    prettier-plugin-jsdoc:
      optional: true
    prettier-plugin-marko:
      optional: true
    prettier-plugin-organize-attributes:
      optional: true
    prettier-plugin-organize-imports:
      optional: true
    prettier-plugin-sort-imports:
      optional: true
    prettier-plugin-style-order:
      optional: true
    prettier-plugin-svelte:
      optional: true
  checksum: 205a774e53cdd9f8e61640ae3b778402758fad3d215fc57dd31172f12cf2b145095f5f973a431d495a6551b638bbb7e54c9f3a49848f0a0f755f5ad975b7c321
  languageName: node
  linkType: hard

"prettier@npm:^3.2.5":
  version: 3.4.2
  resolution: "prettier@npm:3.4.2"
  bin:
    prettier: bin/prettier.cjs
  checksum: 061c84513db62d3944c8dc8df36584dad82883ce4e49efcdbedd8703dce5b173c33fd9d2a4e1725d642a3b713c932b55418342eaa347479bc4a9cca114a04cd0
  languageName: node
  linkType: hard

"proc-log@npm:^5.0.0":
  version: 5.0.0
  resolution: "proc-log@npm:5.0.0"
  checksum: c78b26ecef6d5cce4a7489a1e9923d7b4b1679028c8654aef0463b27f4a90b0946cd598f55799da602895c52feb085ec76381d007ab8dcceebd40b89c2f9dfe0
  languageName: node
  linkType: hard

"promise-coalesce@npm:^1.1.2":
  version: 1.1.2
  resolution: "promise-coalesce@npm:1.1.2"
  checksum: 6f951b5db40ca78d09ad0f72adea0a2e9c5bdb0fc5b3ef218c611e2191c6ce2e9f752f815630fd07875dd1c9f04cc7327d6bada04b662dce986ce86ab88c2d5e
  languageName: node
  linkType: hard

"promise-retry@npm:^2.0.1":
  version: 2.0.1
  resolution: "promise-retry@npm:2.0.1"
  dependencies:
    err-code: ^2.0.2
    retry: ^0.12.0
  checksum: f96a3f6d90b92b568a26f71e966cbbc0f63ab85ea6ff6c81284dc869b41510e6cdef99b6b65f9030f0db422bf7c96652a3fff9f2e8fb4a0f069d8f4430359429
  languageName: node
  linkType: hard

"pstree.remy@npm:^1.1.8":
  version: 1.1.8
  resolution: "pstree.remy@npm:1.1.8"
  checksum: 5cb53698d6bb34dfb278c8a26957964aecfff3e161af5fbf7cee00bbe9d8547c7aced4bd9cb193bce15fb56e9e4220fc02a5bf9c14345ffb13a36b858701ec2d
  languageName: node
  linkType: hard

"pump@npm:^3.0.0":
  version: 3.0.2
  resolution: "pump@npm:3.0.2"
  dependencies:
    end-of-stream: ^1.1.0
    once: ^1.3.1
  checksum: e0c4216874b96bd25ddf31a0b61a5613e26cc7afa32379217cf39d3915b0509def3565f5f6968fafdad2894c8bbdbd67d340e84f3634b2a29b950cffb6442d9f
  languageName: node
  linkType: hard

"punycode@npm:^2.1.0":
  version: 2.3.1
  resolution: "punycode@npm:2.3.1"
  checksum: bb0a0ceedca4c3c57a9b981b90601579058903c62be23c5e8e843d2c2d4148a3ecf029d5133486fb0e1822b098ba8bba09e89d6b21742d02fa26bda6441a6fb2
  languageName: node
  linkType: hard

"pupa@npm:^2.1.1":
  version: 2.1.1
  resolution: "pupa@npm:2.1.1"
  dependencies:
    escape-goat: ^2.0.0
  checksum: 49529e50372ffdb0cccf0efa0f3b3cb0a2c77805d0d9cc2725bd2a0f6bb414631e61c93a38561b26be1259550b7bb6c2cb92315aa09c8bf93f3bdcb49f2b2fb7
  languageName: node
  linkType: hard

"qs@npm:^6.5.2":
  version: 6.13.1
  resolution: "qs@npm:6.13.1"
  dependencies:
    side-channel: ^1.0.6
  checksum: 86c5059146955fab76624e95771031541328c171b1d63d48a7ac3b1fdffe262faf8bc5fcadc1684e6f3da3ec87a8dedc8c0009792aceb20c5e94dc34cf468bb9
  languageName: node
  linkType: hard

"queue-microtask@npm:^1.2.2":
  version: 1.2.3
  resolution: "queue-microtask@npm:1.2.3"
  checksum: b676f8c040cdc5b12723ad2f91414d267605b26419d5c821ff03befa817ddd10e238d22b25d604920340fd73efd8ba795465a0377c4adf45a4a41e4234e42dc4
  languageName: node
  linkType: hard

"raw-body@npm:^2.3.3":
  version: 2.5.2
  resolution: "raw-body@npm:2.5.2"
  dependencies:
    bytes: 3.1.2
    http-errors: 2.0.0
    iconv-lite: 0.4.24
    unpipe: 1.0.0
  checksum: ba1583c8d8a48e8fbb7a873fdbb2df66ea4ff83775421bfe21ee120140949ab048200668c47d9ae3880012f6e217052690628cf679ddfbd82c9fc9358d574676
  languageName: node
  linkType: hard

"rc@npm:1.2.8, rc@npm:^1.2.8":
  version: 1.2.8
  resolution: "rc@npm:1.2.8"
  dependencies:
    deep-extend: ^0.6.0
    ini: ~1.3.0
    minimist: ^1.2.0
    strip-json-comments: ~2.0.1
  bin:
    rc: ./cli.js
  checksum: 2e26e052f8be2abd64e6d1dabfbd7be03f80ec18ccbc49562d31f617d0015fbdbcf0f9eed30346ea6ab789e0fdfe4337f033f8016efdbee0df5354751842080e
  languageName: node
  linkType: hard

"read-cache@npm:^1.0.0":
  version: 1.0.0
  resolution: "read-cache@npm:1.0.0"
  dependencies:
    pify: ^2.3.0
  checksum: cffc728b9ede1e0667399903f9ecaf3789888b041c46ca53382fa3a06303e5132774dc0a96d0c16aa702dbac1ea0833d5a868d414f5ab2af1e1438e19e6657c6
  languageName: node
  linkType: hard

"readable-stream@npm:^3.4.0, readable-stream@npm:^3.6.2":
  version: 3.6.2
  resolution: "readable-stream@npm:3.6.2"
  dependencies:
    inherits: ^2.0.3
    string_decoder: ^1.1.1
    util-deprecate: ^1.0.1
  checksum: bdcbe6c22e846b6af075e32cf8f4751c2576238c5043169a1c221c92ee2878458a816a4ea33f4c67623c0b6827c8a400409bfb3cf0bf3381392d0b1dfb52ac8d
  languageName: node
  linkType: hard

"readdirp@npm:~3.6.0":
  version: 3.6.0
  resolution: "readdirp@npm:3.6.0"
  dependencies:
    picomatch: ^2.2.1
  checksum: 1ced032e6e45670b6d7352d71d21ce7edf7b9b928494dcaba6f11fba63180d9da6cd7061ebc34175ffda6ff529f481818c962952004d273178acd70f7059b320
  languageName: node
  linkType: hard

"reflect.getprototypeof@npm:^1.0.6":
  version: 1.0.8
  resolution: "reflect.getprototypeof@npm:1.0.8"
  dependencies:
    call-bind: ^1.0.8
    define-properties: ^1.2.1
    dunder-proto: ^1.0.0
    es-abstract: ^1.23.5
    es-errors: ^1.3.0
    get-intrinsic: ^1.2.4
    gopd: ^1.2.0
    which-builtin-type: ^1.2.0
  checksum: d7dcbe34bec80f50e2b2f824af83302aae2520863b56b967052ade76402cddcb61933690931d567b973ff7635ae39ff655237ad9cdb2be755190eace95c1768b
  languageName: node
  linkType: hard

"regenerator-runtime@npm:^0.14.0":
  version: 0.14.1
  resolution: "regenerator-runtime@npm:0.14.1"
  checksum: 9f57c93277b5585d3c83b0cf76be47b473ae8c6d9142a46ce8b0291a04bb2cf902059f0f8445dcabb3fb7378e5fe4bb4ea1e008876343d42e46d3b484534ce38
  languageName: node
  linkType: hard

"regexp.prototype.flags@npm:^1.5.3":
  version: 1.5.3
  resolution: "regexp.prototype.flags@npm:1.5.3"
  dependencies:
    call-bind: ^1.0.7
    define-properties: ^1.2.1
    es-errors: ^1.3.0
    set-function-name: ^2.0.2
  checksum: 83ff0705b837f7cb6d664010a11642250f36d3f642263dd0f3bdfe8f150261aa7b26b50ee97f21c1da30ef82a580bb5afedbef5f45639d69edaafbeac9bbb0ed
  languageName: node
  linkType: hard

"regexpp@npm:^3.0.0":
  version: 3.2.0
  resolution: "regexpp@npm:3.2.0"
  checksum: a78dc5c7158ad9ddcfe01aa9144f46e192ddbfa7b263895a70a5c6c73edd9ce85faf7c0430e59ac38839e1734e275b9c3de5c57ee3ab6edc0e0b1bdebefccef8
  languageName: node
  linkType: hard

"registry-auth-token@npm:^4.0.0":
  version: 4.2.2
  resolution: "registry-auth-token@npm:4.2.2"
  dependencies:
    rc: 1.2.8
  checksum: c5030198546ecfdcbcb0722cbc3e260c4f5f174d8d07bdfedd4620e79bfdf17a2db735aa230d600bd388fce6edd26c0a9ed2eb7e9b4641ec15213a28a806688b
  languageName: node
  linkType: hard

"registry-url@npm:^5.0.0":
  version: 5.1.0
  resolution: "registry-url@npm:5.1.0"
  dependencies:
    rc: ^1.2.8
  checksum: bcea86c84a0dbb66467b53187fadebfea79017cddfb4a45cf27530d7275e49082fe9f44301976eb0164c438e395684bcf3dae4819b36ff9d1640d8cc60c73df9
  languageName: node
  linkType: hard

"require-from-string@npm:^2.0.2":
  version: 2.0.2
  resolution: "require-from-string@npm:2.0.2"
  checksum: a03ef6895445f33a4015300c426699bc66b2b044ba7b670aa238610381b56d3f07c686251740d575e22f4c87531ba662d06937508f0f3c0f1ddc04db3130560b
  languageName: node
  linkType: hard

"resolve-from@npm:^4.0.0":
  version: 4.0.0
  resolution: "resolve-from@npm:4.0.0"
  checksum: f4ba0b8494846a5066328ad33ef8ac173801a51739eb4d63408c847da9a2e1c1de1e6cbbf72699211f3d13f8fc1325648b169bd15eb7da35688e30a5fb0e4a7f
  languageName: node
  linkType: hard

"resolve-path@npm:^1.4.0":
  version: 1.4.0
  resolution: "resolve-path@npm:1.4.0"
  dependencies:
    http-errors: ~1.6.2
    path-is-absolute: 1.0.1
  checksum: 1a39f569ee54dd5f8ee8576ef8671c9724bea65d9f9982fbb5352af9fb4e500e1e459c1bfb1ae3ebfd8d43a709c3a01dfa4f46cf5b831e45e2caed4f1a208300
  languageName: node
  linkType: hard

"resolve-pkg-maps@npm:^1.0.0":
  version: 1.0.0
  resolution: "resolve-pkg-maps@npm:1.0.0"
  checksum: 1012afc566b3fdb190a6309cc37ef3b2dcc35dff5fa6683a9d00cd25c3247edfbc4691b91078c97adc82a29b77a2660c30d791d65dab4fc78bfc473f60289977
  languageName: node
  linkType: hard

"resolve@npm:^1.1.7, resolve@npm:^1.10.1, resolve@npm:^1.22.2, resolve@npm:^1.22.4, resolve@npm:^1.22.8":
  version: 1.22.9
  resolution: "resolve@npm:1.22.9"
  dependencies:
    is-core-module: ^2.16.0
    path-parse: ^1.0.7
    supports-preserve-symlinks-flag: ^1.0.0
  bin:
    resolve: bin/resolve
  checksum: dd7da6c4ccc60bb4884f636b987102bb1cae6c486f7172361719a7f52769660c9a83a2d3520bef71d91f3da27e4fb8a40404c63ee4372a15863f3208bef5af67
  languageName: node
  linkType: hard

"resolve@patch:resolve@^1.1.7#~builtin<compat/resolve>, resolve@patch:resolve@^1.10.1#~builtin<compat/resolve>, resolve@patch:resolve@^1.22.2#~builtin<compat/resolve>, resolve@patch:resolve@^1.22.4#~builtin<compat/resolve>, resolve@patch:resolve@^1.22.8#~builtin<compat/resolve>":
  version: 1.22.9
  resolution: "resolve@patch:resolve@npm%3A1.22.9#~builtin<compat/resolve>::version=1.22.9&hash=c3c19d"
  dependencies:
    is-core-module: ^2.16.0
    path-parse: ^1.0.7
    supports-preserve-symlinks-flag: ^1.0.0
  bin:
    resolve: bin/resolve
  checksum: 8db5c0f16ab65f58c036cb5be0964605c97c29b9fdf1e20f298ec027e2a4fd96ad0413aa14f6e761629956dc552cd478c2f9b6c5a07e37e4c85209090162501e
  languageName: node
  linkType: hard

"responselike@npm:^1.0.2":
  version: 1.0.2
  resolution: "responselike@npm:1.0.2"
  dependencies:
    lowercase-keys: ^1.0.0
  checksum: 2e9e70f1dcca3da621a80ce71f2f9a9cad12c047145c6ece20df22f0743f051cf7c73505e109814915f23f9e34fb0d358e22827723ee3d56b623533cab8eafcd
  languageName: node
  linkType: hard

"restore-cursor@npm:^3.1.0":
  version: 3.1.0
  resolution: "restore-cursor@npm:3.1.0"
  dependencies:
    onetime: ^5.1.0
    signal-exit: ^3.0.2
  checksum: f877dd8741796b909f2a82454ec111afb84eb45890eb49ac947d87991379406b3b83ff9673a46012fca0d7844bb989f45cc5b788254cf1a39b6b5a9659de0630
  languageName: node
  linkType: hard

"retry@npm:^0.12.0":
  version: 0.12.0
  resolution: "retry@npm:0.12.0"
  checksum: 623bd7d2e5119467ba66202d733ec3c2e2e26568074923bc0585b6b99db14f357e79bdedb63cab56cec47491c4a0da7e6021a7465ca6dc4f481d3898fdd3158c
  languageName: node
  linkType: hard

"reusify@npm:^1.0.4":
  version: 1.0.4
  resolution: "reusify@npm:1.0.4"
  checksum: c3076ebcc22a6bc252cb0b9c77561795256c22b757f40c0d8110b1300723f15ec0fc8685e8d4ea6d7666f36c79ccc793b1939c748bf36f18f542744a4e379fcc
  languageName: node
  linkType: hard

"rimraf@npm:^3.0.2":
  version: 3.0.2
  resolution: "rimraf@npm:3.0.2"
  dependencies:
    glob: ^7.1.3
  bin:
    rimraf: bin.js
  checksum: 87f4164e396f0171b0a3386cc1877a817f572148ee13a7e113b238e48e8a9f2f31d009a92ec38a591ff1567d9662c6b67fd8818a2dbbaed74bc26a87a2a4a9a0
  languageName: node
  linkType: hard

"rimraf@npm:^5.0.5":
  version: 5.0.10
  resolution: "rimraf@npm:5.0.10"
  dependencies:
    glob: ^10.3.7
  bin:
    rimraf: dist/esm/bin.mjs
  checksum: 50e27388dd2b3fa6677385fc1e2966e9157c89c86853b96d02e6915663a96b7ff4d590e14f6f70e90f9b554093aa5dbc05ac3012876be558c06a65437337bc05
  languageName: node
  linkType: hard

"rollup@npm:^4.20.0":
  version: 4.28.1
  resolution: "rollup@npm:4.28.1"
  dependencies:
    "@rollup/rollup-android-arm-eabi": 4.28.1
    "@rollup/rollup-android-arm64": 4.28.1
    "@rollup/rollup-darwin-arm64": 4.28.1
    "@rollup/rollup-darwin-x64": 4.28.1
    "@rollup/rollup-freebsd-arm64": 4.28.1
    "@rollup/rollup-freebsd-x64": 4.28.1
    "@rollup/rollup-linux-arm-gnueabihf": 4.28.1
    "@rollup/rollup-linux-arm-musleabihf": 4.28.1
    "@rollup/rollup-linux-arm64-gnu": 4.28.1
    "@rollup/rollup-linux-arm64-musl": 4.28.1
    "@rollup/rollup-linux-loongarch64-gnu": 4.28.1
    "@rollup/rollup-linux-powerpc64le-gnu": 4.28.1
    "@rollup/rollup-linux-riscv64-gnu": 4.28.1
    "@rollup/rollup-linux-s390x-gnu": 4.28.1
    "@rollup/rollup-linux-x64-gnu": 4.28.1
    "@rollup/rollup-linux-x64-musl": 4.28.1
    "@rollup/rollup-win32-arm64-msvc": 4.28.1
    "@rollup/rollup-win32-ia32-msvc": 4.28.1
    "@rollup/rollup-win32-x64-msvc": 4.28.1
    "@types/estree": 1.0.6
    fsevents: ~2.3.2
  dependenciesMeta:
    "@rollup/rollup-android-arm-eabi":
      optional: true
    "@rollup/rollup-android-arm64":
      optional: true
    "@rollup/rollup-darwin-arm64":
      optional: true
    "@rollup/rollup-darwin-x64":
      optional: true
    "@rollup/rollup-freebsd-arm64":
      optional: true
    "@rollup/rollup-freebsd-x64":
      optional: true
    "@rollup/rollup-linux-arm-gnueabihf":
      optional: true
    "@rollup/rollup-linux-arm-musleabihf":
      optional: true
    "@rollup/rollup-linux-arm64-gnu":
      optional: true
    "@rollup/rollup-linux-arm64-musl":
      optional: true
    "@rollup/rollup-linux-loongarch64-gnu":
      optional: true
    "@rollup/rollup-linux-powerpc64le-gnu":
      optional: true
    "@rollup/rollup-linux-riscv64-gnu":
      optional: true
    "@rollup/rollup-linux-s390x-gnu":
      optional: true
    "@rollup/rollup-linux-x64-gnu":
      optional: true
    "@rollup/rollup-linux-x64-musl":
      optional: true
    "@rollup/rollup-win32-arm64-msvc":
      optional: true
    "@rollup/rollup-win32-ia32-msvc":
      optional: true
    "@rollup/rollup-win32-x64-msvc":
      optional: true
    fsevents:
      optional: true
  bin:
    rollup: dist/bin/rollup
  checksum: 092b87526d32e6f97aa4912184f7b29b7e3b28009b2b8c6cac841c311c07e7636f6108c4338f1f66d8ed699ddd9100db4218faf50d6cfd358b2a85749aeb8935
  languageName: node
  linkType: hard

"run-async@npm:^3.0.0":
  version: 3.0.0
  resolution: "run-async@npm:3.0.0"
  checksum: 280c03d5a88603f48103fc6fd69f07fb0c392a1e0d319c34ec96a2516030e07ba06f79231a563c78698b882649c2fc1fda601bc84705f57d50efcd1fa506cfc0
  languageName: node
  linkType: hard

"run-parallel@npm:^1.1.9":
  version: 1.2.0
  resolution: "run-parallel@npm:1.2.0"
  dependencies:
    queue-microtask: ^1.2.2
  checksum: cb4f97ad25a75ebc11a8ef4e33bb962f8af8516bb2001082ceabd8902e15b98f4b84b4f8a9b222e5d57fc3bd1379c483886ed4619367a7680dad65316993021d
  languageName: node
  linkType: hard

"safe-array-concat@npm:^1.1.2":
  version: 1.1.3
  resolution: "safe-array-concat@npm:1.1.3"
  dependencies:
    call-bind: ^1.0.8
    call-bound: ^1.0.2
    get-intrinsic: ^1.2.6
    has-symbols: ^1.1.0
    isarray: ^2.0.5
  checksum: 00f6a68140e67e813f3ad5e73e6dedcf3e42a9fa01f04d44b0d3f7b1f4b257af876832a9bfc82ac76f307e8a6cc652e3cf95876048a26cbec451847cf6ae3707
  languageName: node
  linkType: hard

"safe-buffer@npm:5.2.1, safe-buffer@npm:~5.2.0":
  version: 5.2.1
  resolution: "safe-buffer@npm:5.2.1"
  checksum: b99c4b41fdd67a6aaf280fcd05e9ffb0813654894223afb78a31f14a19ad220bba8aba1cb14eddce1fcfb037155fe6de4e861784eb434f7d11ed58d1e70dd491
  languageName: node
  linkType: hard

"safe-regex-test@npm:^1.0.3, safe-regex-test@npm:^1.1.0":
  version: 1.1.0
  resolution: "safe-regex-test@npm:1.1.0"
  dependencies:
    call-bound: ^1.0.2
    es-errors: ^1.3.0
    is-regex: ^1.2.1
  checksum: 3c809abeb81977c9ed6c869c83aca6873ea0f3ab0f806b8edbba5582d51713f8a6e9757d24d2b4b088f563801475ea946c8e77e7713e8c65cdd02305b6caedab
  languageName: node
  linkType: hard

"safe-stable-stringify@npm:^2.3.1":
  version: 2.5.0
  resolution: "safe-stable-stringify@npm:2.5.0"
  checksum: d3ce103ed43c6c2f523e39607208bfb1c73aa48179fc5be53c3aa97c118390bffd4d55e012f5393b982b65eb3e0ee954dd57b547930d3f242b0053dcdb923d17
  languageName: node
  linkType: hard

"safer-buffer@npm:>= 2.1.2 < 3, safer-buffer@npm:>= 2.1.2 < 3.0.0":
  version: 2.1.2
  resolution: "safer-buffer@npm:2.1.2"
  checksum: cab8f25ae6f1434abee8d80023d7e72b598cf1327164ddab31003c51215526801e40b66c5e65d658a0af1e9d6478cadcb4c745f4bd6751f97d8644786c0978b0
  languageName: node
  linkType: hard

"scule@npm:^1.3.0":
  version: 1.3.0
  resolution: "scule@npm:1.3.0"
  checksum: f2968b292e33c0eddca4a68b5c70f08dfc8479e492461c248f72873deaf77ae87c9f27dde7a342b3cb6394d2fae9665890b07a2243f79cff5cba65c9525ccf7e
  languageName: node
  linkType: hard

"search_with_ai@workspace:.":
  version: 0.0.0-use.local
  resolution: "search_with_ai@workspace:."
  dependencies:
    turbo: ^2.3.0
  languageName: unknown
  linkType: soft

"semver-diff@npm:^3.1.1":
  version: 3.1.1
  resolution: "semver-diff@npm:3.1.1"
  dependencies:
    semver: ^6.3.0
  checksum: 8bbe5a5d7add2d5e51b72314a9215cd294d71f41cdc2bf6bd59ee76411f3610b576172896f1d191d0d7294cb9f2f847438d2ee158adacc0c224dca79052812fe
  languageName: node
  linkType: hard

"semver@npm:^5.5.0":
  version: 5.7.2
  resolution: "semver@npm:5.7.2"
  bin:
    semver: bin/semver
  checksum: fb4ab5e0dd1c22ce0c937ea390b4a822147a9c53dbd2a9a0132f12fe382902beef4fbf12cf51bb955248d8d15874ce8cd89532569756384f994309825f10b686
  languageName: node
  linkType: hard

"semver@npm:^6.0.0, semver@npm:^6.1.0, semver@npm:^6.2.0, semver@npm:^6.3.0, semver@npm:^6.3.1":
  version: 6.3.1
  resolution: "semver@npm:6.3.1"
  bin:
    semver: bin/semver.js
  checksum: ae47d06de28836adb9d3e25f22a92943477371292d9b665fb023fae278d345d508ca1958232af086d85e0155aee22e313e100971898bbb8d5d89b8b1d4054ca2
  languageName: node
  linkType: hard

"semver@npm:^7.0.0, semver@npm:^7.3.4, semver@npm:^7.3.5, semver@npm:^7.3.6, semver@npm:^7.5.3, semver@npm:^7.5.4, semver@npm:^7.6.0, semver@npm:^7.6.3":
  version: 7.6.3
  resolution: "semver@npm:7.6.3"
  bin:
    semver: bin/semver.js
  checksum: 4110ec5d015c9438f322257b1c51fe30276e5f766a3f64c09edd1d7ea7118ecbc3f379f3b69032bacf13116dc7abc4ad8ce0d7e2bd642e26b0d271b56b61a7d8
  languageName: node
  linkType: hard

"server@workspace:apps/server":
  version: 0.0.0-use.local
  resolution: "server@workspace:apps/server"
  dependencies:
    "@dotenvx/dotenvx": ^0.24.0
    "@google/generative-ai": ^0.2.1
    "@koa/bodyparser": ^5.1.0
    "@koa/cors": ^5.0.0
    "@koa/router": ^12.0.1
    "@lmstudio/sdk": ^0.1.0
    "@types/koa": ^2.15.0
    "@types/koa-static": ^4.0.4
    "@types/koa__cors": ^5.0.0
    "@types/koa__router": ^12.0.4
    cache-manager: ^5.4.0
    cheerio: ^1.0.0
    cross-env: ^7.0.3
    dotenv: ^16.4.5
    eslint: ^8.57.0
    eslint-config-standard: ^17.1.0
    eslint-plugin-import: ^2.29.1
    eslint-plugin-n: ^16.6.2
    eslint-plugin-node: ^11.1.0
    eslint-plugin-promise: ^6.1.1
    fetch-sse: 1.0.23
    koa: ^2.15.0
    koa-static: ^5.0.0
    koa2-connect-history-api-fallback: ^0.1.3
    nodemon: ^3.1.0
    ollama: ^0.5.9
    openai: ^4.61.1
    tencentcloud-sdk-nodejs: ^4.0.973
    ts-node: ^10.9.2
    typescript: ^5.3.3
    typescript-eslint: ^7.0.2
    winston: ^3.15.0
  languageName: unknown
  linkType: soft

"set-function-length@npm:^1.2.2":
  version: 1.2.2
  resolution: "set-function-length@npm:1.2.2"
  dependencies:
    define-data-property: ^1.1.4
    es-errors: ^1.3.0
    function-bind: ^1.1.2
    get-intrinsic: ^1.2.4
    gopd: ^1.0.1
    has-property-descriptors: ^1.0.2
  checksum: a8248bdacdf84cb0fab4637774d9fb3c7a8e6089866d04c817583ff48e14149c87044ce683d7f50759a8c50fb87c7a7e173535b06169c87ef76f5fb276dfff72
  languageName: node
  linkType: hard

"set-function-name@npm:^2.0.2":
  version: 2.0.2
  resolution: "set-function-name@npm:2.0.2"
  dependencies:
    define-data-property: ^1.1.4
    es-errors: ^1.3.0
    functions-have-names: ^1.2.3
    has-property-descriptors: ^1.0.2
  checksum: d6229a71527fd0404399fc6227e0ff0652800362510822a291925c9d7b48a1ca1a468b11b281471c34cd5a2da0db4f5d7ff315a61d26655e77f6e971e6d0c80f
  languageName: node
  linkType: hard

"setprototypeof@npm:1.1.0":
  version: 1.1.0
  resolution: "setprototypeof@npm:1.1.0"
  checksum: 27cb44304d6c9e1a23bc6c706af4acaae1a7aa1054d4ec13c05f01a99fd4887109a83a8042b67ad90dbfcd100d43efc171ee036eb080667172079213242ca36e
  languageName: node
  linkType: hard

"setprototypeof@npm:1.2.0":
  version: 1.2.0
  resolution: "setprototypeof@npm:1.2.0"
  checksum: be18cbbf70e7d8097c97f713a2e76edf84e87299b40d085c6bf8b65314e994cc15e2e317727342fa6996e38e1f52c59720b53fe621e2eb593a6847bf0356db89
  languageName: node
  linkType: hard

"shebang-command@npm:^1.2.0":
  version: 1.2.0
  resolution: "shebang-command@npm:1.2.0"
  dependencies:
    shebang-regex: ^1.0.0
  checksum: 9eed1750301e622961ba5d588af2212505e96770ec376a37ab678f965795e995ade7ed44910f5d3d3cb5e10165a1847f52d3348c64e146b8be922f7707958908
  languageName: node
  linkType: hard

"shebang-command@npm:^2.0.0":
  version: 2.0.0
  resolution: "shebang-command@npm:2.0.0"
  dependencies:
    shebang-regex: ^3.0.0
  checksum: 6b52fe87271c12968f6a054e60f6bde5f0f3d2db483a1e5c3e12d657c488a15474121a1d55cd958f6df026a54374ec38a4a963988c213b7570e1d51575cea7fa
  languageName: node
  linkType: hard

"shebang-regex@npm:^1.0.0":
  version: 1.0.0
  resolution: "shebang-regex@npm:1.0.0"
  checksum: 404c5a752cd40f94591dfd9346da40a735a05139dac890ffc229afba610854d8799aaa52f87f7e0c94c5007f2c6af55bdcaeb584b56691926c5eaf41dc8f1372
  languageName: node
  linkType: hard

"shebang-regex@npm:^3.0.0":
  version: 3.0.0
  resolution: "shebang-regex@npm:3.0.0"
  checksum: 1a2bcae50de99034fcd92ad4212d8e01eedf52c7ec7830eedcf886622804fe36884278f2be8be0ea5fde3fd1c23911643a4e0f726c8685b61871c8908af01222
  languageName: node
  linkType: hard

"side-channel-list@npm:^1.0.0":
  version: 1.0.0
  resolution: "side-channel-list@npm:1.0.0"
  dependencies:
    es-errors: ^1.3.0
    object-inspect: ^1.13.3
  checksum: 603b928997abd21c5a5f02ae6b9cc36b72e3176ad6827fab0417ead74580cc4fb4d5c7d0a8a2ff4ead34d0f9e35701ed7a41853dac8a6d1a664fcce1a044f86f
  languageName: node
  linkType: hard

"side-channel-map@npm:^1.0.1":
  version: 1.0.1
  resolution: "side-channel-map@npm:1.0.1"
  dependencies:
    call-bound: ^1.0.2
    es-errors: ^1.3.0
    get-intrinsic: ^1.2.5
    object-inspect: ^1.13.3
  checksum: 42501371cdf71f4ccbbc9c9e2eb00aaaab80a4c1c429d5e8da713fd4d39ef3b8d4a4b37ed4f275798a65260a551a7131fd87fe67e922dba4ac18586d6aab8b06
  languageName: node
  linkType: hard

"side-channel-weakmap@npm:^1.0.2":
  version: 1.0.2
  resolution: "side-channel-weakmap@npm:1.0.2"
  dependencies:
    call-bound: ^1.0.2
    es-errors: ^1.3.0
    get-intrinsic: ^1.2.5
    object-inspect: ^1.13.3
    side-channel-map: ^1.0.1
  checksum: a815c89bc78c5723c714ea1a77c938377ea710af20d4fb886d362b0d1f8ac73a17816a5f6640f354017d7e292a43da9c5e876c22145bac00b76cfb3468001736
  languageName: node
  linkType: hard

"side-channel@npm:^1.0.6, side-channel@npm:^1.1.0":
  version: 1.1.0
  resolution: "side-channel@npm:1.1.0"
  dependencies:
    es-errors: ^1.3.0
    object-inspect: ^1.13.3
    side-channel-list: ^1.0.0
    side-channel-map: ^1.0.1
    side-channel-weakmap: ^1.0.2
  checksum: bf73d6d6682034603eb8e99c63b50155017ed78a522d27c2acec0388a792c3ede3238b878b953a08157093b85d05797217d270b7666ba1f111345fbe933380ff
  languageName: node
  linkType: hard

"signal-exit@npm:^3.0.0, signal-exit@npm:^3.0.2, signal-exit@npm:^3.0.3":
  version: 3.0.7
  resolution: "signal-exit@npm:3.0.7"
  checksum: a2f098f247adc367dffc27845853e9959b9e88b01cb301658cfe4194352d8d2bb32e18467c786a7fe15f1d44b233ea35633d076d5e737870b7139949d1ab6318
  languageName: node
  linkType: hard

"signal-exit@npm:^4.0.1, signal-exit@npm:^4.1.0":
  version: 4.1.0
  resolution: "signal-exit@npm:4.1.0"
  checksum: 64c757b498cb8629ffa5f75485340594d2f8189e9b08700e69199069c8e3070fb3e255f7ab873c05dc0b3cec412aea7402e10a5990cb6a050bd33ba062a6c549
  languageName: node
  linkType: hard

"simple-swizzle@npm:^0.2.2":
  version: 0.2.2
  resolution: "simple-swizzle@npm:0.2.2"
  dependencies:
    is-arrayish: ^0.3.1
  checksum: a7f3f2ab5c76c4472d5c578df892e857323e452d9f392e1b5cf74b74db66e6294a1e1b8b390b519fa1b96b5b613f2a37db6cffef52c3f1f8f3c5ea64eb2d54c0
  languageName: node
  linkType: hard

"simple-update-notifier@npm:^2.0.0":
  version: 2.0.0
  resolution: "simple-update-notifier@npm:2.0.0"
  dependencies:
    semver: ^7.5.3
  checksum: 9ba00d38ce6a29682f64a46213834e4eb01634c2f52c813a9a7b8873ca49cdbb703696f3290f3b27dc067de6d9418b0b84bef22c3eb074acf352529b2d6c27fd
  languageName: node
  linkType: hard

"slash@npm:^3.0.0":
  version: 3.0.0
  resolution: "slash@npm:3.0.0"
  checksum: 94a93fff615f25a999ad4b83c9d5e257a7280c90a32a7cb8b4a87996e4babf322e469c42b7f649fd5796edd8687652f3fb452a86dc97a816f01113183393f11c
  languageName: node
  linkType: hard

"smart-buffer@npm:^4.2.0":
  version: 4.2.0
  resolution: "smart-buffer@npm:4.2.0"
  checksum: b5167a7142c1da704c0e3af85c402002b597081dd9575031a90b4f229ca5678e9a36e8a374f1814c8156a725d17008ae3bde63b92f9cfd132526379e580bec8b
  languageName: node
  linkType: hard

"socks-proxy-agent@npm:^8.0.3":
  version: 8.0.5
  resolution: "socks-proxy-agent@npm:8.0.5"
  dependencies:
    agent-base: ^7.1.2
    debug: ^4.3.4
    socks: ^2.8.3
  checksum: b4fbcdb7ad2d6eec445926e255a1fb95c975db0020543fbac8dfa6c47aecc6b3b619b7fb9c60a3f82c9b2969912a5e7e174a056ae4d98cb5322f3524d6036e1d
  languageName: node
  linkType: hard

"socks@npm:^2.8.3":
  version: 2.8.3
  resolution: "socks@npm:2.8.3"
  dependencies:
    ip-address: ^9.0.5
    smart-buffer: ^4.2.0
  checksum: 7a6b7f6eedf7482b9e4597d9a20e09505824208006ea8f2c49b71657427f3c137ca2ae662089baa73e1971c62322d535d9d0cf1c9235cf6f55e315c18203eadd
  languageName: node
  linkType: hard

"sortablejs@npm:^1.15.0":
  version: 1.15.6
  resolution: "sortablejs@npm:1.15.6"
  checksum: c9ed2f1a8cbff9ceea506b076d684a5444a3fb69f7382178415b842b031924b994591f22a05743f6da179cabccc4c063139b83180e09f34c636ccec5cec83bb9
  languageName: node
  linkType: hard

"source-map-js@npm:^1.0.2, source-map-js@npm:^1.2.0, source-map-js@npm:^1.2.1":
  version: 1.2.1
  resolution: "source-map-js@npm:1.2.1"
  checksum: 4eb0cd997cdf228bc253bcaff9340afeb706176e64868ecd20efbe6efea931465f43955612346d6b7318789e5265bdc419bc7669c1cebe3db0eb255f57efa76b
  languageName: node
  linkType: hard

"sprintf-js@npm:^1.1.3":
  version: 1.1.3
  resolution: "sprintf-js@npm:1.1.3"
  checksum: a3fdac7b49643875b70864a9d9b469d87a40dfeaf5d34d9d0c5b1cda5fd7d065531fcb43c76357d62254c57184a7b151954156563a4d6a747015cfb41021cad0
  languageName: node
  linkType: hard

"ssri@npm:^12.0.0":
  version: 12.0.0
  resolution: "ssri@npm:12.0.0"
  dependencies:
    minipass: ^7.0.3
  checksum: ef4b6b0ae47b4a69896f5f1c4375f953b9435388c053c36d27998bc3d73e046969ccde61ab659e679142971a0b08e50478a1228f62edb994105b280f17900c98
  languageName: node
  linkType: hard

"stack-trace@npm:0.0.x":
  version: 0.0.10
  resolution: "stack-trace@npm:0.0.10"
  checksum: 473036ad32f8c00e889613153d6454f9be0536d430eb2358ca51cad6b95cea08a3cc33cc0e34de66b0dad221582b08ed2e61ef8e13f4087ab690f388362d6610
  languageName: node
  linkType: hard

"statuses@npm:2.0.1":
  version: 2.0.1
  resolution: "statuses@npm:2.0.1"
  checksum: 18c7623fdb8f646fb213ca4051be4df7efb3484d4ab662937ca6fbef7ced9b9e12842709872eb3020cc3504b93bde88935c9f6417489627a7786f24f8031cbcb
  languageName: node
  linkType: hard

"statuses@npm:>= 1.4.0 < 2, statuses@npm:>= 1.5.0 < 2, statuses@npm:^1.5.0":
  version: 1.5.0
  resolution: "statuses@npm:1.5.0"
  checksum: c469b9519de16a4bb19600205cffb39ee471a5f17b82589757ca7bd40a8d92ebb6ed9f98b5a540c5d302ccbc78f15dc03cc0280dd6e00df1335568a5d5758a5c
  languageName: node
  linkType: hard

"string-width-cjs@npm:string-width@^4.2.0, string-width@npm:^4.0.0, string-width@npm:^4.1.0, string-width@npm:^4.2.2":
  version: 4.2.3
  resolution: "string-width@npm:4.2.3"
  dependencies:
    emoji-regex: ^8.0.0
    is-fullwidth-code-point: ^3.0.0
    strip-ansi: ^6.0.1
  checksum: e52c10dc3fbfcd6c3a15f159f54a90024241d0f149cf8aed2982a2d801d2e64df0bf1dc351cf8e95c3319323f9f220c16e740b06faecd53e2462df1d2b5443fb
  languageName: node
  linkType: hard

"string-width@npm:^5.0.1, string-width@npm:^5.1.2":
  version: 5.1.2
  resolution: "string-width@npm:5.1.2"
  dependencies:
    eastasianwidth: ^0.2.0
    emoji-regex: ^9.2.2
    strip-ansi: ^7.0.1
  checksum: 7369deaa29f21dda9a438686154b62c2c5f661f8dda60449088f9f980196f7908fc39fdd1803e3e01541970287cf5deae336798337e9319a7055af89dafa7193
  languageName: node
  linkType: hard

"string.prototype.trim@npm:^1.2.9":
  version: 1.2.10
  resolution: "string.prototype.trim@npm:1.2.10"
  dependencies:
    call-bind: ^1.0.8
    call-bound: ^1.0.2
    define-data-property: ^1.1.4
    define-properties: ^1.2.1
    es-abstract: ^1.23.5
    es-object-atoms: ^1.0.0
    has-property-descriptors: ^1.0.2
  checksum: 87659cd8561237b6c69f5376328fda934693aedde17bb7a2c57008e9d9ff992d0c253a391c7d8d50114e0e49ff7daf86a362f7961cf92f7564cd01342ca2e385
  languageName: node
  linkType: hard

"string.prototype.trimend@npm:^1.0.8":
  version: 1.0.9
  resolution: "string.prototype.trimend@npm:1.0.9"
  dependencies:
    call-bind: ^1.0.8
    call-bound: ^1.0.2
    define-properties: ^1.2.1
    es-object-atoms: ^1.0.0
  checksum: cb86f639f41d791a43627784be2175daa9ca3259c7cb83e7a207a729909b74f2ea0ec5d85de5761e6835e5f443e9420c6ff3f63a845378e4a61dd793177bc287
  languageName: node
  linkType: hard

"string.prototype.trimstart@npm:^1.0.8":
  version: 1.0.8
  resolution: "string.prototype.trimstart@npm:1.0.8"
  dependencies:
    call-bind: ^1.0.7
    define-properties: ^1.2.1
    es-object-atoms: ^1.0.0
  checksum: df1007a7f580a49d692375d996521dc14fd103acda7f3034b3c558a60b82beeed3a64fa91e494e164581793a8ab0ae2f59578a49896a7af6583c1f20472bce96
  languageName: node
  linkType: hard

"string_decoder@npm:^1.1.1":
  version: 1.3.0
  resolution: "string_decoder@npm:1.3.0"
  dependencies:
    safe-buffer: ~5.2.0
  checksum: 8417646695a66e73aefc4420eb3b84cc9ffd89572861fe004e6aeb13c7bc00e2f616247505d2dbbef24247c372f70268f594af7126f43548565c68c117bdeb56
  languageName: node
  linkType: hard

"strip-ansi-cjs@npm:strip-ansi@^6.0.1, strip-ansi@npm:^6.0.0, strip-ansi@npm:^6.0.1":
  version: 6.0.1
  resolution: "strip-ansi@npm:6.0.1"
  dependencies:
    ansi-regex: ^5.0.1
  checksum: f3cd25890aef3ba6e1a74e20896c21a46f482e93df4a06567cebf2b57edabb15133f1f94e57434e0a958d61186087b1008e89c94875d019910a213181a14fc8c
  languageName: node
  linkType: hard

"strip-ansi@npm:^7.0.1":
  version: 7.1.0
  resolution: "strip-ansi@npm:7.1.0"
  dependencies:
    ansi-regex: ^6.0.1
  checksum: 859c73fcf27869c22a4e4d8c6acfe690064659e84bef9458aa6d13719d09ca88dcfd40cbf31fd0be63518ea1a643fe070b4827d353e09533a5b0b9fd4553d64d
  languageName: node
  linkType: hard

"strip-bom@npm:^3.0.0":
  version: 3.0.0
  resolution: "strip-bom@npm:3.0.0"
  checksum: 8d50ff27b7ebe5ecc78f1fe1e00fcdff7af014e73cf724b46fb81ef889eeb1015fc5184b64e81a2efe002180f3ba431bdd77e300da5c6685d702780fbf0c8d5b
  languageName: node
  linkType: hard

"strip-eof@npm:^1.0.0":
  version: 1.0.0
  resolution: "strip-eof@npm:1.0.0"
  checksum: 40bc8ddd7e072f8ba0c2d6d05267b4e0a4800898c3435b5fb5f5a21e6e47dfaff18467e7aa0d1844bb5d6274c3097246595841fbfeb317e541974ee992cac506
  languageName: node
  linkType: hard

"strip-final-newline@npm:^2.0.0":
  version: 2.0.0
  resolution: "strip-final-newline@npm:2.0.0"
  checksum: 69412b5e25731e1938184b5d489c32e340605bb611d6140344abc3421b7f3c6f9984b21dff296dfcf056681b82caa3bb4cc996a965ce37bcfad663e92eae9c64
  languageName: node
  linkType: hard

"strip-json-comments@npm:^3.1.1":
  version: 3.1.1
  resolution: "strip-json-comments@npm:3.1.1"
  checksum: 492f73e27268f9b1c122733f28ecb0e7e8d8a531a6662efbd08e22cccb3f9475e90a1b82cab06a392f6afae6d2de636f977e231296400d0ec5304ba70f166443
  languageName: node
  linkType: hard

"strip-json-comments@npm:~2.0.1":
  version: 2.0.1
  resolution: "strip-json-comments@npm:2.0.1"
  checksum: 1074ccb63270d32ca28edfb0a281c96b94dc679077828135141f27d52a5a398ef5e78bcf22809d23cadc2b81dfbe345eb5fd8699b385c8b1128907dec4a7d1e1
  languageName: node
  linkType: hard

"strip-literal@npm:^2.1.1":
  version: 2.1.1
  resolution: "strip-literal@npm:2.1.1"
  dependencies:
    js-tokens: ^9.0.1
  checksum: 781f2018b2aa9e8e149882dfa35f4d284c244424e7b66cc62259796dbc4bc6da9d40f9206949ba12fa839f5f643d6c62a309f7eec4ff6e76ced15f0730f04831
  languageName: node
  linkType: hard

"sucrase@npm:^3.35.0":
  version: 3.35.0
  resolution: "sucrase@npm:3.35.0"
  dependencies:
    "@jridgewell/gen-mapping": ^0.3.2
    commander: ^4.0.0
    glob: ^10.3.10
    lines-and-columns: ^1.1.6
    mz: ^2.7.0
    pirates: ^4.0.1
    ts-interface-checker: ^0.1.9
  bin:
    sucrase: bin/sucrase
    sucrase-node: bin/sucrase-node
  checksum: 9fc5792a9ab8a14dcf9c47dcb704431d35c1cdff1d17d55d382a31c2e8e3063870ad32ce120a80915498486246d612e30cda44f1624d9d9a10423e1a43487ad1
  languageName: node
  linkType: hard

"supports-color@npm:^5.5.0":
  version: 5.5.0
  resolution: "supports-color@npm:5.5.0"
  dependencies:
    has-flag: ^3.0.0
  checksum: 95f6f4ba5afdf92f495b5a912d4abee8dcba766ae719b975c56c084f5004845f6f5a5f7769f52d53f40e21952a6d87411bafe34af4a01e65f9926002e38e1dac
  languageName: node
  linkType: hard

"supports-color@npm:^7.1.0":
  version: 7.2.0
  resolution: "supports-color@npm:7.2.0"
  dependencies:
    has-flag: ^4.0.0
  checksum: 3dda818de06ebbe5b9653e07842d9479f3555ebc77e9a0280caf5a14fb877ffee9ed57007c3b78f5a6324b8dbeec648d9e97a24e2ed9fdb81ddc69ea07100f4a
  languageName: node
  linkType: hard

"supports-preserve-symlinks-flag@npm:^1.0.0":
  version: 1.0.0
  resolution: "supports-preserve-symlinks-flag@npm:1.0.0"
  checksum: 53b1e247e68e05db7b3808b99b892bd36fb096e6fba213a06da7fab22045e97597db425c724f2bbd6c99a3c295e1e73f3e4de78592289f38431049e1277ca0ae
  languageName: node
  linkType: hard

"svg-tags@npm:^1.0.0":
  version: 1.0.0
  resolution: "svg-tags@npm:1.0.0"
  checksum: 407e5ef87cfa2fb81c61d738081c2decd022ce13b922d035b214b49810630bf5d1409255a4beb3a940b77b32f6957806deff16f1bf0ce1ab11c7a184115a0b7f
  languageName: node
  linkType: hard

"tailwindcss@npm:^3.4.1":
  version: 3.4.16
  resolution: "tailwindcss@npm:3.4.16"
  dependencies:
    "@alloc/quick-lru": ^5.2.0
    arg: ^5.0.2
    chokidar: ^3.6.0
    didyoumean: ^1.2.2
    dlv: ^1.1.3
    fast-glob: ^3.3.2
    glob-parent: ^6.0.2
    is-glob: ^4.0.3
    jiti: ^1.21.6
    lilconfig: ^3.1.3
    micromatch: ^4.0.8
    normalize-path: ^3.0.0
    object-hash: ^3.0.0
    picocolors: ^1.1.1
    postcss: ^8.4.47
    postcss-import: ^15.1.0
    postcss-js: ^4.0.1
    postcss-load-config: ^4.0.2
    postcss-nested: ^6.2.0
    postcss-selector-parser: ^6.1.2
    resolve: ^1.22.8
    sucrase: ^3.35.0
  bin:
    tailwind: lib/cli.js
    tailwindcss: lib/cli.js
  checksum: a6ec1ce07da6ea4d40a62d9b3babfc5e56da75c5efb3c6fe48317dbda6877949f011c67b4fd03cb9a680d3bd734f45dbc977ee9138f8ce59619c7c712fb1350f
  languageName: node
  linkType: hard

"tar@npm:^7.4.3":
  version: 7.4.3
  resolution: "tar@npm:7.4.3"
  dependencies:
    "@isaacs/fs-minipass": ^4.0.0
    chownr: ^3.0.0
    minipass: ^7.1.2
    minizlib: ^3.0.1
    mkdirp: ^3.0.1
    yallist: ^5.0.0
  checksum: 8485350c0688331c94493031f417df069b778aadb25598abdad51862e007c39d1dd5310702c7be4a6784731a174799d8885d2fde0484269aea205b724d7b2ffa
  languageName: node
  linkType: hard

"tdesign-icons-vue-next@npm:^0.2.4":
  version: 0.2.6
  resolution: "tdesign-icons-vue-next@npm:0.2.6"
  dependencies:
    "@babel/runtime": ^7.16.3
  peerDependencies:
    vue: ^3.0.0
  checksum: b8dda44cc7504ef7f74476b048e08ed9c72afeea9be382ccc5eb01aeaead93e803de4787c6cbbe2c78f97a0b100d4c9ca4f5963af8765529da0739c9a33d6901
  languageName: node
  linkType: hard

"tdesign-vue-next@npm:^1.10.1":
  version: 1.10.3
  resolution: "tdesign-vue-next@npm:1.10.3"
  dependencies:
    "@babel/runtime": ^7.22.6
    "@popperjs/core": ^2.11.8
    "@types/lodash": 4.14.182
    "@types/sortablejs": ^1.15.1
    "@types/tinycolor2": ^1.4.3
    "@types/validator": ^13.7.17
    dayjs: 1.11.10
    lodash: ^4.17.21
    mitt: ^3.0.1
    sortablejs: ^1.15.0
    tdesign-icons-vue-next: ^0.2.4
    tinycolor2: ^1.6.0
    validator: ^13.9.0
  peerDependencies:
    vue: ">=3.1.0"
  checksum: b16aa6ebf0ba2b804c630ff458c14fa71c3fef6aa16632bad51b98835f30364afcbf80323c5a02b1329768277599bea4b71f183bde4e773780550895d488449e
  languageName: node
  linkType: hard

"tencentcloud-sdk-nodejs@npm:^4.0.973":
  version: 4.0.991
  resolution: "tencentcloud-sdk-nodejs@npm:4.0.991"
  dependencies:
    form-data: ^3.0.0
    get-stream: ^6.0.0
    https-proxy-agent: ^5.0.0
    is-stream: ^2.0.0
    json-bigint: ^1.0.0
    node-fetch: ^2.2.0
    tslib: 1.13.0
    uuid: ^9.0.1
  checksum: 947a8d32b37d3376e94f0a2b01b420f71c847d0de4610e39ddd3bd3fdc40a7906c3b28e8d0659a40cd012a4b2d1ca1ca6aab87e8f0a6dec910a6a2f6620afcb3
  languageName: node
  linkType: hard

"text-hex@npm:1.0.x":
  version: 1.0.0
  resolution: "text-hex@npm:1.0.0"
  checksum: 1138f68adc97bf4381a302a24e2352f04992b7b1316c5003767e9b0d3367ffd0dc73d65001ea02b07cd0ecc2a9d186de0cf02f3c2d880b8a522d4ccb9342244a
  languageName: node
  linkType: hard

"text-table@npm:^0.2.0":
  version: 0.2.0
  resolution: "text-table@npm:0.2.0"
  checksum: b6937a38c80c7f84d9c11dd75e49d5c44f71d95e810a3250bd1f1797fc7117c57698204adf676b71497acc205d769d65c16ae8fa10afad832ae1322630aef10a
  languageName: node
  linkType: hard

"thenify-all@npm:^1.0.0":
  version: 1.6.0
  resolution: "thenify-all@npm:1.6.0"
  dependencies:
    thenify: ">= 3.1.0 < 4"
  checksum: dba7cc8a23a154cdcb6acb7f51d61511c37a6b077ec5ab5da6e8b874272015937788402fd271fdfc5f187f8cb0948e38d0a42dcc89d554d731652ab458f5343e
  languageName: node
  linkType: hard

"thenify@npm:>= 3.1.0 < 4":
  version: 3.3.1
  resolution: "thenify@npm:3.3.1"
  dependencies:
    any-promise: ^1.0.0
  checksum: 84e1b804bfec49f3531215f17b4a6e50fd4397b5f7c1bccc427b9c656e1ecfb13ea79d899930184f78bc2f57285c54d9a50a590c8868f4f0cef5c1d9f898b05e
  languageName: node
  linkType: hard

"tinycolor2@npm:^1.6.0":
  version: 1.6.0
  resolution: "tinycolor2@npm:1.6.0"
  checksum: 6df4d07fceeedc0a878d7bac47e2cd47c1ceeb1078340a9eb8a295bc0651e17c750f73d47b3028d829f30b85c15e0572c0fd4142083e4c21a30a597e47f47230
  languageName: node
  linkType: hard

"tmp@npm:^0.0.33":
  version: 0.0.33
  resolution: "tmp@npm:0.0.33"
  dependencies:
    os-tmpdir: ~1.0.2
  checksum: 902d7aceb74453ea02abbf58c203f4a8fc1cead89b60b31e354f74ed5b3fb09ea817f94fb310f884a5d16987dd9fa5a735412a7c2dd088dd3d415aa819ae3a28
  languageName: node
  linkType: hard

"to-readable-stream@npm:^1.0.0":
  version: 1.0.0
  resolution: "to-readable-stream@npm:1.0.0"
  checksum: 2bd7778490b6214a2c40276065dd88949f4cf7037ce3964c76838b8cb212893aeb9cceaaf4352a4c486e3336214c350270f3263e1ce7a0c38863a715a4d9aeb5
  languageName: node
  linkType: hard

"to-regex-range@npm:^5.0.1":
  version: 5.0.1
  resolution: "to-regex-range@npm:5.0.1"
  dependencies:
    is-number: ^7.0.0
  checksum: f76fa01b3d5be85db6a2a143e24df9f60dd047d151062d0ba3df62953f2f697b16fe5dad9b0ac6191c7efc7b1d9dcaa4b768174b7b29da89d4428e64bc0a20ed
  languageName: node
  linkType: hard

"toidentifier@npm:1.0.1":
  version: 1.0.1
  resolution: "toidentifier@npm:1.0.1"
  checksum: 952c29e2a85d7123239b5cfdd889a0dde47ab0497f0913d70588f19c53f7e0b5327c95f4651e413c74b785147f9637b17410ac8c846d5d4a20a5a33eb6dc3a45
  languageName: node
  linkType: hard

"touch@npm:^3.1.0":
  version: 3.1.1
  resolution: "touch@npm:3.1.1"
  bin:
    nodetouch: bin/nodetouch.js
  checksum: fb8c54207500eb760b6b9d77b9c5626cc027c9ad44431eed4268845f00f8c6bbfc95ce7e9da8e487f020aa921982a8bc5d8e909d0606e82686bd0a08a8e0539b
  languageName: node
  linkType: hard

"tr46@npm:~0.0.3":
  version: 0.0.3
  resolution: "tr46@npm:0.0.3"
  checksum: 726321c5eaf41b5002e17ffbd1fb7245999a073e8979085dacd47c4b4e8068ff5777142fc6726d6ca1fd2ff16921b48788b87225cbc57c72636f6efa8efbffe3
  languageName: node
  linkType: hard

"triple-beam@npm:^1.3.0":
  version: 1.4.1
  resolution: "triple-beam@npm:1.4.1"
  checksum: 2e881a3e8e076b6f2b85b9ec9dd4a900d3f5016e6d21183ed98e78f9abcc0149e7d54d79a3f432b23afde46b0885bdcdcbff789f39bc75de796316961ec07f61
  languageName: node
  linkType: hard

"ts-api-utils@npm:^1.3.0":
  version: 1.4.3
  resolution: "ts-api-utils@npm:1.4.3"
  peerDependencies:
    typescript: ">=4.2.0"
  checksum: ea00dee382d19066b2a3d8929f1089888b05fec797e32e7a7004938eda1dccf2e77274ee2afcd4166f53fab9b8d7ee90ebb225a3183f9ba8817d636f688a148d
  languageName: node
  linkType: hard

"ts-interface-checker@npm:^0.1.9":
  version: 0.1.13
  resolution: "ts-interface-checker@npm:0.1.13"
  checksum: 20c29189c2dd6067a8775e07823ddf8d59a33e2ffc47a1bd59a5cb28bb0121a2969a816d5e77eda2ed85b18171aa5d1c4005a6b88ae8499ec7cc49f78571cb5e
  languageName: node
  linkType: hard

"ts-node@npm:^10.9.2":
  version: 10.9.2
  resolution: "ts-node@npm:10.9.2"
  dependencies:
    "@cspotcode/source-map-support": ^0.8.0
    "@tsconfig/node10": ^1.0.7
    "@tsconfig/node12": ^1.0.7
    "@tsconfig/node14": ^1.0.0
    "@tsconfig/node16": ^1.0.2
    acorn: ^8.4.1
    acorn-walk: ^8.1.1
    arg: ^4.1.0
    create-require: ^1.1.0
    diff: ^4.0.1
    make-error: ^1.1.1
    v8-compile-cache-lib: ^3.0.1
    yn: 3.1.1
  peerDependencies:
    "@swc/core": ">=1.2.50"
    "@swc/wasm": ">=1.2.50"
    "@types/node": "*"
    typescript: ">=2.7"
  peerDependenciesMeta:
    "@swc/core":
      optional: true
    "@swc/wasm":
      optional: true
  bin:
    ts-node: dist/bin.js
    ts-node-cwd: dist/bin-cwd.js
    ts-node-esm: dist/bin-esm.js
    ts-node-script: dist/bin-script.js
    ts-node-transpile-only: dist/bin-transpile.js
    ts-script: dist/bin-script-deprecated.js
  checksum: fde256c9073969e234526e2cfead42591b9a2aec5222bac154b0de2fa9e4ceb30efcd717ee8bc785a56f3a119bdd5aa27b333d9dbec94ed254bd26f8944c67ac
  languageName: node
  linkType: hard

"tsconfig-paths@npm:^3.15.0":
  version: 3.15.0
  resolution: "tsconfig-paths@npm:3.15.0"
  dependencies:
    "@types/json5": ^0.0.29
    json5: ^1.0.2
    minimist: ^1.2.6
    strip-bom: ^3.0.0
  checksum: 59f35407a390d9482b320451f52a411a256a130ff0e7543d18c6f20afab29ac19fbe55c360a93d6476213cc335a4d76ce90f67df54c4e9037f7d240920832201
  languageName: node
  linkType: hard

"tslib@npm:1.13.0":
  version: 1.13.0
  resolution: "tslib@npm:1.13.0"
  checksum: 50e9327361f94f328c0715582a7f725f69838ab3c2559d143643c5367262fe14552768ba8cfc65bc7dc924a619aea599b3a28b6653458cdca77bbebaf9bc8df4
  languageName: node
  linkType: hard

"tsscmp@npm:1.0.6":
  version: 1.0.6
  resolution: "tsscmp@npm:1.0.6"
  checksum: 1512384def36bccc9125cabbd4c3b0e68608d7ee08127ceaa0b84a71797263f1a01c7f82fa69be8a3bd3c1396e2965d2f7b52d581d3a5eeaf3967fbc52e3b3bf
  languageName: node
  linkType: hard

"turbo-darwin-64@npm:2.3.0":
  version: 2.3.0
  resolution: "turbo-darwin-64@npm:2.3.0"
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"turbo-darwin-arm64@npm:2.3.0":
  version: 2.3.0
  resolution: "turbo-darwin-arm64@npm:2.3.0"
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"turbo-linux-64@npm:2.3.0":
  version: 2.3.0
  resolution: "turbo-linux-64@npm:2.3.0"
  conditions: os=linux & cpu=x64
  languageName: node
  linkType: hard

"turbo-linux-arm64@npm:2.3.0":
  version: 2.3.0
  resolution: "turbo-linux-arm64@npm:2.3.0"
  conditions: os=linux & cpu=arm64
  languageName: node
  linkType: hard

"turbo-windows-64@npm:2.3.0":
  version: 2.3.0
  resolution: "turbo-windows-64@npm:2.3.0"
  conditions: os=win32 & cpu=x64
  languageName: node
  linkType: hard

"turbo-windows-arm64@npm:2.3.0":
  version: 2.3.0
  resolution: "turbo-windows-arm64@npm:2.3.0"
  conditions: os=win32 & cpu=arm64
  languageName: node
  linkType: hard

"turbo@npm:^2.3.0":
  version: 2.3.0
  resolution: "turbo@npm:2.3.0"
  dependencies:
    turbo-darwin-64: 2.3.0
    turbo-darwin-arm64: 2.3.0
    turbo-linux-64: 2.3.0
    turbo-linux-arm64: 2.3.0
    turbo-windows-64: 2.3.0
    turbo-windows-arm64: 2.3.0
  dependenciesMeta:
    turbo-darwin-64:
      optional: true
    turbo-darwin-arm64:
      optional: true
    turbo-linux-64:
      optional: true
    turbo-linux-arm64:
      optional: true
    turbo-windows-64:
      optional: true
    turbo-windows-arm64:
      optional: true
  bin:
    turbo: bin/turbo
  checksum: 52b7e6a873ab92f4b43fc84d73211bac187e1812fb1c9e1d9cac22185d00386fa9057fd33eeed5c60e53f941686aeccb88e8d757c06d7c10d9284a9396e035d1
  languageName: node
  linkType: hard

"type-check@npm:^0.4.0, type-check@npm:~0.4.0":
  version: 0.4.0
  resolution: "type-check@npm:0.4.0"
  dependencies:
    prelude-ls: ^1.2.1
  checksum: ec688ebfc9c45d0c30412e41ca9c0cdbd704580eb3a9ccf07b9b576094d7b86a012baebc95681999dd38f4f444afd28504cb3a89f2ef16b31d4ab61a0739025a
  languageName: node
  linkType: hard

"type-fest@npm:^0.20.2":
  version: 0.20.2
  resolution: "type-fest@npm:0.20.2"
  checksum: 4fb3272df21ad1c552486f8a2f8e115c09a521ad7a8db3d56d53718d0c907b62c6e9141ba5f584af3f6830d0872c521357e512381f24f7c44acae583ad517d73
  languageName: node
  linkType: hard

"type-fest@npm:^0.21.3":
  version: 0.21.3
  resolution: "type-fest@npm:0.21.3"
  checksum: e6b32a3b3877f04339bae01c193b273c62ba7bfc9e325b8703c4ee1b32dc8fe4ef5dfa54bf78265e069f7667d058e360ae0f37be5af9f153b22382cd55a9afe0
  languageName: node
  linkType: hard

"type-is@npm:^1.6.16, type-is@npm:^1.6.18":
  version: 1.6.18
  resolution: "type-is@npm:1.6.18"
  dependencies:
    media-typer: 0.3.0
    mime-types: ~2.1.24
  checksum: 2c8e47675d55f8b4e404bcf529abdf5036c537a04c2b20177bcf78c9e3c1da69da3942b1346e6edb09e823228c0ee656ef0e033765ec39a70d496ef601a0c657
  languageName: node
  linkType: hard

"typed-array-buffer@npm:^1.0.2":
  version: 1.0.2
  resolution: "typed-array-buffer@npm:1.0.2"
  dependencies:
    call-bind: ^1.0.7
    es-errors: ^1.3.0
    is-typed-array: ^1.1.13
  checksum: 02ffc185d29c6df07968272b15d5319a1610817916ec8d4cd670ded5d1efe72901541ff2202fcc622730d8a549c76e198a2f74e312eabbfb712ed907d45cbb0b
  languageName: node
  linkType: hard

"typed-array-byte-length@npm:^1.0.1":
  version: 1.0.1
  resolution: "typed-array-byte-length@npm:1.0.1"
  dependencies:
    call-bind: ^1.0.7
    for-each: ^0.3.3
    gopd: ^1.0.1
    has-proto: ^1.0.3
    is-typed-array: ^1.1.13
  checksum: f65e5ecd1cf76b1a2d0d6f631f3ea3cdb5e08da106c6703ffe687d583e49954d570cc80434816d3746e18be889ffe53c58bf3e538081ea4077c26a41055b216d
  languageName: node
  linkType: hard

"typed-array-byte-offset@npm:^1.0.2":
  version: 1.0.3
  resolution: "typed-array-byte-offset@npm:1.0.3"
  dependencies:
    available-typed-arrays: ^1.0.7
    call-bind: ^1.0.7
    for-each: ^0.3.3
    gopd: ^1.0.1
    has-proto: ^1.0.3
    is-typed-array: ^1.1.13
    reflect.getprototypeof: ^1.0.6
  checksum: 36728daa80d49a9fa51cd3f0f2b037613f4574666fd4473bd37ac123d7f6f81ea68ff45424c1e2673257964e10bedeb3ebfce73532672913ebbe446999912303
  languageName: node
  linkType: hard

"typed-array-length@npm:^1.0.6":
  version: 1.0.7
  resolution: "typed-array-length@npm:1.0.7"
  dependencies:
    call-bind: ^1.0.7
    for-each: ^0.3.3
    gopd: ^1.0.1
    is-typed-array: ^1.1.13
    possible-typed-array-names: ^1.0.0
    reflect.getprototypeof: ^1.0.6
  checksum: deb1a4ffdb27cd930b02c7030cb3e8e0993084c643208e52696e18ea6dd3953dfc37b939df06ff78170423d353dc8b10d5bae5796f3711c1b3abe52872b3774c
  languageName: node
  linkType: hard

"typedarray-to-buffer@npm:^3.1.5":
  version: 3.1.5
  resolution: "typedarray-to-buffer@npm:3.1.5"
  dependencies:
    is-typedarray: ^1.0.0
  checksum: 99c11aaa8f45189fcfba6b8a4825fd684a321caa9bd7a76a27cf0c7732c174d198b99f449c52c3818107430b5f41c0ccbbfb75cb2ee3ca4a9451710986d61a60
  languageName: node
  linkType: hard

"typescript-eslint@npm:^7.0.2":
  version: 7.18.0
  resolution: "typescript-eslint@npm:7.18.0"
  dependencies:
    "@typescript-eslint/eslint-plugin": 7.18.0
    "@typescript-eslint/parser": 7.18.0
    "@typescript-eslint/utils": 7.18.0
  peerDependencies:
    eslint: ^8.56.0
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 68f263821c593d77cd607940a1a411edea6dcc528a0f5047be402c4a8cd612e8c7642b5c41ee6cb89c884ad83676658f7adb9ea688e550415938c84701d8ac93
  languageName: node
  linkType: hard

"typescript@npm:5.6.2":
  version: 5.6.2
  resolution: "typescript@npm:5.6.2"
  bin:
    tsc: bin/tsc
    tsserver: bin/tsserver
  checksum: 48777e1dabd9044519f56cd012b0296e3b72bafe12b7e8e34222751d45c67e0eba5387ecdaa6c14a53871a29361127798df6dc8d1d35643a0a47cb0b1c65a33a
  languageName: node
  linkType: hard

"typescript@npm:^5.3.3":
  version: 5.7.2
  resolution: "typescript@npm:5.7.2"
  bin:
    tsc: bin/tsc
    tsserver: bin/tsserver
  checksum: b55300c4cefee8ee380d14fa9359ccb41ff8b54c719f6bc49b424899d662a5ce62ece390ce769568c7f4d14af844085255e63788740084444eb12ef423b13433
  languageName: node
  linkType: hard

"typescript@patch:typescript@5.6.2#~builtin<compat/typescript>":
  version: 5.6.2
  resolution: "typescript@patch:typescript@npm%3A5.6.2#~builtin<compat/typescript>::version=5.6.2&hash=77c9e2"
  bin:
    tsc: bin/tsc
    tsserver: bin/tsserver
  checksum: c084ee1ab865f108c787e6233a5f63c126c482c0c8e87ec998ac5288a2ad54b603e1ea8b8b272355823b833eb31b9fabb99e8c6152283e1cb47e3a76bd6faf6c
  languageName: node
  linkType: hard

"typescript@patch:typescript@^5.3.3#~builtin<compat/typescript>":
  version: 5.7.2
  resolution: "typescript@patch:typescript@npm%3A5.7.2#~builtin<compat/typescript>::version=5.7.2&hash=77c9e2"
  bin:
    tsc: bin/tsc
    tsserver: bin/tsserver
  checksum: 803430c6da2ba73c25a21880d8d4f08a56d9d2444e6db2ea949ac4abceeece8e4a442b7b9b585db7d8a0b47ebda2060e45fe8ee8b8aca23e27ec1d4844987ee6
  languageName: node
  linkType: hard

"ufo@npm:^1.5.4":
  version: 1.5.4
  resolution: "ufo@npm:1.5.4"
  checksum: f244703b7d4f9f0df4f9af23921241ab73410b591f4e5b39c23e3147f3159b139a4b1fb5903189c306129f7a16b55995dac0008e0fbae88a37c3e58cbc34d833
  languageName: node
  linkType: hard

"unbox-primitive@npm:^1.0.2":
  version: 1.0.2
  resolution: "unbox-primitive@npm:1.0.2"
  dependencies:
    call-bind: ^1.0.2
    has-bigints: ^1.0.2
    has-symbols: ^1.0.3
    which-boxed-primitive: ^1.0.2
  checksum: b7a1cf5862b5e4b5deb091672ffa579aa274f648410009c81cca63fed3b62b610c4f3b773f912ce545bb4e31edc3138975b5bc777fc6e4817dca51affb6380e9
  languageName: node
  linkType: hard

"undefsafe@npm:^2.0.5":
  version: 2.0.5
  resolution: "undefsafe@npm:2.0.5"
  checksum: f42ab3b5770fedd4ada175fc1b2eb775b78f609156f7c389106aafd231bfc210813ee49f54483d7191d7b76e483bc7f537b5d92d19ded27156baf57592eb02cc
  languageName: node
  linkType: hard

"undici-types@npm:~5.26.4":
  version: 5.26.5
  resolution: "undici-types@npm:5.26.5"
  checksum: 3192ef6f3fd5df652f2dc1cd782b49d6ff14dc98e5dced492aa8a8c65425227da5da6aafe22523c67f035a272c599bb89cfe803c1db6311e44bed3042fc25487
  languageName: node
  linkType: hard

"undici-types@npm:~6.19.2":
  version: 6.19.8
  resolution: "undici-types@npm:6.19.8"
  checksum: de51f1b447d22571cf155dfe14ff6d12c5bdaec237c765085b439c38ca8518fc360e88c70f99469162bf2e14188a7b0bcb06e1ed2dc031042b984b0bb9544017
  languageName: node
  linkType: hard

"undici-types@npm:~6.20.0":
  version: 6.20.0
  resolution: "undici-types@npm:6.20.0"
  checksum: b7bc50f012dc6afbcce56c9fd62d7e86b20a62ff21f12b7b5cbf1973b9578d90f22a9c7fe50e638e96905d33893bf2f9f16d98929c4673c2480de05c6c96ea8b
  languageName: node
  linkType: hard

"undici@npm:^5.28.3":
  version: 5.28.4
  resolution: "undici@npm:5.28.4"
  dependencies:
    "@fastify/busboy": ^2.0.0
  checksum: a8193132d84540e4dc1895ecc8dbaa176e8a49d26084d6fbe48a292e28397cd19ec5d13bc13e604484e76f94f6e334b2bdc740d5f06a6e50c44072818d0c19f9
  languageName: node
  linkType: hard

"undici@npm:^6.19.5":
  version: 6.21.0
  resolution: "undici@npm:6.21.0"
  checksum: bc2eb26c4b010a4f816314d48d4529f62b1116405097b2c5f0ac68247c56049a857d11a9f05b237818f04ce4f51d6f5e8d6fcc6aae2ab816c2b7318a9706727c
  languageName: node
  linkType: hard

"unimport@npm:^3.7.2":
  version: 3.14.5
  resolution: "unimport@npm:3.14.5"
  dependencies:
    "@rollup/pluginutils": ^5.1.3
    acorn: ^8.14.0
    escape-string-regexp: ^5.0.0
    estree-walker: ^3.0.3
    fast-glob: ^3.3.2
    local-pkg: ^0.5.1
    magic-string: ^0.30.14
    mlly: ^1.7.3
    pathe: ^1.1.2
    picomatch: ^4.0.2
    pkg-types: ^1.2.1
    scule: ^1.3.0
    strip-literal: ^2.1.1
    unplugin: ^1.16.0
  checksum: c9a5e604d96768f4cbf38cb17fdbbbe5c932e1a1b317a0ac53c919d3362aa125c3b3c2ef1ca280f1c33fa9f25a5f7d0a153bf03944f0c1d78afc5c9e6e34fe6e
  languageName: node
  linkType: hard

"unique-filename@npm:^4.0.0":
  version: 4.0.0
  resolution: "unique-filename@npm:4.0.0"
  dependencies:
    unique-slug: ^5.0.0
  checksum: 6a62094fcac286b9ec39edbd1f8f64ff92383baa430af303dfed1ffda5e47a08a6b316408554abfddd9730c78b6106bef4ca4d02c1231a735ddd56ced77573df
  languageName: node
  linkType: hard

"unique-slug@npm:^5.0.0":
  version: 5.0.0
  resolution: "unique-slug@npm:5.0.0"
  dependencies:
    imurmurhash: ^0.1.4
  checksum: 222d0322bc7bbf6e45c08967863212398313ef73423f4125e075f893a02405a5ffdbaaf150f7dd1e99f8861348a486dd079186d27c5f2c60e465b7dcbb1d3e5b
  languageName: node
  linkType: hard

"unique-string@npm:^2.0.0":
  version: 2.0.0
  resolution: "unique-string@npm:2.0.0"
  dependencies:
    crypto-random-string: ^2.0.0
  checksum: ef68f639136bcfe040cf7e3cd7a8dff076a665288122855148a6f7134092e6ed33bf83a7f3a9185e46c98dddc445a0da6ac25612afa1a7c38b8b654d6c02498e
  languageName: node
  linkType: hard

"unpipe@npm:1.0.0":
  version: 1.0.0
  resolution: "unpipe@npm:1.0.0"
  checksum: 4fa18d8d8d977c55cb09715385c203197105e10a6d220087ec819f50cb68870f02942244f1017565484237f1f8c5d3cd413631b1ae104d3096f24fdfde1b4aa2
  languageName: node
  linkType: hard

"unplugin-auto-import@npm:^0.17.5":
  version: 0.17.8
  resolution: "unplugin-auto-import@npm:0.17.8"
  dependencies:
    "@antfu/utils": ^0.7.10
    "@rollup/pluginutils": ^5.1.0
    fast-glob: ^3.3.2
    local-pkg: ^0.5.0
    magic-string: ^0.30.10
    minimatch: ^9.0.4
    unimport: ^3.7.2
    unplugin: ^1.11.0
  peerDependencies:
    "@nuxt/kit": ^3.2.2
    "@vueuse/core": "*"
  peerDependenciesMeta:
    "@nuxt/kit":
      optional: true
    "@vueuse/core":
      optional: true
  checksum: 3c77d0fd0bcbbb939649c9fe1cba02098dab05e51e3a59d123ee48dd78ccec735ef58a37ddd1bd1d6878517a6c27d77bafcd8ce25df38e08d67e32d9ebec477a
  languageName: node
  linkType: hard

"unplugin-vue-components@npm:^0.26.0":
  version: 0.26.0
  resolution: "unplugin-vue-components@npm:0.26.0"
  dependencies:
    "@antfu/utils": ^0.7.6
    "@rollup/pluginutils": ^5.0.4
    chokidar: ^3.5.3
    debug: ^4.3.4
    fast-glob: ^3.3.1
    local-pkg: ^0.4.3
    magic-string: ^0.30.3
    minimatch: ^9.0.3
    resolve: ^1.22.4
    unplugin: ^1.4.0
  peerDependencies:
    "@babel/parser": ^7.15.8
    "@nuxt/kit": ^3.2.2
    vue: 2 || 3
  peerDependenciesMeta:
    "@babel/parser":
      optional: true
    "@nuxt/kit":
      optional: true
  checksum: 185281a23bdf277cb3545f23c49af491bc05b4eb75b3610ded5e2f72be47f23ddc72fb7cd891b1495e06e4c23503d55f039a4325793bb76cd619f24759265ac3
  languageName: node
  linkType: hard

"unplugin@npm:^1.11.0, unplugin@npm:^1.16.0, unplugin@npm:^1.4.0":
  version: 1.16.0
  resolution: "unplugin@npm:1.16.0"
  dependencies:
    acorn: ^8.14.0
    webpack-virtual-modules: ^0.6.2
  checksum: 84bff88dd8fd6ba88bd21dad1b170fea2a91f7ff8ddcfadf826297cf77dfe305f3428f1612c0637f30d7ac10d668491f15fdf8f378dd56def370fdbc16edd85e
  languageName: node
  linkType: hard

"update-browserslist-db@npm:^1.1.1":
  version: 1.1.1
  resolution: "update-browserslist-db@npm:1.1.1"
  dependencies:
    escalade: ^3.2.0
    picocolors: ^1.1.0
  peerDependencies:
    browserslist: ">= 4.21.0"
  bin:
    update-browserslist-db: cli.js
  checksum: 2ea11bd2562122162c3e438d83a1f9125238c0844b6d16d366e3276d0c0acac6036822dc7df65fc5a89c699cdf9f174acf439c39bedf3f9a2f3983976e4b4c3e
  languageName: node
  linkType: hard

"update-notifier@npm:^5.1.0":
  version: 5.1.0
  resolution: "update-notifier@npm:5.1.0"
  dependencies:
    boxen: ^5.0.0
    chalk: ^4.1.0
    configstore: ^5.0.1
    has-yarn: ^2.1.0
    import-lazy: ^2.1.0
    is-ci: ^2.0.0
    is-installed-globally: ^0.4.0
    is-npm: ^5.0.0
    is-yarn-global: ^0.3.0
    latest-version: ^5.1.0
    pupa: ^2.1.1
    semver: ^7.3.4
    semver-diff: ^3.1.1
    xdg-basedir: ^4.0.0
  checksum: 461e5e5b002419296d3868ee2abe0f9ab3e1846d9db642936d0c46f838872ec56069eddfe662c45ce1af0a8d6d5026353728de2e0a95ab2e3546a22ea077caf1
  languageName: node
  linkType: hard

"uri-js@npm:^4.2.2":
  version: 4.4.1
  resolution: "uri-js@npm:4.4.1"
  dependencies:
    punycode: ^2.1.0
  checksum: 7167432de6817fe8e9e0c9684f1d2de2bb688c94388f7569f7dbdb1587c9f4ca2a77962f134ec90be0cc4d004c939ff0d05acc9f34a0db39a3c797dada262633
  languageName: node
  linkType: hard

"url-parse-lax@npm:^3.0.0":
  version: 3.0.0
  resolution: "url-parse-lax@npm:3.0.0"
  dependencies:
    prepend-http: ^2.0.0
  checksum: 1040e357750451173132228036aff1fd04abbd43eac1fb3e4fca7495a078bcb8d33cb765fe71ad7e473d9c94d98fd67adca63bd2716c815a2da066198dd37217
  languageName: node
  linkType: hard

"utf-8-validate@npm:^6.0.3":
  version: 6.0.5
  resolution: "utf-8-validate@npm:6.0.5"
  dependencies:
    node-gyp: latest
    node-gyp-build: ^4.3.0
  checksum: ab6bd5b0db2241a990a9e1c434a29194900f9f8f715158b56d591306634cdd4f748e91b965d91a32fd459c86b082ec0a35d369bbc4efbb243b48f593c14448e8
  languageName: node
  linkType: hard

"util-deprecate@npm:^1.0.1, util-deprecate@npm:^1.0.2":
  version: 1.0.2
  resolution: "util-deprecate@npm:1.0.2"
  checksum: 474acf1146cb2701fe3b074892217553dfcf9a031280919ba1b8d651a068c9b15d863b7303cb15bd00a862b498e6cf4ad7b4a08fb134edd5a6f7641681cb54a2
  languageName: node
  linkType: hard

"uuid@npm:^9.0.1":
  version: 9.0.1
  resolution: "uuid@npm:9.0.1"
  bin:
    uuid: dist/bin/uuid
  checksum: 39931f6da74e307f51c0fb463dc2462807531dc80760a9bff1e35af4316131b4fc3203d16da60ae33f07fdca5b56f3f1dd662da0c99fea9aaeab2004780cc5f4
  languageName: node
  linkType: hard

"v8-compile-cache-lib@npm:^3.0.1":
  version: 3.0.1
  resolution: "v8-compile-cache-lib@npm:3.0.1"
  checksum: 78089ad549e21bcdbfca10c08850022b22024cdcc2da9b168bcf5a73a6ed7bf01a9cebb9eac28e03cd23a684d81e0502797e88f3ccd27a32aeab1cfc44c39da0
  languageName: node
  linkType: hard

"validator@npm:^13.9.0":
  version: 13.12.0
  resolution: "validator@npm:13.12.0"
  checksum: fb8f070724770b1449ea1a968605823fdb112dbd10507b2802f8841cda3e7b5c376c40f18c84e6a7b59de320a06177e471554101a85f1fa8a70bac1a84e48adf
  languageName: node
  linkType: hard

"vary@npm:^1.1.2":
  version: 1.1.2
  resolution: "vary@npm:1.1.2"
  checksum: ae0123222c6df65b437669d63dfa8c36cee20a504101b2fcd97b8bf76f91259c17f9f2b4d70a1e3c6bbcee7f51b28392833adb6b2770b23b01abec84e369660b
  languageName: node
  linkType: hard

"vite@npm:^5.1.4":
  version: 5.4.11
  resolution: "vite@npm:5.4.11"
  dependencies:
    esbuild: ^0.21.3
    fsevents: ~2.3.3
    postcss: ^8.4.43
    rollup: ^4.20.0
  peerDependencies:
    "@types/node": ^18.0.0 || >=20.0.0
    less: "*"
    lightningcss: ^1.21.0
    sass: "*"
    sass-embedded: "*"
    stylus: "*"
    sugarss: "*"
    terser: ^5.4.0
  dependenciesMeta:
    fsevents:
      optional: true
  peerDependenciesMeta:
    "@types/node":
      optional: true
    less:
      optional: true
    lightningcss:
      optional: true
    sass:
      optional: true
    sass-embedded:
      optional: true
    stylus:
      optional: true
    sugarss:
      optional: true
    terser:
      optional: true
  bin:
    vite: bin/vite.js
  checksum: 8c5b31d17487b69c40a30419dc0ade9f33360eb6893dbfa33a90980271bd74d35ae550b5cbb2a9e640f0df41ea36fd1bb4f222c98f6d02e607080f20832e69e8
  languageName: node
  linkType: hard

"vscode-uri@npm:^3.0.8":
  version: 3.0.8
  resolution: "vscode-uri@npm:3.0.8"
  checksum: 514249126850c0a41a7d8c3c2836cab35983b9dc1938b903cfa253b9e33974c1416d62a00111385adcfa2b98df456437ab704f709a2ecca76a90134ef5eb4832
  languageName: node
  linkType: hard

"vue-demi@npm:^0.14.10":
  version: 0.14.10
  resolution: "vue-demi@npm:0.14.10"
  peerDependencies:
    "@vue/composition-api": ^1.0.0-rc.1
    vue: ^3.0.0-0 || ^2.6.0
  peerDependenciesMeta:
    "@vue/composition-api":
      optional: true
  bin:
    vue-demi-fix: bin/vue-demi-fix.js
    vue-demi-switch: bin/vue-demi-switch.js
  checksum: 9b4106f99be3b0c1dd4a6dc5725f5e8f79c6b98d1eeb849bf2c54416cd77f4aa344960b202768865245cfa82d57f49a9d96f67f5d8e256604b9dac1c5df9a8d6
  languageName: node
  linkType: hard

"vue-eslint-parser@npm:^9.4.3":
  version: 9.4.3
  resolution: "vue-eslint-parser@npm:9.4.3"
  dependencies:
    debug: ^4.3.4
    eslint-scope: ^7.1.1
    eslint-visitor-keys: ^3.3.0
    espree: ^9.3.1
    esquery: ^1.4.0
    lodash: ^4.17.21
    semver: ^7.3.6
  peerDependencies:
    eslint: ">=6.0.0"
  checksum: 8d5b7ef7c5ee264ca2ba78da4b95ac7a66175a458d153a35e92cd7c55b794db0f2c31a8fdd40021bab4496f2f64ab80d7dbb6dccff4103beb4564c439a88fa42
  languageName: node
  linkType: hard

"vue-i18n@npm:^9.12.1":
  version: 9.14.2
  resolution: "vue-i18n@npm:9.14.2"
  dependencies:
    "@intlify/core-base": 9.14.2
    "@intlify/shared": 9.14.2
    "@vue/devtools-api": ^6.5.0
  peerDependencies:
    vue: ^3.0.0
  checksum: d387ed57f1b3a6c7c730d6737d90d0932a036d8ad5016476f59474e7c978e9a8eef0c790b2ea402fcf71696a3c0589030d8ca76b7251b055943bd051b06f7f56
  languageName: node
  linkType: hard

"vue-router@npm:^4.3.0":
  version: 4.5.0
  resolution: "vue-router@npm:4.5.0"
  dependencies:
    "@vue/devtools-api": ^6.6.4
  peerDependencies:
    vue: ^3.2.0
  checksum: 2604db5a6b33c5079e3ba1a3aabf5693b4a112899b7cb2c44521961cf60677b48efdf87bb76a284ce938d70ca39e5e46e2b5dbe59f684ca519526bc6cd91efaa
  languageName: node
  linkType: hard

"vue-tsc@npm:^2.1.10":
  version: 2.1.10
  resolution: "vue-tsc@npm:2.1.10"
  dependencies:
    "@volar/typescript": ~2.4.8
    "@vue/language-core": 2.1.10
    semver: ^7.5.4
  peerDependencies:
    typescript: ">=5.0.0"
  bin:
    vue-tsc: ./bin/vue-tsc.js
  checksum: a64ad7b4388a1e2679cc0a53b6ecd7e26e5fda131ada06d7f02040c650616bdf93532482224f0e719a912b4d3222ec64997171f4721a29cf734b964141629691
  languageName: node
  linkType: hard

"vue@npm:^3.4.19":
  version: 3.5.13
  resolution: "vue@npm:3.5.13"
  dependencies:
    "@vue/compiler-dom": 3.5.13
    "@vue/compiler-sfc": 3.5.13
    "@vue/runtime-dom": 3.5.13
    "@vue/server-renderer": 3.5.13
    "@vue/shared": 3.5.13
  peerDependencies:
    typescript: "*"
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 3d435ffaae2736234bbcf30cd14e88631c07ebb9447d2b4815d9dc9a1254b8d9368048a453c7fe29d2d87194052e1fef85b12c1e72ea64bda3ec816a19c22560
  languageName: node
  linkType: hard

"wcwidth@npm:^1.0.1":
  version: 1.0.1
  resolution: "wcwidth@npm:1.0.1"
  dependencies:
    defaults: ^1.0.3
  checksum: 814e9d1ddcc9798f7377ffa448a5a3892232b9275ebb30a41b529607691c0491de47cba426e917a4d08ded3ee7e9ba2f3fe32e62ee3cd9c7d3bafb7754bd553c
  languageName: node
  linkType: hard

"web-streams-polyfill@npm:4.0.0-beta.3":
  version: 4.0.0-beta.3
  resolution: "web-streams-polyfill@npm:4.0.0-beta.3"
  checksum: dfec1fbf52b9140e4183a941e380487b6c3d5d3838dd1259be81506c1c9f2abfcf5aeb670aeeecfd9dff4271a6d8fef931b193c7bedfb42542a3b05ff36c0d16
  languageName: node
  linkType: hard

"web@workspace:apps/web":
  version: 0.0.0-use.local
  resolution: "web@workspace:apps/web"
  dependencies:
    "@remixicon/vue": ^4.1.0
    "@typescript-eslint/eslint-plugin": ^7.0.2
    "@typescript-eslint/parser": ^7.0.2
    "@vitejs/plugin-vue": ^5.0.4
    "@vitejs/plugin-vue-jsx": ^3.1.0
    autoprefixer: ^10.4.17
    cheerio: ^1.0.0
    eslint: ^8.57.0
    eslint-config-prettier: ^9.1.0
    eslint-plugin-tailwindcss: ^3.14.3
    eslint-plugin-vue: ^9.22.0
    fetch-sse: 1.0.23
    github-markdown-css: ^5.6.1
    marked: ^14.1.2
    pinia: ^2.2.2
    pinia-plugin-persistedstate: ^3.2.1
    postcss: ^8.4.35
    prettier: ^3.2.5
    prettier-plugin-tailwindcss: ^0.5.11
    tailwindcss: ^3.4.1
    tdesign-vue-next: ^1.10.1
    tencentcloud-sdk-nodejs: ^4.0.973
    typescript: 5.6.2
    unplugin-auto-import: ^0.17.5
    unplugin-vue-components: ^0.26.0
    vite: ^5.1.4
    vue: ^3.4.19
    vue-i18n: ^9.12.1
    vue-router: ^4.3.0
    vue-tsc: ^2.1.10
  languageName: unknown
  linkType: soft

"webidl-conversions@npm:^3.0.0":
  version: 3.0.1
  resolution: "webidl-conversions@npm:3.0.1"
  checksum: c92a0a6ab95314bde9c32e1d0a6dfac83b578f8fa5f21e675bc2706ed6981bc26b7eb7e6a1fab158e5ce4adf9caa4a0aee49a52505d4d13c7be545f15021b17c
  languageName: node
  linkType: hard

"webpack-virtual-modules@npm:^0.6.2":
  version: 0.6.2
  resolution: "webpack-virtual-modules@npm:0.6.2"
  checksum: 7e8e1d63f35864c815420cc2f27da8561a1e028255040698a352717de0ba46d3b3faf16f06c1a1965217054c4c2894eb9af53a85451870e919b5707ce9c5822d
  languageName: node
  linkType: hard

"whatwg-encoding@npm:^3.1.1":
  version: 3.1.1
  resolution: "whatwg-encoding@npm:3.1.1"
  dependencies:
    iconv-lite: 0.6.3
  checksum: f75a61422421d991e4aec775645705beaf99a16a88294d68404866f65e92441698a4f5b9fa11dd609017b132d7b286c3c1534e2de5b3e800333856325b549e3c
  languageName: node
  linkType: hard

"whatwg-fetch@npm:^3.6.20":
  version: 3.6.20
  resolution: "whatwg-fetch@npm:3.6.20"
  checksum: c58851ea2c4efe5c2235f13450f426824cf0253c1d45da28f45900290ae602a20aff2ab43346f16ec58917d5562e159cd691efa368354b2e82918c2146a519c5
  languageName: node
  linkType: hard

"whatwg-mimetype@npm:^4.0.0":
  version: 4.0.0
  resolution: "whatwg-mimetype@npm:4.0.0"
  checksum: f97edd4b4ee7e46a379f3fb0e745de29fe8b839307cc774300fd49059fcdd560d38cb8fe21eae5575b8f39b022f23477cc66e40b0355c2851ce84760339cef30
  languageName: node
  linkType: hard

"whatwg-url@npm:^5.0.0":
  version: 5.0.0
  resolution: "whatwg-url@npm:5.0.0"
  dependencies:
    tr46: ~0.0.3
    webidl-conversions: ^3.0.0
  checksum: b8daed4ad3356cc4899048a15b2c143a9aed0dfae1f611ebd55073310c7b910f522ad75d727346ad64203d7e6c79ef25eafd465f4d12775ca44b90fa82ed9e2c
  languageName: node
  linkType: hard

"which-boxed-primitive@npm:^1.0.2, which-boxed-primitive@npm:^1.1.0":
  version: 1.1.0
  resolution: "which-boxed-primitive@npm:1.1.0"
  dependencies:
    is-bigint: ^1.1.0
    is-boolean-object: ^1.2.0
    is-number-object: ^1.1.0
    is-string: ^1.1.0
    is-symbol: ^1.1.0
  checksum: 49ebec9693ed21ee8183b9e353ee7134a03722776c84624019964124885a4a940f469af3d1508ad83022a68cc515aecbef70fb1256ace57a871c43d24d050304
  languageName: node
  linkType: hard

"which-builtin-type@npm:^1.2.0":
  version: 1.2.1
  resolution: "which-builtin-type@npm:1.2.1"
  dependencies:
    call-bound: ^1.0.2
    function.prototype.name: ^1.1.6
    has-tostringtag: ^1.0.2
    is-async-function: ^2.0.0
    is-date-object: ^1.1.0
    is-finalizationregistry: ^1.1.0
    is-generator-function: ^1.0.10
    is-regex: ^1.2.1
    is-weakref: ^1.0.2
    isarray: ^2.0.5
    which-boxed-primitive: ^1.1.0
    which-collection: ^1.0.2
    which-typed-array: ^1.1.16
  checksum: 7a3617ba0e7cafb795f74db418df889867d12bce39a477f3ee29c6092aa64d396955bf2a64eae3726d8578440e26777695544057b373c45a8bcf5fbe920bf633
  languageName: node
  linkType: hard

"which-collection@npm:^1.0.2":
  version: 1.0.2
  resolution: "which-collection@npm:1.0.2"
  dependencies:
    is-map: ^2.0.3
    is-set: ^2.0.3
    is-weakmap: ^2.0.2
    is-weakset: ^2.0.3
  checksum: c51821a331624c8197916598a738fc5aeb9a857f1e00d89f5e4c03dc7c60b4032822b8ec5696d28268bb83326456a8b8216344fb84270d18ff1d7628051879d9
  languageName: node
  linkType: hard

"which-typed-array@npm:^1.1.14, which-typed-array@npm:^1.1.15, which-typed-array@npm:^1.1.16":
  version: 1.1.16
  resolution: "which-typed-array@npm:1.1.16"
  dependencies:
    available-typed-arrays: ^1.0.7
    call-bind: ^1.0.7
    for-each: ^0.3.3
    gopd: ^1.0.1
    has-tostringtag: ^1.0.2
  checksum: 903d398ec234d608011e1df09af6c004e66965bb24d5e1a82856cba0495fa6389ae393d1c9d5411498a9cba8e61b2e39a8e8be7b3005cbeadd317f772b1bdaef
  languageName: node
  linkType: hard

"which@npm:^1.2.9":
  version: 1.3.1
  resolution: "which@npm:1.3.1"
  dependencies:
    isexe: ^2.0.0
  bin:
    which: ./bin/which
  checksum: f2e185c6242244b8426c9df1510e86629192d93c1a986a7d2a591f2c24869e7ffd03d6dac07ca863b2e4c06f59a4cc9916c585b72ee9fa1aa609d0124df15e04
  languageName: node
  linkType: hard

"which@npm:^2.0.1":
  version: 2.0.2
  resolution: "which@npm:2.0.2"
  dependencies:
    isexe: ^2.0.0
  bin:
    node-which: ./bin/node-which
  checksum: 1a5c563d3c1b52d5f893c8b61afe11abc3bab4afac492e8da5bde69d550de701cf9806235f20a47b5c8fa8a1d6a9135841de2596535e998027a54589000e66d1
  languageName: node
  linkType: hard

"which@npm:^4.0.0":
  version: 4.0.0
  resolution: "which@npm:4.0.0"
  dependencies:
    isexe: ^3.1.1
  bin:
    node-which: bin/which.js
  checksum: f17e84c042592c21e23c8195108cff18c64050b9efb8459589116999ea9da6dd1509e6a1bac3aeebefd137be00fabbb61b5c2bc0aa0f8526f32b58ee2f545651
  languageName: node
  linkType: hard

"which@npm:^5.0.0":
  version: 5.0.0
  resolution: "which@npm:5.0.0"
  dependencies:
    isexe: ^3.1.1
  bin:
    node-which: bin/which.js
  checksum: 6ec99e89ba32c7e748b8a3144e64bfc74aa63e2b2eacbb61a0060ad0b961eb1a632b08fb1de067ed59b002cec3e21de18299216ebf2325ef0f78e0f121e14e90
  languageName: node
  linkType: hard

"widest-line@npm:^3.1.0":
  version: 3.1.0
  resolution: "widest-line@npm:3.1.0"
  dependencies:
    string-width: ^4.0.0
  checksum: 03db6c9d0af9329c37d74378ff1d91972b12553c7d72a6f4e8525fe61563fa7adb0b9d6e8d546b7e059688712ea874edd5ded475999abdeedf708de9849310e0
  languageName: node
  linkType: hard

"winston-transport@npm:^4.9.0":
  version: 4.9.0
  resolution: "winston-transport@npm:4.9.0"
  dependencies:
    logform: ^2.7.0
    readable-stream: ^3.6.2
    triple-beam: ^1.3.0
  checksum: f5fd06a27def7597229925ba2b8b9ffa61b5b8748f994c8325064744e4e36dfea19868a16c16b3806f9b98bb7da67c25f08ae6fba3bdc6db4a9555673474a972
  languageName: node
  linkType: hard

"winston@npm:^3.11.0, winston@npm:^3.15.0":
  version: 3.17.0
  resolution: "winston@npm:3.17.0"
  dependencies:
    "@colors/colors": ^1.6.0
    "@dabh/diagnostics": ^2.0.2
    async: ^3.2.3
    is-stream: ^2.0.0
    logform: ^2.7.0
    one-time: ^1.0.0
    readable-stream: ^3.4.0
    safe-stable-stringify: ^2.3.1
    stack-trace: 0.0.x
    triple-beam: ^1.3.0
    winston-transport: ^4.9.0
  checksum: ba772c25937007cea6cdeddc931de18a1ea336ae7b3aff2c15de762de5c559b2d310ca2e7a911c209711d325e47d653485e33271ddfb27cd73179e35c7d52267
  languageName: node
  linkType: hard

"word-wrap@npm:^1.2.5":
  version: 1.2.5
  resolution: "word-wrap@npm:1.2.5"
  checksum: f93ba3586fc181f94afdaff3a6fef27920b4b6d9eaefed0f428f8e07adea2a7f54a5f2830ce59406c8416f033f86902b91eb824072354645eea687dff3691ccb
  languageName: node
  linkType: hard

"wrap-ansi-cjs@npm:wrap-ansi@^7.0.0, wrap-ansi@npm:^7.0.0":
  version: 7.0.0
  resolution: "wrap-ansi@npm:7.0.0"
  dependencies:
    ansi-styles: ^4.0.0
    string-width: ^4.1.0
    strip-ansi: ^6.0.0
  checksum: a790b846fd4505de962ba728a21aaeda189b8ee1c7568ca5e817d85930e06ef8d1689d49dbf0e881e8ef84436af3a88bc49115c2e2788d841ff1b8b5b51a608b
  languageName: node
  linkType: hard

"wrap-ansi@npm:^6.2.0":
  version: 6.2.0
  resolution: "wrap-ansi@npm:6.2.0"
  dependencies:
    ansi-styles: ^4.0.0
    string-width: ^4.1.0
    strip-ansi: ^6.0.0
  checksum: 6cd96a410161ff617b63581a08376f0cb9162375adeb7956e10c8cd397821f7eb2a6de24eb22a0b28401300bf228c86e50617cd568209b5f6775b93c97d2fe3a
  languageName: node
  linkType: hard

"wrap-ansi@npm:^8.1.0":
  version: 8.1.0
  resolution: "wrap-ansi@npm:8.1.0"
  dependencies:
    ansi-styles: ^6.1.0
    string-width: ^5.0.1
    strip-ansi: ^7.0.1
  checksum: 371733296dc2d616900ce15a0049dca0ef67597d6394c57347ba334393599e800bab03c41d4d45221b6bc967b8c453ec3ae4749eff3894202d16800fdfe0e238
  languageName: node
  linkType: hard

"wrappy@npm:1":
  version: 1.0.2
  resolution: "wrappy@npm:1.0.2"
  checksum: 159da4805f7e84a3d003d8841557196034155008f817172d4e986bd591f74aa82aa7db55929a54222309e01079a65a92a9e6414da5a6aa4b01ee44a511ac3ee5
  languageName: node
  linkType: hard

"write-file-atomic@npm:^3.0.0":
  version: 3.0.3
  resolution: "write-file-atomic@npm:3.0.3"
  dependencies:
    imurmurhash: ^0.1.4
    is-typedarray: ^1.0.0
    signal-exit: ^3.0.2
    typedarray-to-buffer: ^3.1.5
  checksum: c55b24617cc61c3a4379f425fc62a386cc51916a9b9d993f39734d005a09d5a4bb748bc251f1304e7abd71d0a26d339996c275955f527a131b1dcded67878280
  languageName: node
  linkType: hard

"ws@npm:^8.16.0":
  version: 8.18.0
  resolution: "ws@npm:8.18.0"
  peerDependencies:
    bufferutil: ^4.0.1
    utf-8-validate: ">=5.0.2"
  peerDependenciesMeta:
    bufferutil:
      optional: true
    utf-8-validate:
      optional: true
  checksum: 91d4d35bc99ff6df483bdf029b9ea4bfd7af1f16fc91231a96777a63d263e1eabf486e13a2353970efc534f9faa43bdbf9ee76525af22f4752cbc5ebda333975
  languageName: node
  linkType: hard

"xdg-basedir@npm:^4.0.0":
  version: 4.0.0
  resolution: "xdg-basedir@npm:4.0.0"
  checksum: 0073d5b59a37224ed3a5ac0dd2ec1d36f09c49f0afd769008a6e9cd3cd666bd6317bd1c7ce2eab47e1de285a286bad11a9b038196413cd753b79770361855f3c
  languageName: node
  linkType: hard

"xml-name-validator@npm:^4.0.0":
  version: 4.0.0
  resolution: "xml-name-validator@npm:4.0.0"
  checksum: af100b79c29804f05fa35aa3683e29a321db9b9685d5e5febda3fa1e40f13f85abc40f45a6b2bf7bee33f68a1dc5e8eaef4cec100a304a9db565e6061d4cb5ad
  languageName: node
  linkType: hard

"xxhashjs@npm:^0.2.2":
  version: 0.2.2
  resolution: "xxhashjs@npm:0.2.2"
  dependencies:
    cuint: ^0.2.2
  checksum: cf6baf05bafe5651dbf108008bafdb1ebe972f65228633f00b56c49d7a1e614a821fe3345c4eb27462994c7c954d982eae05871be6a48146f30803dd87f3c3b6
  languageName: node
  linkType: hard

"yallist@npm:^3.0.2":
  version: 3.1.1
  resolution: "yallist@npm:3.1.1"
  checksum: 48f7bb00dc19fc635a13a39fe547f527b10c9290e7b3e836b9a8f1ca04d4d342e85714416b3c2ab74949c9c66f9cebb0473e6bc353b79035356103b47641285d
  languageName: node
  linkType: hard

"yallist@npm:^4.0.0":
  version: 4.0.0
  resolution: "yallist@npm:4.0.0"
  checksum: 343617202af32df2a15a3be36a5a8c0c8545208f3d3dfbc6bb7c3e3b7e8c6f8e7485432e4f3b88da3031a6e20afa7c711eded32ddfb122896ac5d914e75848d5
  languageName: node
  linkType: hard

"yallist@npm:^5.0.0":
  version: 5.0.0
  resolution: "yallist@npm:5.0.0"
  checksum: eba51182400b9f35b017daa7f419f434424410691bbc5de4f4240cc830fdef906b504424992700dc047f16b4d99100a6f8b8b11175c193f38008e9c96322b6a5
  languageName: node
  linkType: hard

"yaml@npm:^2.3.4":
  version: 2.6.1
  resolution: "yaml@npm:2.6.1"
  bin:
    yaml: bin.mjs
  checksum: 5cf2627f121dcf04ccdebce8e6cbac7c9983d465c4eab314f6fbdc13cda8a07f4e8f9c2252a382b30bcabe05ee3c683647293afd52eb37cbcefbdc7b6ebde9ee
  languageName: node
  linkType: hard

"ylru@npm:^1.2.0":
  version: 1.4.0
  resolution: "ylru@npm:1.4.0"
  checksum: e0bf797476487e3d57a6e8790cbb749cff2089e2afc87e46bc84ce7605c329d578ff422c8e8c2ddf167681ddd218af0f58e099733ae1044cba9e9472ebedc01d
  languageName: node
  linkType: hard

"yn@npm:3.1.1":
  version: 3.1.1
  resolution: "yn@npm:3.1.1"
  checksum: 2c487b0e149e746ef48cda9f8bad10fc83693cd69d7f9dcd8be4214e985de33a29c9e24f3c0d6bcf2288427040a8947406ab27f7af67ee9456e6b84854f02dd6
  languageName: node
  linkType: hard

"yocto-queue@npm:^0.1.0":
  version: 0.1.0
  resolution: "yocto-queue@npm:0.1.0"
  checksum: f77b3d8d00310def622123df93d4ee654fc6a0096182af8bd60679ddcdfb3474c56c6c7190817c84a2785648cdee9d721c0154eb45698c62176c322fb46fc700
  languageName: node
  linkType: hard
