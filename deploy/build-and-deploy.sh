#!/bin/bash

# AI Search 本地构建部署脚本
# 使用本地代码构建Docker镜像，避免在线镜像版本不兼容问题

set -e

echo "🚀 开始 AI Search 本地构建部署..."

# 检查是否在正确的目录
if [ ! -f "../package.json" ]; then
    echo "❌ 错误: 请在 deploy 目录下运行此脚本"
    exit 1
fi

# 检查Docker是否运行
if ! docker info > /dev/null 2>&1; then
    echo "❌ 错误: Docker 未运行，请先启动 Docker"
    exit 1
fi

# 停止现有容器
echo "🛑 停止现有容器..."
docker compose down

# 清理旧镜像（可选）
read -p "是否清理旧的本地构建镜像? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🧹 清理旧镜像..."
    docker compose down
    docker rmi aisearch-local 2>/dev/null || true
fi

# 构建统一镜像
echo "🔨 构建统一镜像..."
docker compose build aisearch-openai

# 启动所有服务
echo "🚀 启动所有服务..."
docker compose up -d

# 检查服务状态
echo "⏳ 等待服务启动..."
sleep 10

echo "📊 检查服务状态..."
docker compose ps

echo ""
echo "✅ 部署完成!"
echo ""
echo "🌐 访问地址:"
echo "  - OpenAI 服务:     http://localhost:3001"
echo "  - SiliconFlow 服务: http://localhost:3002 (internlm)"
echo "  - SiliconFlow 服务: http://localhost:3003 (Qwen)"
echo "  - Claude 服务:     http://localhost:3004"
echo ""
echo "📝 查看日志: docker compose logs -f [service-name]"
echo "🛑 停止服务: docker compose down"
