version: "3.7"

services:
  aisearch-openai:
    container_name: aisearch-openai
    build:
      context: ..
      dockerfile: .devops/Dockerfile
    volumes:
      - ./.env.docker-openai:/app/.env
      - ./model-openai.json:/app/dist/model.json
    ports:
      - "3001:3000"
    restart: always

  aisearch-siliconflow-internlm:
    container_name: aisearch-siliconflow-internlm
    build:
      context: ..
      dockerfile: .devops/Dockerfile
    volumes:
      - ./.env.docker-siliconflow:/app/.env
      - ./model-siliconflow-internlm.json:/app/dist/model.json
    ports:
      - "3002:3000"
    restart: always

  aisearch-siliconflow-Qwen:
    container_name: aisearch-siliconflow-Qwen
    build:
      context: ..
      dockerfile: .devops/Dockerfile
    volumes:
      - ./.env.docker-siliconflow:/app/.env
      - ./model-siliconflow-Qwen.json:/app/dist/model.json
    ports:
      - "3003:3000"
    restart: always

  aisearch-claude:
    container_name: aisearch-claude
    build:
      context: ..
      dockerfile: .devops/Dockerfile
    volumes:
      - ./.env.docker-openai:/app/.env
      - ./model-claude.json:/app/dist/model.json
    ports:
      - "3004:3000"
    restart: always

  searxng:
    container_name: searxng-main
    image: docker.io/searxng/searxng:latest
    restart: always
    # ports:
    #   - "127.0.0.1:8080:8080"
    volumes:
      - ./searxng:/etc/searxng:rw
    environment:
      - SEARXNG_BASE_URL=https://${SEARXNG_HOSTNAME:-localhost}/
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8080/healthz"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
