# AI Search 本地构建部署指南

## 问题背景

原来的部署方式使用在线Docker镜像 `docker.cnb.cool/aigc/aisearch`，然后通过volume挂载本地编译文件。这种方式存在以下问题：

1. **版本不匹配**: 在线镜像版本可能与本地代码版本不兼容
2. **依赖冲突**: 在线镜像的依赖版本可能与本地代码需要的版本不匹配
3. **维护困难**: 无法控制在线镜像的更新时机

## 解决方案

改为使用本地代码构建Docker镜像，确保代码和运行环境完全匹配。

## 文件说明

- `docker-compose.yaml`: 使用本地构建的配置（**推荐使用**）
- `docker-compose-online-image.yaml`: 原来使用在线镜像的配置（备份）
- `build-and-deploy.sh`: 自动化构建部署脚本
- `.devops/Dockerfile`: 优化后的Dockerfile

## 快速开始

### 方法一：使用自动化脚本（推荐）

```bash
cd deploy
./build-and-deploy.sh
```

### 方法二：手动执行

```bash
cd deploy

# 停止现有服务
docker compose down

# 构建并启动服务
docker compose up -d --build

# 查看服务状态
docker compose ps
```

## 构建过程说明

1. **构建阶段**: 使用 `node:20` 镜像构建前后端代码
   - 安装所有依赖
   - 使用 turbo 构建所有应用
   
2. **生产阶段**: 使用 `node:20-alpine` 镜像运行服务
   - 只复制构建产物和必要文件
   - 安装生产依赖
   - 启动服务

## 优势

✅ **版本一致性**: 本地代码和运行环境完全匹配  
✅ **依赖兼容性**: 使用本地代码的确切依赖版本  
✅ **可控性**: 完全控制构建和部署过程  
✅ **安全性**: 不依赖外部镜像更新  
✅ **调试友好**: 可以轻松修改代码并重新构建  

## 注意事项

1. **首次构建时间较长**: 需要下载依赖和构建代码，请耐心等待
2. **磁盘空间**: 本地构建会占用更多磁盘空间
3. **网络要求**: 构建过程需要下载npm包，确保网络连接正常

## 常用命令

```bash
# 查看服务日志
docker compose logs -f

# 查看特定服务日志
docker compose logs -f aisearch-openai

# 重新构建特定服务
docker compose up -d --build aisearch-openai

# 停止所有服务
docker compose down

# 停止服务并删除镜像
docker compose down --rmi local
```

## 故障排除

### 构建失败
- 检查网络连接
- 清理Docker缓存: `docker system prune -a`
- 检查磁盘空间

### 服务启动失败
- 检查环境变量配置
- 查看服务日志: `docker compose logs [service-name]`
- 确认端口未被占用

### 回退到在线镜像
如果需要回退到原来的在线镜像方式：

```bash
cp docker-compose-online-image.yaml docker-compose.yaml
docker compose up -d
```
