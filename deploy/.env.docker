# Server side configuration
PORT=3000  # The port number your server will run on inside the Docker container (default is 3000)

# Search Engine API Keys
BING_SEARCH_KEY=  # Your Bing Web Search API key (required for using Bing search in Docker)
GOOGLE_SEARCH_KEY=  # Your Google Search API key (for Google Search in Docker)
GOOGLE_SEARCH_ID=  # Your Google Custom Search Engine (CSE) ID

# AliYun API Key
ALIYUN_KEY=  # Your AliYun API key (if using AliYun services in Docker)

# Yi Search Engine Key
YI_KEY=  # Your Yi search engine API key (if applicable)

# Google Gemini API Configuration
GOOGLE_KEY=  # Your Google Gemini API key (for Google AI services)
GOOGLE_PROXY_URL=  # Proxy URL for Google services (if applicable)

# Baidu API Keys
BAIDU_KEY=  # Your Baidu API key (if using Baidu services)
BAIDU_SECRET=  # Your Baidu API secret key

# Tencent API Keys
TENCENT_KEY=  # Your Tencent API key (if using Tencent services)
TENCENT_SECRET=  # Your Tencent API secret key

# OpenAI API Configuration
OPENAI_KEY=  # Your OpenAI API key (required for using OpenAI models like ChatGPT)
OPENAI_PROXY_URL=  # OpenAI proxy URL (optional, if using a proxy for OpenAI requests)

# DeepSeek API Key
DEEPSEEK_KEY=  # Your DeepSeek API key (for accessing DeepSeek models)

# ChatGLM Key
GLM_KEY=  # Your ChatGLM API key (used for Chinese language models)

# Moonshot API Key
MOONSHOT_KEY=  # Your Moonshot API key (if applicable)

# Lepton API Key
LEPTON_KEY=  # Your Lepton API key (for using Lepton models)

# JINA API KEY
JINA_KEY=  # Your Jina API key

# Local LLM Configuration (Ollama)
OLLAMA_HOST=http://host.docker.internal:11434  # Ollama hostname (for running Ollama LLM locally in Docker)

# Local LLM Configuration (LM Studio)
LMSTUDIO_HOSTNAME=localhost:1234  # LM Studio hostname (for running LM Studio LLM locally in Docker)

# SearXNG Metasearch Engine Configuration
SEARXNG_HOSTNAME=http://searxng:8080  # Hostname for your SearXNG instance in Docker (default is the local instance)

# SearXNG Search Engines
SEARXNG_ENGINES=bing,google  # Search engines SearXNG will use for general searches (Bing and Google by default)
SEARXNG_IMAGES_ENGINES=bing,google  # Search engines SearXNG will use for image searches (Bing and Google by default)

# Search Result Configuration
REFERENCE_COUNT=8  # Number of search results to reference (default is 8)

# Cache Configuration
CACHE_ENABLE=1  # Enable caching: 1=Enable, 0=Disable (default is enabled)
