[{"platform": "<PERSON><PERSON><PERSON>", "type": "openai", "models": ["qwen-max", "qwen-max-0428", "qwen-turbo", "qwen-plus"]}, {"platform": "openai", "type": "openai", "models": ["internlm/internlm2_5-7b-chat"]}, {"platform": "baidu", "type": "baidu", "models": ["eb-instant", "completions_pro", "ernie_bot_8k"]}, {"platform": "google", "type": "gemini", "models": ["gemini-1.0-pro", "gemini-1.5-pro", "gemini-1.5-flash"]}, {"platform": "yi", "type": "openai", "models": ["yi-large", "yi-large-turbo", "yi-medium", "yi-spark"]}, {"platform": "moonshot", "type": "openai", "models": ["moonshot-v1-8k", "moonshot-v1-32k", "moonshot-v1-128k"]}, {"platform": "lepton", "type": "openai", "models": ["llama2-7b", "llama2-13b", "llama2-70b", "mixtral-8*7b", "mixtral-8*22b"]}, {"platform": "deepseek", "type": "openai", "models": ["deepseek-chat", "deepseek-coder"]}, {"platform": "chatglm", "type": "openai", "models": ["glm-4", "glm-4-plus", "glm-4-air", "glm-4-airx", "glm-4-flash"]}, {"platform": "tencent", "type": "tencent", "models": ["std", "pro"]}]