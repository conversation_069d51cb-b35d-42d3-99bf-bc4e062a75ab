main:
  push:
    - runner:
        tags: cnb:arch:amd64
      services:
        - docker
      env:
        IMAGE_TAG: ${CNB_DOCKER_REGISTRY}/${CNB_REPO_SLUG_LOWERCASE}:latest-linux-amd64
      stages:
        - name: docker login
          script: docker login -u ${CNB_TOKEN_USER_NAME} -p "${CNB_TOKEN}" ${CNB_DOCKER_REGISTRY}
        - name: docker build
          script: docker build -t ${IMAGE_TAG} -f ./.devops/Dockerfile .
        - name: docker push
          script: docker push ${IMAGE_TAG}
        - name: resolve
          type: cnb:resolve
          options:
            key: build-amd64

    - runner:
        tags: cnb:arch:arm64:v8
      services:
        - docker
      env:
        IMAGE_TAG: ${CNB_DOCKER_REGISTRY}/${CNB_REPO_SLUG_LOWERCASE}:latest-linux-arm64
      stages:
        - name: docker login
          script: docker login -u ${CNB_TOKEN_USER_NAME} -p "${CNB_TOKEN}" ${CNB_DOCKER_REGISTRY}
        - name: docker build
          script: docker build -t ${IMAGE_TAG} -f ./.devops/Dockerfile .
        - name: docker push
          script: docker push ${IMAGE_TAG}
        - name: resolve
          type: cnb:resolve
          options:
            key: build-arm64

    - services:
        - docker
      env:
        IMAGE_TAG: ${CNB_DOCKER_REGISTRY}/${CNB_REPO_SLUG_LOWERCASE}:latest
      stages:
        - name: await the amd64
          type: cnb:await
          options:
            key: build-amd64
        - name: await the arm64
          type: cnb:await
          options:
            key: build-arm64
        - name: manifest
          image: cnbcool/manifest
          settings:
            target: ${IMAGE_TAG}
            template: ${IMAGE_TAG}-OS-ARCH
            platforms:
              - linux/amd64
              - linux/arm64
