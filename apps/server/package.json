{"name": "server", "main": "./dist/app.js", "author": "zac_ma", "license": "MIT", "engines": {"node": ">=20"}, "scripts": {"start": "dotenvx run --env-file=.env -- node ./dist/app.js", "dev": "dotenvx run --env-file=.env.local --env-file=.env -- cross-env NODE_ENV=development nodemon ./src/app.ts", "check-types": "tsc --noEmit", "build": "tsc"}, "dependencies": {"@dotenvx/dotenvx": "^0.24.0", "@google/generative-ai": "^0.2.1", "@koa/bodyparser": "^5.1.0", "@koa/cors": "^5.0.0", "@koa/router": "^12.0.1", "@lmstudio/sdk": "^0.1.0", "cache-manager": "^5.4.0", "cheerio": "^1.0.0", "dotenv": "^16.4.5", "fetch-sse": "1.0.23", "koa": "^2.15.0", "koa-static": "^5.0.0", "koa2-connect-history-api-fallback": "^0.1.3", "ollama": "^0.5.9", "openai": "^4.61.1", "tencentcloud-sdk-nodejs": "^4.0.973", "winston": "^3.15.0"}, "devDependencies": {"@types/koa": "^2.15.0", "@types/koa-static": "^4.0.4", "@types/koa__cors": "^5.0.0", "@types/koa__router": "^12.0.4", "cross-env": "^7.0.3", "eslint": "^8.57.0", "eslint-config-standard": "^17.1.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-n": "^16.6.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^6.1.1", "nodemon": "^3.1.0", "ts-node": "^10.9.2", "typescript": "^5.3.3", "typescript-eslint": "^7.0.2"}}