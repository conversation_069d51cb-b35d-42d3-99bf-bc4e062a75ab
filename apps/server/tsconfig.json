{"compilerOptions": {"target": "ES2019", "module": "CommonJS", "experimentalDecorators": true, "emitDecoratorMetadata": true, "inlineSourceMap": true, "noImplicitThis": true, "noUnusedLocals": true, "stripInternal": true, "skipLibCheck": true, "pretty": true, "declaration": true, "forceConsistentCasingInFileNames": true, "outDir": "./dist", "rootDir": "./src", "resolveJsonModule": true, "strict": true, "esModuleInterop": true, "strictPropertyInitialization": false}, "include": ["./src/**/*"], "exclude": ["node_modules", "dist", "test"]}