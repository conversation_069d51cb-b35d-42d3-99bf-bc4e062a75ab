{"env": {"es6": true, "node": true}, "extends": ["eslint:recommended", "plugin:node/recommended"], "parserOptions": {"sourceType": "module"}, "ignorePatterns": ["dist/", ".yarn/", "node_modules/", "logs/"], "overrides": [{"files": ["./src/**/*.js", "./src/**/*.ts"], "extends": ["plugin:@typescript-eslint/recommended"], "parser": "@typescript-eslint/parser", "rules": {"@typescript-eslint/no-non-null-asserted-optional-chain": "off", "@typescript-eslint/no-non-null-assertion": "off", "@typescript-eslint/no-use-before-define": "off", "@typescript-eslint/no-warning-comments": "off", "@typescript-eslint/no-empty-function": "off", "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-var-requires": "off", "@typescript-eslint/explicit-function-return-type": "off", "@typescript-eslint/explicit-module-boundary-types": "off", "@typescript-eslint/camelcase": "off", "@typescript-eslint/interface-name-prefix": "off", "node/no-missing-import": "off", "node/no-empty-function": "off", "node/no-unsupported-features/es-syntax": "off", "node/no-missing-require": "off", "node/no-unpublished-import": "warn", "node/shebang": "off", "no-dupe-class-members": "off", "require-atomic-updates": "off"}}], "rules": {"block-scoped-var": "error", "eqeqeq": ["error", "always", {"null": "ignore"}], "no-var": "error", "semi": "error", "indent": ["error", 2, {"SwitchCase": 1}], "no-tabs": "error", "prefer-const": "error", "eol-last": "error", "prefer-arrow-callback": "error", "no-constant-condition": "off", "no-process-exit": "off", "no-trailing-spaces": "error", "quotes": ["warn", "single", {"avoidEscape": true}]}}