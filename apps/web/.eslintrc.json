{"root": true, "env": {"browser": true, "node": true, "es2021": true}, "extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended", "plugin:vue/vue3-recommended", "plugin:vue/essential", "plugin:tailwindcss/recommended", "prettier"], "overrides": [{"files": ["*.ts", "*.tsx", "*.vue"], "rules": {"@typescript-eslint/no-explicit-any": "off", "semi": "warn", "no-unused-vars": "off"}}], "parserOptions": {"parser": "@typescript-eslint/parser", "ecmaVersion": "latest", "sourceType": "module"}, "plugins": ["vue", "@typescript-eslint", "tailwindcss"]}