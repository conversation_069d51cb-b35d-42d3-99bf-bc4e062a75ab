{"name": "web", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview"}, "dependencies": {"@remixicon/vue": "^4.1.0", "cheerio": "^1.0.0", "fetch-sse": "1.0.23", "github-markdown-css": "^5.6.1", "marked": "^14.1.2", "pinia": "^2.2.2", "pinia-plugin-persistedstate": "^3.2.1", "tdesign-vue-next": "^1.10.1", "tencentcloud-sdk-nodejs": "^4.0.973", "vue": "^3.4.19", "vue-i18n": "^9.12.1", "vue-router": "^4.3.0"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^7.0.2", "@typescript-eslint/parser": "^7.0.2", "@vitejs/plugin-vue": "^5.0.4", "@vitejs/plugin-vue-jsx": "^3.1.0", "autoprefixer": "^10.4.17", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-tailwindcss": "^3.14.3", "eslint-plugin-vue": "^9.22.0", "postcss": "^8.4.35", "prettier": "^3.2.5", "prettier-plugin-tailwindcss": "^0.5.11", "tailwindcss": "^3.4.1", "typescript": "5.6.2", "unplugin-auto-import": "^0.17.5", "unplugin-vue-components": "^0.26.0", "vite": "^5.1.4", "vue-tsc": "^2.1.10"}}