/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    Answer: typeof import('./src/components/answer.vue')['default']
    Category: typeof import('./src/components/category.vue')['default']
    Engine: typeof import('./src/components/engine.vue')['default']
    Footer: typeof import('./src/components/footer.vue')['default']
    Language: typeof import('./src/components/language.vue')['default']
    LocalModels: typeof import('./src/components/localModels.vue')['default']
    LocalProviders: typeof import('./src/components/localProviders.vue')['default']
    Media: typeof import('./src/components/media.vue')['default']
    Mode: typeof import('./src/components/mode.vue')['default']
    Models: typeof import('./src/components/models.vue')['default']
    Related: typeof import('./src/components/related.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SearchInputBar: typeof import('./src/components/searchInputBar.vue')['default']
    Settings: typeof import('./src/components/settings.vue')['default']
    Sources: typeof import('./src/components/sources.vue')['default']
    TAlert: typeof import('tdesign-vue-next')['Alert']
    TButton: typeof import('tdesign-vue-next')['Button']
    TDivider: typeof import('tdesign-vue-next')['Divider']
    TDrawer: typeof import('tdesign-vue-next')['Drawer']
    TImage: typeof import('tdesign-vue-next')['Image']
    TImageViewer: typeof import('tdesign-vue-next')['ImageViewer']
    TInput: typeof import('tdesign-vue-next')['Input']
    TLoading: typeof import('tdesign-vue-next')['Loading']
    Toolbar: typeof import('./src/components/toolbar.vue')['default']
    TOption: typeof import('tdesign-vue-next')['Option']
    TPopup: typeof import('tdesign-vue-next')['Popup']
    TRadioButton: typeof import('tdesign-vue-next')['RadioButton']
    TRadioGroup: typeof import('tdesign-vue-next')['RadioGroup']
    TSelect: typeof import('tdesign-vue-next')['Select']
    TSkeleton: typeof import('tdesign-vue-next')['Skeleton']
    TSwitch: typeof import('tdesign-vue-next')['Switch']
    TTag: typeof import('tdesign-vue-next')['Tag']
    TTooltip: typeof import('tdesign-vue-next')['Tooltip']
  }
}
