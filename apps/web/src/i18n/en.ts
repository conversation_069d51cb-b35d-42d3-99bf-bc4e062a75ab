export default {
  title: 'Search with AI',
  theme: 'Theme',
  ask: 'Me',
  answer: 'Answer',
  related: 'Related',
  sources: 'Sources',
  media: 'Media',
  copy: 'Copy',
  reload: 'Reload',
  share: 'Share',
  search: 'Engine',
  newChat: 'New Q&A',
  sogou: 'Sogou',
  chatglm: 'ChatGLM',
  bing: 'Bing',
  google: 'Google',
  searxng: 'SearXNG',
  llm: 'LLM',
  chat: 'Continue Q&A',
  back: 'Back Home',
  language: 'Language',
  settings: 'Settings',
  selectModel: 'Select LLM',
  selectLanguage: 'Select Language',
  selectEngine: 'Select Search Engine',
  localModel: 'Local LLM',
  localProvider: 'Local Provider',
  selectProvider: 'Select Provider',
  provider: 'PROVIDER',
  enableLocalModel: 'Enable Local LLM',
  warning: 'Content is generated by AI and accuracy cannot be guaranteed',
  message: {
    queryError: 'Query error',
    copyError: 'Copy failed',
    noSelect: 'Not selected yet',
    sourceError: 'Failed to obtain information',
    shareSuccess: 'Sharing link copied to clipboard.',
    success: 'successful!'
  },
  category: {
    general: 'General',
    science: 'Science',
    images: 'Images',
    videos: 'Videos'
  },
  mode: {
    simple: 'Simple',
    deep: 'Deep',
    research: 'Research'
  },
  tips: {
    search: 'Ask a question.',
    continue: 'Continue to ask based on the current context.'
  },
  btn: {
    clear: 'Clear history'
  }
};
