<template>
  <div id="home" class="flex size-full items-center justify-center dark:bg-black">
    <div class="mt-36 flex w-full flex-col gap-4 p-4 sm:-mt-28 lg:max-w-3xl xl:max-w-4xl">
      <div class="flex items-center justify-center gap-2">
        <img :src="logoUrl" class="w-10" />
        <span class="text-3xl font-bold dark:text-gray-100">AI Search</span>
        <t-tag variant="light" class="text-xs text-gray-500">beta</t-tag>
      </div>
      <SearchInputBar :autofocus="true" :loading="false" @search="search" />
      <div class="my-2 flex flex-wrap items-center justify-center gap-4">
        <SearchMode />
        <SearCategory v-if="enableAdvanced" />
      </div>
      <div class="mt-4">
        <PageFooter />
      </div>
    </div>
  </div>
</template>

<script setup lang="tsx">
import router from '../router';
import { PageFooter, SearchInputBar, SearCategory, SearchMode } from '../components';
import logoUrl from '../assets/logo.png';
import { computed } from 'vue';
import { useAppStore } from '../store';

const appStore = useAppStore();

const enableAdvanced = computed(() => appStore.engine === 'SEARXNG');

const search = (val: string) => {
  if (!val) {
    return;
  }
  router.push({
    name: 'SearchPage',
    query: {
      q: val
    }
  });
};
</script>

<script lang="tsx">
export default {
  name: 'HomePage'
};
</script>
