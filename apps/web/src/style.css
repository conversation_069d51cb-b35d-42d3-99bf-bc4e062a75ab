@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  color-scheme: light dark;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  --td-brand-color: #000000;
  --td-brand-color-active: #111111;
  --td-brand-color-hover: #666666;
  --td-brand-color-focus: #333333;
  --main-bg-color: #ffffff;
  --main-font-color: #333333;
}

:root[theme-mode="light"] {
  --td-brand-color: #000000;
  --td-brand-color-active: #111111;
  --td-brand-color-hover: #666666;
  --td-brand-color-focus: #f5f5f5;
  --main-bg-color: #ffffff;
  --main-font-color: #333333;
}

:root[theme-mode="dark"] {
  --td-brand-color: #000000;
  --td-brand-color-active: #111111;
  --td-brand-color-hover: #666666;
  --td-brand-color-focus: #333333;
  --main-bg-color: #000000;
  --main-font-color: #cccccc;
}

html, body {
  width: 100%;
  height: 100%;
  padding: 0;
  margin: 0;
  background-color: var(--main-bg-color);
  scrollbar-color: #8e9099 transparent;
  
  overflow-x: hidden;
  overflow-y: auto;
}

html[theme-mode="dark"] {
  scrollbar-color: #8e9099 transparent;
}

html .markdown-body {
  color: var(--main-font-color);
  background-color: transparent;
  line-height: 26px;
  min-height: auto;
}

html .markdown-body pre {
  color: #666666;
  background-color: #f5f5f5;
}

html[theme-mode="dark"] .markdown-body {
  color: var(--main-font-color);
  background-color: transparent;
}

html[theme-mode="dark"] .markdown-body pre {
  color: #999999;
  background-color: #222222;
}

a {
  text-decoration: none;
}

#app {
  display: flex;
  justify-content: center;
  width: 100%;
  height: 100%;
}
