FROM node:20 AS build

WORKDIR /workspace

# 复制根目录的package.json和yarn.lock
COPY package.json yarn.lock turbo.json ./

# 复制apps目录
COPY apps ./apps

# RUN yarn config set registry https://mirrors.cloud.tencent.com/npm/

RUN apt-get update && apt-get install -y python3 make g++

# 启用 Corepack 并设置 yarn 使用 node_modules
RUN corepack enable && yarn config set nodeLinker node-modules

# 安装依赖
RUN yarn install

# 构建所有应用
RUN yarn build

FROM node:20-alpine AS production

WORKDIR /app

# 安装 curl，下载并安装 dotenvx，然后删除 curl
RUN apk add --no-cache curl && \
    curl -fsS https://dotenvx.sh/ | sh

RUN apk add --no-cache python3 make g++

COPY --from=build /workspace/apps/server/dist ./dist
COPY --from=build /workspace/apps/server/package.json ./
COPY --from=build /workspace/apps/web/dist ./web/dist

# RUN yarn config set registry https://mirrors.cloud.tencent.com/npm/
RUN yarn install --production --frozen-lockfile && \
  yarn cache clean && \
  apk del curl python3 make g++

EXPOSE 3000
CMD ["yarn", "run", "start"]
